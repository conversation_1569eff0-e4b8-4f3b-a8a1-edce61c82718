/**
 * 知识库服务单元测试
 */
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { KnowledgeBaseService } from './knowledge-base.service';
import { KnowledgeBase, KnowledgeBaseStatus } from './entities/knowledge-base.entity';
import { VectorStoreService } from '../vector-store/vector-store.service';
import { SearchService } from '../search/search.service';
import { CreateKnowledgeBaseDto } from './dto/create-knowledge-base.dto';

describe('KnowledgeBaseService', () => {
  let service: KnowledgeBaseService;
  let repository: Repository<KnowledgeBase>;
  let vectorStoreService: VectorStoreService;
  let searchService: SearchService;
  let eventEmitter: EventEmitter2;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findAndCount: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    remove: jest.fn(),
  };

  const mockVectorStoreService = {
    createCollection: jest.fn(),
    deleteCollection: jest.fn(),
    getVectorCount: jest.fn(),
  };

  const mockSearchService = {
    search: jest.fn(),
  };

  const mockEventEmitter = {
    emit: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KnowledgeBaseService,
        {
          provide: getRepositoryToken(KnowledgeBase),
          useValue: mockRepository,
        },
        {
          provide: VectorStoreService,
          useValue: mockVectorStoreService,
        },
        {
          provide: SearchService,
          useValue: mockSearchService,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
      ],
    }).compile();

    service = module.get<KnowledgeBaseService>(KnowledgeBaseService);
    repository = module.get<Repository<KnowledgeBase>>(getRepositoryToken(KnowledgeBase));
    vectorStoreService = module.get<VectorStoreService>(VectorStoreService);
    searchService = module.get<SearchService>(SearchService);
    eventEmitter = module.get<EventEmitter2>(EventEmitter2);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a knowledge base successfully', async () => {
      const createDto: CreateKnowledgeBaseDto = {
        name: '测试知识库',
        description: '这是一个测试知识库',
      };

      const ownerId = 'user_123';
      const mockKnowledgeBase = {
        id: 'kb_123',
        ...createDto,
        ownerId,
        status: KnowledgeBaseStatus.ACTIVE,
      };

      mockRepository.create.mockReturnValue(mockKnowledgeBase);
      mockRepository.save.mockResolvedValue(mockKnowledgeBase);
      mockVectorStoreService.createCollection.mockResolvedValue(undefined);

      const result = await service.create(ownerId, createDto);

      expect(repository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          ...createDto,
          ownerId,
          status: KnowledgeBaseStatus.ACTIVE,
        }),
      );
      expect(repository.save).toHaveBeenCalledWith(mockKnowledgeBase);
      expect(vectorStoreService.createCollection).toHaveBeenCalledWith(mockKnowledgeBase.id);
      expect(eventEmitter.emit).toHaveBeenCalledWith('knowledge-base.created', {
        knowledgeBaseId: mockKnowledgeBase.id,
        ownerId,
        name: mockKnowledgeBase.name,
      });
      expect(result).toEqual(mockKnowledgeBase);
    });
  });

  describe('findByOwner', () => {
    it('should return paginated knowledge bases for owner', async () => {
      const ownerId = 'user_123';
      const page = 1;
      const limit = 10;
      const mockKnowledgeBases = [
        { id: 'kb_1', name: '知识库1', ownerId },
        { id: 'kb_2', name: '知识库2', ownerId },
      ];
      const total = 2;

      mockRepository.findAndCount.mockResolvedValue([mockKnowledgeBases, total]);

      const result = await service.findByOwner(ownerId, page, limit);

      expect(repository.findAndCount).toHaveBeenCalledWith({
        where: { ownerId },
        relations: ['documents'],
        order: { createdAt: 'DESC' },
        skip: 0,
        take: 10,
      });
      expect(result).toEqual({
        data: mockKnowledgeBases,
        total,
        page,
        limit,
      });
    });
  });

  describe('findOne', () => {
    it('should return knowledge base if found and user has permission', async () => {
      const id = 'kb_123';
      const ownerId = 'user_123';
      const mockKnowledgeBase = {
        id,
        name: '测试知识库',
        ownerId,
      };

      mockRepository.findOne.mockResolvedValue(mockKnowledgeBase);

      const result = await service.findOne(id, ownerId);

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id },
        relations: ['documents'],
      });
      expect(result).toEqual(mockKnowledgeBase);
    });

    it('should throw NotFoundException if knowledge base not found', async () => {
      const id = 'kb_123';
      const ownerId = 'user_123';

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(id, ownerId)).rejects.toThrow('知识库不存在');
    });

    it('should throw ForbiddenException if user does not own knowledge base', async () => {
      const id = 'kb_123';
      const ownerId = 'user_123';
      const mockKnowledgeBase = {
        id,
        name: '测试知识库',
        ownerId: 'other_user',
      };

      mockRepository.findOne.mockResolvedValue(mockKnowledgeBase);

      await expect(service.findOne(id, ownerId)).rejects.toThrow('您没有权限访问此知识库');
    });
  });

  describe('search', () => {
    it('should perform search in knowledge base', async () => {
      const id = 'kb_123';
      const ownerId = 'user_123';
      const searchDto = {
        query: '测试查询',
        topK: 5,
        threshold: 0.7,
      };
      const mockKnowledgeBase = {
        id,
        name: '测试知识库',
        ownerId,
        status: KnowledgeBaseStatus.ACTIVE,
        config: { maxResults: 10, searchThreshold: 0.8 },
      };
      const mockSearchResults = [
        { id: 'result_1', content: '搜索结果1', score: 0.9 },
        { id: 'result_2', content: '搜索结果2', score: 0.8 },
      ];

      mockRepository.findOne.mockResolvedValue(mockKnowledgeBase);
      mockSearchService.search.mockResolvedValue(mockSearchResults);

      const result = await service.search(id, searchDto, ownerId);

      expect(searchService.search).toHaveBeenCalledWith(id, searchDto.query, {
        topK: searchDto.topK,
        threshold: searchDto.threshold,
        filter: undefined,
      });
      expect(eventEmitter.emit).toHaveBeenCalledWith('knowledge-base.searched', {
        knowledgeBaseId: id,
        query: searchDto.query,
        resultCount: mockSearchResults.length,
        ownerId,
      });
      expect(result).toEqual({
        query: searchDto.query,
        results: mockSearchResults,
        knowledgeBase: {
          id: mockKnowledgeBase.id,
          name: mockKnowledgeBase.name,
          description: mockKnowledgeBase.description,
        },
      });
    });
  });
});
