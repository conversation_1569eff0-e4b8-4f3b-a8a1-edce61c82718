import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DeviceManagementService } from './device-management.service';
import { DeviceManagementController } from './device-management.controller';
import { Device } from './entities/device.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Device])
  ],
  controllers: [DeviceManagementController],
  providers: [DeviceManagementService],
  exports: [DeviceManagementService]
})
export class DeviceManagementModule {}
