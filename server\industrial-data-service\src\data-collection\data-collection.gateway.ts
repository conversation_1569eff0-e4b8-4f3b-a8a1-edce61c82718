import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
  namespace: '/data-collection',
})
export class DataCollectionGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(DataCollectionGateway.name);
  private connectedClients = new Map<string, Socket>();

  handleConnection(client: Socket) {
    this.connectedClients.set(client.id, client);
    this.logger.log(`客户端连接: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.connectedClients.delete(client.id);
    this.logger.log(`客户端断开: ${client.id}`);
  }

  /**
   * 订阅设备数据
   */
  @SubscribeMessage('subscribe-device')
  handleSubscribeDevice(
    @MessageBody() data: { deviceId: string },
    @ConnectedSocket() client: Socket,
  ) {
    client.join(`device-${data.deviceId}`);
    this.logger.log(`客户端 ${client.id} 订阅设备 ${data.deviceId}`);
    
    return {
      event: 'subscribed',
      data: { deviceId: data.deviceId, message: '订阅成功' }
    };
  }

  /**
   * 取消订阅设备数据
   */
  @SubscribeMessage('unsubscribe-device')
  handleUnsubscribeDevice(
    @MessageBody() data: { deviceId: string },
    @ConnectedSocket() client: Socket,
  ) {
    client.leave(`device-${data.deviceId}`);
    this.logger.log(`客户端 ${client.id} 取消订阅设备 ${data.deviceId}`);
    
    return {
      event: 'unsubscribed',
      data: { deviceId: data.deviceId, message: '取消订阅成功' }
    };
  }

  /**
   * 广播数据点到订阅的客户端
   */
  broadcastDataPoints(dataPoints: any[]) {
    dataPoints.forEach(dataPoint => {
      this.server.to(`device-${dataPoint.deviceId}`).emit('data-point', dataPoint);
    });
  }

  /**
   * 广播设备状态变化
   */
  broadcastDeviceStatus(deviceId: string, status: any) {
    this.server.to(`device-${deviceId}`).emit('device-status', {
      deviceId,
      status,
      timestamp: new Date()
    });
  }

  /**
   * 广播系统告警
   */
  broadcastAlert(alert: any) {
    this.server.emit('system-alert', alert);
  }
}
