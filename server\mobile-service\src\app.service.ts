/**
 * 移动端服务主服务
 * 
 * 提供服务基本信息和状态功能
 */

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  constructor(private readonly configService: ConfigService) {}

  /**
   * 获取服务信息
   */
  getServiceInfo() {
    return {
      name: '移动端服务',
      description: '提供移动设备专用的数据同步和实时通信功能',
      version: '1.0.0',
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      port: this.configService.get<number>('PORT', 3009),
      timestamp: new Date().toISOString(),
      features: [
        '移动端数据同步',
        '实时WebSocket通信',
        '设备管理',
        '冲突解决',
        '离线支持',
        '性能优化'
      ],
      endpoints: {
        api: '/api',
        docs: '/api/docs',
        health: '/api/v1/health',
        sync: '/api/mobile/sync',
        devices: '/api/mobile/devices'
      }
    };
  }

  /**
   * 获取服务版本
   */
  getVersion() {
    return {
      version: '1.0.0',
      buildTime: new Date().toISOString(),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    };
  }
}
