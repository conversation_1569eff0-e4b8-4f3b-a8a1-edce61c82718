import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsObject, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { RelationType } from '../knowledge-graph.service';

export class CreateRelationDto {
  @ApiProperty({
    description: '关系类型',
    enum: RelationType,
    example: RelationType.CONTAINS,
  })
  @IsEnum(RelationType)
  type: RelationType;

  @ApiProperty({
    description: '源实体ID',
    example: 'entity_123456789_abc123',
  })
  @IsString()
  sourceId: string;

  @ApiProperty({
    description: '目标实体ID',
    example: 'entity_987654321_def456',
  })
  @IsString()
  targetId: string;

  @ApiProperty({
    description: '关系属性',
    example: {
      strength: 'strong',
      description: '主要组成部分',
    },
  })
  @IsObject()
  properties: Record<string, any> = {};

  @ApiProperty({
    description: '关系权重',
    minimum: 0,
    maximum: 1,
    example: 0.8,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  weight: number;

  @ApiProperty({
    description: '置信度',
    minimum: 0,
    maximum: 1,
    example: 0.9,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence: number;

  @ApiProperty({
    description: '数据来源',
    example: 'expert_knowledge',
  })
  @IsString()
  source: string;
}
