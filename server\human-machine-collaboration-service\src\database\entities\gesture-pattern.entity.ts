import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * 手势模式实体
 * 存储手势识别的模式和配置
 */
@Entity('gesture_patterns')
@Index(['patternName', 'isActive'])
@Index(['gestureType', 'difficulty'])
export class GesturePattern {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  patternName: string;

  @Column({
    type: 'enum',
    enum: ['static', 'dynamic', 'sequence', 'continuous'],
    default: 'static',
  })
  gestureType: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'json' })
  keyPoints: any[]; // 关键点坐标

  @Column({ type: 'json', nullable: true })
  sequence: any[]; // 动作序列

  @Column({ type: 'float', default: 0.8, comment: '识别阈值' })
  threshold: number;

  @Column({
    type: 'enum',
    enum: ['easy', 'medium', 'hard'],
    default: 'medium',
  })
  difficulty: string;

  @Column({ type: 'json', nullable: true })
  trainingData: any;

  @Column({ type: 'varchar', length: 100, nullable: true })
  associatedAction: string;

  @Column({ type: 'json', nullable: true })
  actionParameters: any;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'int', default: 0, comment: '使用次数' })
  usageCount: number;

  @Column({ type: 'float', default: 0, comment: '平均识别准确率' })
  averageAccuracy: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  createdBy: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  updatedBy: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
