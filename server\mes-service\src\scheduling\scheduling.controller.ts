import { Controller, Get, Post, Body, Query, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { SchedulingService } from './scheduling.service';

@ApiTags('scheduling')
@Controller('scheduling')
@ApiBearerAuth()
export class SchedulingController {
  private readonly logger = new Logger(SchedulingController.name);

  constructor(private readonly schedulingService: SchedulingService) {}

  @Post('generate')
  @ApiOperation({ summary: '生成调度方案' })
  async generateSchedule(@Body() scheduleDto: any) {
    return await this.schedulingService.generateSchedule(scheduleDto);
  }

  @Get()
  @ApiOperation({ summary: '获取调度列表' })
  async getSchedules(@Query() query: any) {
    return await this.schedulingService.getSchedules(query);
  }

  @Get('stats/overview')
  @ApiOperation({ summary: '获取调度统计' })
  async getSchedulingStatistics() {
    return await this.schedulingService.getSchedulingStatistics();
  }
}
