import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * 容量类型枚举
 */
export enum CapacityType {
  HOURLY = 'hourly',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  TOTAL = 'total',
}

/**
 * 资源容量实体
 */
@Entity('resource_capacities')
@Index(['resourceId', 'timeSlot'])
@Index(['capacityType'])
export class ResourceCapacity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'resource_id', type: 'uuid' })
  resourceId: string;

  @Column({
    type: 'enum',
    enum: CapacityType,
  })
  capacityType: CapacityType;

  @Column({ name: 'time_slot', type: 'datetime' })
  timeSlot: Date;

  @Column({ name: 'total_capacity', type: 'decimal', precision: 10, scale: 2 })
  totalCapacity: number;

  @Column({ name: 'available_capacity', type: 'decimal', precision: 10, scale: 2 })
  availableCapacity: number;

  @Column({ name: 'reserved_capacity', type: 'decimal', precision: 10, scale: 2, default: 0 })
  reservedCapacity: number;

  @Column({ name: 'utilization_rate', type: 'decimal', precision: 5, scale: 2, default: 0 })
  utilizationRate: number;

  @Column({ name: 'peak_demand', type: 'decimal', precision: 10, scale: 2, nullable: true })
  peakDemand: number;

  @Column({ name: 'average_demand', type: 'decimal', precision: 10, scale: 2, nullable: true })
  averageDemand: number;

  @Column({ type: 'json', nullable: true, comment: '容量约束' })
  constraints: any;

  @Column({ type: 'json', nullable: true, comment: '扩展属性' })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
