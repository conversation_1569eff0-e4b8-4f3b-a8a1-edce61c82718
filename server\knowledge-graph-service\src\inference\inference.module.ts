import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InferenceService } from './inference.service';
import { InferenceController } from './inference.controller';

// 实体
import { InferenceRuleModel } from '../knowledge/entities/inference-rule.entity';
import { KnowledgeEntityModel } from '../knowledge/entities/knowledge-entity.entity';
import { KnowledgeRelationModel } from '../knowledge/entities/knowledge-relation.entity';

@Module({
  imports: [
    ...(process.env.DB_HOST ? [
      TypeOrmModule.forFeature([
        InferenceRuleModel,
        KnowledgeEntityModel,
        KnowledgeRelationModel,
      ])
    ] : []),
  ],
  controllers: [InferenceController],
  providers: [InferenceService],
  exports: [InferenceService],
})
export class InferenceModule {}
