import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Resource } from './entities/resource.entity';
import { ResourceAllocation } from './entities/resource-allocation.entity';

/**
 * 资源监控服务
 */
@Injectable()
export class ResourceMonitoringService {
  private readonly logger = new Logger(ResourceMonitoringService.name);

  constructor(
    @InjectRepository(Resource)
    private readonly resourceRepository: Repository<Resource>,
    @InjectRepository(ResourceAllocation)
    private readonly allocationRepository: Repository<ResourceAllocation>,
  ) {}

  /**
   * 获取资源利用率
   */
  async getResourceUtilization(
    resourceId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<any> {
    try {
      const resource = await this.resourceRepository.findOne({
        where: { id: resourceId },
      });

      if (!resource) {
        throw new Error(`资源不存在: ${resourceId}`);
      }

      const start = startDate || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const end = endDate || new Date();

      const allocations = await this.allocationRepository.find({
        where: {
          resourceId,
        },
      });

      // 过滤时间范围内的分配
      const filteredAllocations = allocations.filter(allocation => 
        allocation.startTime >= start && allocation.endTime <= end
      );

      const totalPeriod = end.getTime() - start.getTime();
      const allocatedTime = filteredAllocations.reduce((sum, allocation) => {
        const duration = allocation.endTime.getTime() - allocation.startTime.getTime();
        return sum + duration;
      }, 0);

      const utilizationRate = totalPeriod > 0 ? allocatedTime / totalPeriod : 0;

      return {
        resourceId,
        resourceName: resource.name,
        period: { start, end },
        utilizationRate,
        allocatedTime: allocatedTime / (1000 * 60 * 60), // 转换为小时
        totalTime: totalPeriod / (1000 * 60 * 60), // 转换为小时
        allocationsCount: filteredAllocations.length,
        allocations: filteredAllocations.map(allocation => ({
          id: allocation.id,
          taskId: allocation.taskId,
          startTime: allocation.startTime,
          endTime: allocation.endTime,
          quantity: allocation.allocatedQuantity,
          status: allocation.status,
        })),
      };
    } catch (error) {
      this.logger.error(`获取资源利用率失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取监控统计
   */
  async getMonitoringStats(): Promise<any> {
    try {
      const totalResources = await this.resourceRepository.count();
      const activeAllocations = await this.allocationRepository.count({
        where: { status: 'active' as any },
      });

      const resources = await this.resourceRepository.find();
      const resourcesByType = resources.reduce((acc, resource) => {
        acc[resource.type] = (acc[resource.type] || 0) + 1;
        return acc;
      }, {});

      const resourcesByStatus = resources.reduce((acc, resource) => {
        acc[resource.status] = (acc[resource.status] || 0) + 1;
        return acc;
      }, {});

      // 计算平均利用率
      let totalUtilization = 0;
      let utilizationCount = 0;

      for (const resource of resources) {
        try {
          const utilization = await this.getResourceUtilization(resource.id);
          totalUtilization += utilization.utilizationRate;
          utilizationCount++;
        } catch (error) {
          // 忽略单个资源的错误
        }
      }

      const averageUtilization = utilizationCount > 0 ? totalUtilization / utilizationCount : 0;

      return {
        overview: {
          totalResources,
          activeAllocations,
          averageUtilization,
        },
        distribution: {
          byType: resourcesByType,
          byStatus: resourcesByStatus,
        },
        performance: {
          highUtilizationResources: await this.getHighUtilizationResources(),
          lowUtilizationResources: await this.getLowUtilizationResources(),
          bottleneckResources: await this.getBottleneckResources(),
        },
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`获取监控统计失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取高利用率资源
   */
  private async getHighUtilizationResources(): Promise<any[]> {
    const resources = await this.resourceRepository.find();
    const highUtilizationResources = [];

    for (const resource of resources) {
      try {
        const utilization = await this.getResourceUtilization(resource.id);
        if (utilization.utilizationRate > 0.8) {
          highUtilizationResources.push({
            resourceId: resource.id,
            resourceName: resource.name,
            type: resource.type,
            utilizationRate: utilization.utilizationRate,
          });
        }
      } catch (error) {
        // 忽略错误
      }
    }

    return highUtilizationResources.sort((a, b) => b.utilizationRate - a.utilizationRate);
  }

  /**
   * 获取低利用率资源
   */
  private async getLowUtilizationResources(): Promise<any[]> {
    const resources = await this.resourceRepository.find();
    const lowUtilizationResources = [];

    for (const resource of resources) {
      try {
        const utilization = await this.getResourceUtilization(resource.id);
        if (utilization.utilizationRate < 0.3) {
          lowUtilizationResources.push({
            resourceId: resource.id,
            resourceName: resource.name,
            type: resource.type,
            utilizationRate: utilization.utilizationRate,
          });
        }
      } catch (error) {
        // 忽略错误
      }
    }

    return lowUtilizationResources.sort((a, b) => a.utilizationRate - b.utilizationRate);
  }

  /**
   * 获取瓶颈资源
   */
  private async getBottleneckResources(): Promise<any[]> {
    const resources = await this.resourceRepository.find();
    const bottleneckResources = [];

    for (const resource of resources) {
      try {
        const utilization = await this.getResourceUtilization(resource.id);
        if (utilization.utilizationRate > 0.9) {
          bottleneckResources.push({
            resourceId: resource.id,
            resourceName: resource.name,
            type: resource.type,
            utilizationRate: utilization.utilizationRate,
            severity: utilization.utilizationRate > 0.95 ? 'critical' : 'high',
          });
        }
      } catch (error) {
        // 忽略错误
      }
    }

    return bottleneckResources.sort((a, b) => b.utilizationRate - a.utilizationRate);
  }
}
