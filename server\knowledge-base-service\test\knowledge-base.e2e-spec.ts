/**
 * 知识库服务E2E测试
 */
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { KnowledgeBase } from '../src/knowledge-base/entities/knowledge-base.entity';

describe('KnowledgeBaseController (e2e)', () => {
  let app: INestApplication;
  let knowledgeBaseRepository: Repository<KnowledgeBase>;

  // 模拟JWT令牌
  const mockJwtToken = 'Bearer mock-jwt-token';
  const mockUser = { id: 'user_123', email: '<EMAIL>' };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider('USER_SERVICE')
      .useValue({
        send: jest.fn().mockResolvedValue(mockUser),
      })
      .compile();

    app = moduleFixture.createNestApplication();
    knowledgeBaseRepository = moduleFixture.get<Repository<KnowledgeBase>>(
      getRepositoryToken(KnowledgeBase),
    );

    await app.init();
  });

  afterEach(async () => {
    // 清理测试数据
    await knowledgeBaseRepository.clear();
    await app.close();
  });

  describe('/api/v1/knowledge-base (POST)', () => {
    it('should create a knowledge base', () => {
      const createDto = {
        name: '测试知识库',
        description: '这是一个E2E测试知识库',
      };

      return request(app.getHttpServer())
        .post('/api/v1/knowledge-base')
        .set('Authorization', mockJwtToken)
        .send(createDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.data).toHaveProperty('id');
          expect(res.body.data.name).toBe(createDto.name);
          expect(res.body.data.description).toBe(createDto.description);
          expect(res.body.data.ownerId).toBe(mockUser.id);
        });
    });

    it('should return 400 for invalid data', () => {
      const invalidDto = {
        // 缺少必需的name字段
        description: '无效的知识库',
      };

      return request(app.getHttpServer())
        .post('/api/v1/knowledge-base')
        .set('Authorization', mockJwtToken)
        .send(invalidDto)
        .expect(400);
    });

    it('should return 401 without authorization', () => {
      const createDto = {
        name: '测试知识库',
        description: '这是一个测试知识库',
      };

      return request(app.getHttpServer())
        .post('/api/v1/knowledge-base')
        .send(createDto)
        .expect(401);
    });
  });

  describe('/api/v1/knowledge-base (GET)', () => {
    beforeEach(async () => {
      // 创建测试数据
      const knowledgeBase1 = knowledgeBaseRepository.create({
        name: '知识库1',
        description: '测试知识库1',
        ownerId: mockUser.id,
      });
      const knowledgeBase2 = knowledgeBaseRepository.create({
        name: '知识库2',
        description: '测试知识库2',
        ownerId: mockUser.id,
      });
      await knowledgeBaseRepository.save([knowledgeBase1, knowledgeBase2]);
    });

    it('should return paginated knowledge bases', () => {
      return request(app.getHttpServer())
        .get('/api/v1/knowledge-base?page=1&limit=10')
        .set('Authorization', mockJwtToken)
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveProperty('data');
          expect(res.body.data).toHaveProperty('total');
          expect(res.body.data).toHaveProperty('page');
          expect(res.body.data).toHaveProperty('limit');
          expect(Array.isArray(res.body.data.data)).toBe(true);
          expect(res.body.data.total).toBeGreaterThanOrEqual(2);
        });
    });
  });

  describe('/api/v1/knowledge-base/:id (GET)', () => {
    let knowledgeBaseId: string;

    beforeEach(async () => {
      const knowledgeBase = knowledgeBaseRepository.create({
        name: '测试知识库',
        description: '这是一个测试知识库',
        ownerId: mockUser.id,
      });
      const saved = await knowledgeBaseRepository.save(knowledgeBase);
      knowledgeBaseId = saved.id;
    });

    it('should return knowledge base details', () => {
      return request(app.getHttpServer())
        .get(`/api/v1/knowledge-base/${knowledgeBaseId}`)
        .set('Authorization', mockJwtToken)
        .expect(200)
        .expect((res) => {
          expect(res.body.data.id).toBe(knowledgeBaseId);
          expect(res.body.data.name).toBe('测试知识库');
          expect(res.body.data.ownerId).toBe(mockUser.id);
        });
    });

    it('should return 404 for non-existent knowledge base', () => {
      return request(app.getHttpServer())
        .get('/api/v1/knowledge-base/non-existent-id')
        .set('Authorization', mockJwtToken)
        .expect(404);
    });
  });

  describe('/api/v1/knowledge-base/:id/search (POST)', () => {
    let knowledgeBaseId: string;

    beforeEach(async () => {
      const knowledgeBase = knowledgeBaseRepository.create({
        name: '测试知识库',
        description: '这是一个测试知识库',
        ownerId: mockUser.id,
      });
      const saved = await knowledgeBaseRepository.save(knowledgeBase);
      knowledgeBaseId = saved.id;
    });

    it('should perform search in knowledge base', () => {
      const searchDto = {
        query: '测试查询',
        topK: 5,
        threshold: 0.7,
      };

      return request(app.getHttpServer())
        .post(`/api/v1/knowledge-base/${knowledgeBaseId}/search`)
        .set('Authorization', mockJwtToken)
        .send(searchDto)
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveProperty('query');
          expect(res.body.data).toHaveProperty('results');
          expect(res.body.data).toHaveProperty('knowledgeBase');
          expect(res.body.data.query).toBe(searchDto.query);
          expect(Array.isArray(res.body.data.results)).toBe(true);
        });
    });
  });

  describe('/health (GET)', () => {
    it('should return health status', () => {
      return request(app.getHttpServer())
        .get('/health')
        .expect(200)
        .expect((res) => {
          expect(res.body.status).toBe('ok');
          expect(res.body.service).toBe('knowledge-base-service');
        });
    });
  });
});
