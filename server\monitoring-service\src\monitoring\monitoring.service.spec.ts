import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MonitoringService } from './monitoring.service';
import { MetricsEntity } from './entities/metrics.entity';
import { ServiceMetricsEntity } from './entities/service-metrics.entity';
import { SystemMetricsEntity } from './entities/system-metrics.entity';
import { MetricsCollectorService } from './metrics-collector.service';
import { MetricsAggregatorService } from './metrics-aggregator.service';
import { MetricsStorageService } from './metrics-storage.service';

describe('MonitoringService', () => {
  let service: MonitoringService;
  let metricsRepository: Repository<MetricsEntity>;
  let serviceMetricsRepository: Repository<ServiceMetricsEntity>;
  let systemMetricsRepository: Repository<SystemMetricsEntity>;
  let metricsCollectorService: MetricsCollectorService;
  let metricsAggregatorService: MetricsAggregatorService;
  let metricsStorageService: MetricsStorageService;
  let configService: ConfigService;
  let eventEmitter: EventEmitter2;

  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getOne: jest.fn(),
    })),
  };

  const mockMetricsCollectorService = {
    collectSystemMetrics: jest.fn(),
    collectServiceMetrics: jest.fn(),
    collectCustomMetrics: jest.fn(),
  };

  const mockMetricsAggregatorService = {
    aggregateMetrics: jest.fn(),
    calculateAverages: jest.fn(),
    generateSummary: jest.fn(),
  };

  const mockMetricsStorageService = {
    storeMetrics: jest.fn(),
    storeSystemMetrics: jest.fn(),
    storeServiceMetrics: jest.fn(),
    cleanupOldMetrics: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn((key: string, defaultValue?: any) => {
      const config = {
        METRICS_RETENTION_DAYS: 30,
        METRICS_AGGREGATION_MINUTES: 5,
        METRICS_COLLECTION_INTERVAL_SECONDS: 60,
      };
      return config[key] || defaultValue;
    }),
  };

  const mockEventEmitter = {
    emit: jest.fn(),
    on: jest.fn(),
    removeListener: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MonitoringService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
        {
          provide: MetricsCollectorService,
          useValue: mockMetricsCollectorService,
        },
        {
          provide: MetricsAggregatorService,
          useValue: mockMetricsAggregatorService,
        },
        {
          provide: MetricsStorageService,
          useValue: mockMetricsStorageService,
        },
        {
          provide: getRepositoryToken(MetricsEntity),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(ServiceMetricsEntity),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(SystemMetricsEntity),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<MonitoringService>(MonitoringService);
    metricsRepository = module.get<Repository<MetricsEntity>>(
      getRepositoryToken(MetricsEntity),
    );
    serviceMetricsRepository = module.get<Repository<ServiceMetricsEntity>>(
      getRepositoryToken(ServiceMetricsEntity),
    );
    systemMetricsRepository = module.get<Repository<SystemMetricsEntity>>(
      getRepositoryToken(SystemMetricsEntity),
    );
    metricsCollectorService = module.get<MetricsCollectorService>(
      MetricsCollectorService,
    );
    metricsAggregatorService = module.get<MetricsAggregatorService>(
      MetricsAggregatorService,
    );
    metricsStorageService = module.get<MetricsStorageService>(
      MetricsStorageService,
    );
    configService = module.get<ConfigService>(ConfigService);
    eventEmitter = module.get<EventEmitter2>(EventEmitter2);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('应该被正确定义', () => {
    expect(service).toBeDefined();
  });

  describe('onModuleInit', () => {
    it('应该在模块初始化时收集指标', async () => {
      const collectAllMetricsSpy = jest.spyOn(service, 'collectAllMetrics');
      collectAllMetricsSpy.mockResolvedValue(undefined);

      await service.onModuleInit();

      expect(collectAllMetricsSpy).toHaveBeenCalled();
    });
  });

  describe('collectAllMetrics', () => {
    it('应该收集所有类型的指标', async () => {
      const mockSystemMetrics = {
        cpuUsage: 50,
        memoryUsage: 60,
        diskUsage: 70,
        networkUsage: 40,
      };

      const mockServiceMetrics = [
        {
          serviceName: 'user-service',
          status: 'healthy',
          responseTime: 100,
          errorRate: 0.1,
        },
      ];

      mockMetricsCollectorService.collectSystemMetrics.mockResolvedValue(
        mockSystemMetrics,
      );
      mockMetricsCollectorService.collectServiceMetrics.mockResolvedValue(
        mockServiceMetrics,
      );

      await service.collectAllMetrics();

      expect(mockMetricsCollectorService.collectSystemMetrics).toHaveBeenCalled();
      expect(mockMetricsCollectorService.collectServiceMetrics).toHaveBeenCalled();
      expect(mockMetricsStorageService.storeSystemMetrics).toHaveBeenCalledWith(
        mockSystemMetrics,
      );
      expect(mockMetricsStorageService.storeServiceMetrics).toHaveBeenCalledWith(
        mockServiceMetrics,
      );
    });

    it('应该在收集指标失败时记录错误', async () => {
      const error = new Error('收集指标失败');
      mockMetricsCollectorService.collectSystemMetrics.mockRejectedValue(error);

      const loggerSpy = jest.spyOn(service['logger'], 'error');

      await service.collectAllMetrics();

      expect(loggerSpy).toHaveBeenCalledWith(
        '收集指标失败:',
        error.stack,
      );
    });
  });

  describe('getMetrics', () => {
    it('应该返回指定时间范围内的指标', async () => {
      const startTime = new Date('2023-01-01');
      const endTime = new Date('2023-01-02');
      const mockMetrics = [
        {
          id: 1,
          metricName: 'cpu_usage',
          value: 50,
          timestamp: new Date(),
        },
      ];

      mockRepository.createQueryBuilder().getMany.mockResolvedValue(mockMetrics);

      const result = await service.getMetrics(startTime, endTime);

      expect(result).toEqual(mockMetrics);
      expect(mockRepository.createQueryBuilder).toHaveBeenCalled();
    });

    it('应该使用默认时间范围', async () => {
      const mockMetrics = [];
      mockRepository.createQueryBuilder().getMany.mockResolvedValue(mockMetrics);

      const result = await service.getMetrics();

      expect(result).toEqual(mockMetrics);
      expect(mockRepository.createQueryBuilder).toHaveBeenCalled();
    });
  });

  describe('getSystemMetrics', () => {
    it('应该返回系统指标', async () => {
      const mockSystemMetrics = [
        {
          id: 1,
          cpuUsage: 50,
          memoryUsage: 60,
          diskUsage: 70,
          networkUsage: 40,
          timestamp: new Date(),
        },
      ];

      mockRepository.find.mockResolvedValue(mockSystemMetrics);

      const result = await service.getSystemMetrics();

      expect(result).toEqual(mockSystemMetrics);
      expect(mockRepository.find).toHaveBeenCalled();
    });
  });

  describe('getServiceMetrics', () => {
    it('应该返回指定服务的指标', async () => {
      const serviceName = 'user-service';
      const mockServiceMetrics = [
        {
          id: 1,
          serviceName,
          status: 'healthy',
          responseTime: 100,
          errorRate: 0.1,
          timestamp: new Date(),
        },
      ];

      mockRepository.find.mockResolvedValue(mockServiceMetrics);

      const result = await service.getServiceMetrics(serviceName);

      expect(result).toEqual(mockServiceMetrics);
      expect(mockRepository.find).toHaveBeenCalledWith({
        where: { serviceName },
        order: { timestamp: 'DESC' },
        take: 100,
      });
    });

    it('应该返回所有服务的指标', async () => {
      const mockServiceMetrics = [
        {
          id: 1,
          serviceName: 'user-service',
          status: 'healthy',
          responseTime: 100,
          errorRate: 0.1,
          timestamp: new Date(),
        },
      ];

      mockRepository.find.mockResolvedValue(mockServiceMetrics);

      const result = await service.getServiceMetrics();

      expect(result).toEqual(mockServiceMetrics);
      expect(mockRepository.find).toHaveBeenCalledWith({
        order: { timestamp: 'DESC' },
        take: 100,
      });
    });
  });

  describe('aggregateMetrics', () => {
    it('应该聚合指标数据', async () => {
      const startTime = new Date('2023-01-01');
      const endTime = new Date('2023-01-02');
      const interval = '1h';
      const mockAggregatedData = {
        averageCpuUsage: 50,
        averageMemoryUsage: 60,
        maxResponseTime: 200,
        totalRequests: 1000,
      };

      mockMetricsAggregatorService.aggregateMetrics.mockResolvedValue(
        mockAggregatedData,
      );

      const result = await service.aggregateMetrics(startTime, endTime, interval);

      expect(result).toEqual(mockAggregatedData);
      expect(mockMetricsAggregatorService.aggregateMetrics).toHaveBeenCalledWith(
        startTime,
        endTime,
        interval,
      );
    });
  });

  describe('cleanupOldMetrics', () => {
    it('应该清理过期的指标数据', async () => {
      await service.cleanupOldMetrics();

      expect(mockMetricsStorageService.cleanupOldMetrics).toHaveBeenCalled();
    });
  });
});
