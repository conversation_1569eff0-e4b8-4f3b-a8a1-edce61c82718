import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { ScheduledTask } from './scheduled-task.entity';
import { TaskConstraint } from './task-constraint.entity';

/**
 * 任务状态枚举
 */
export enum TaskStatus {
  PENDING = 'pending',
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  DELAYED = 'delayed',
}

/**
 * 任务优先级枚举
 */
export enum TaskPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  URGENT = 4,
  CRITICAL = 5,
}

/**
 * 资源类型枚举
 */
export enum ResourceType {
  MACHINE = 'machine',
  WORKER = 'worker',
  MATERIAL = 'material',
  TOOL = 'tool',
  ENERGY = 'energy',
  SPACE = 'space',
  TRANSPORT = 'transport',
}

/**
 * 生产任务实体
 */
@Entity('production_tasks')
@Index(['status', 'priority'])
@Index(['dueDate'])
@Index(['orderId'])
export class ProductionTask {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'task_id', length: 100, unique: true })
  taskId: string;

  @Column({ name: 'order_id', length: 100 })
  @Index()
  orderId: string;

  @Column({ name: 'product_id', length: 100 })
  productId: string;

  @Column({ length: 200 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'int' })
  quantity: number;

  @Column({
    type: 'enum',
    enum: TaskPriority,
    default: TaskPriority.NORMAL,
  })
  priority: TaskPriority;

  @Column({
    type: 'enum',
    enum: TaskStatus,
    default: TaskStatus.PENDING,
  })
  status: TaskStatus;

  @Column({ name: 'due_date', type: 'datetime' })
  dueDate: Date;

  @Column({ name: 'estimated_duration', type: 'int', comment: '预估时长(分钟)' })
  estimatedDuration: number;

  @Column({ name: 'actual_duration', type: 'int', nullable: true, comment: '实际时长(分钟)' })
  actualDuration: number;

  @Column({ name: 'required_resources', type: 'json', comment: '所需资源' })
  requiredResources: {
    resourceType: ResourceType;
    resourceId: string;
    quantity: number;
    duration: number;
    alternatives?: string[];
  }[];

  @Column({ type: 'json', nullable: true, comment: '任务依赖' })
  dependencies: string[];

  @Column({ name: 'skill_requirements', type: 'json', nullable: true, comment: '技能要求' })
  skillRequirements: string[];

  @Column({ name: 'setup_time', type: 'int', default: 0, comment: '准备时间(分钟)' })
  setupTime: number;

  @Column({ name: 'teardown_time', type: 'int', default: 0, comment: '清理时间(分钟)' })
  teardownTime: number;

  @Column({ name: 'batch_size', type: 'int', default: 1, comment: '批次大小' })
  batchSize: number;

  @Column({ name: 'quality_requirements', type: 'json', nullable: true, comment: '质量要求' })
  qualityRequirements: any;

  @Column({ name: 'cost_estimate', type: 'decimal', precision: 10, scale: 2, nullable: true })
  costEstimate: number;

  @Column({ name: 'energy_estimate', type: 'decimal', precision: 10, scale: 2, nullable: true })
  energyEstimate: number;

  @Column({ type: 'json', nullable: true, comment: '扩展属性' })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => ScheduledTask, scheduledTask => scheduledTask.productionTask)
  scheduledTasks: ScheduledTask[];

  @OneToMany(() => TaskConstraint, constraint => constraint.task)
  constraints: TaskConstraint[];
}
