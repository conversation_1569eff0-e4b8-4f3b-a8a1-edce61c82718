/**
 * 文档控制器
 */
import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Body,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { DocumentsService } from './documents.service';
import { Document } from './entities/document.entity';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { CurrentUser } from '../common/decorators/current-user.decorator';

@ApiTags('documents')
@Controller('documents')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  @Post('upload/:knowledgeBaseId')
  @ApiOperation({ summary: '上传文档到知识库' })
  @ApiParam({ name: 'knowledgeBaseId', description: '知识库ID' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        metadata: {
          type: 'object',
          description: '文档元数据',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '文档上传成功',
    type: Document,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '文件格式不支持或上传失败',
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadDocument(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body('metadata') metadata?: string,
  ): Promise<Document> {
    if (!file) {
      throw new BadRequestException('请选择要上传的文件');
    }

    let parsedMetadata = {};
    if (metadata) {
      try {
        parsedMetadata = JSON.parse(metadata);
      } catch (error) {
        throw new BadRequestException('元数据格式错误');
      }
    }

    return this.documentsService.uploadDocument(knowledgeBaseId, file, parsedMetadata);
  }

  @Get('knowledge-base/:knowledgeBaseId')
  @ApiOperation({ summary: '获取知识库的文档列表' })
  @ApiParam({ name: 'knowledgeBaseId', description: '知识库ID' })
  @ApiQuery({ name: 'page', required: false, description: '页码', example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量', example: 10 })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        data: { type: 'array', items: { $ref: '#/components/schemas/Document' } },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
      },
    },
  })
  async getDocuments(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    return this.documentsService.findByKnowledgeBase(knowledgeBaseId, page, limit);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取文档详情' })
  @ApiParam({ name: 'id', description: '文档ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    type: Document,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '文档不存在',
  })
  async getDocument(@Param('id') id: string): Promise<Document> {
    return this.documentsService.findOne(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除文档' })
  @ApiParam({ name: 'id', description: '文档ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: '删除成功',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '文档不存在',
  })
  async deleteDocument(@Param('id') id: string): Promise<void> {
    return this.documentsService.remove(id);
  }

  @Post(':id/reprocess')
  @ApiOperation({ summary: '重新处理文档' })
  @ApiParam({ name: 'id', description: '文档ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '重新处理任务已提交',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '文档不存在',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '文档正在处理中',
  })
  async reprocessDocument(@Param('id') id: string): Promise<{ message: string }> {
    await this.documentsService.reprocessDocument(id);
    return { message: '重新处理任务已提交' };
  }

  @Get('knowledge-base/:knowledgeBaseId/statistics')
  @ApiOperation({ summary: '获取知识库文档处理统计' })
  @ApiParam({ name: 'knowledgeBaseId', description: '知识库ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number', description: '总文档数' },
        completed: { type: 'number', description: '已完成处理' },
        processing: { type: 'number', description: '处理中' },
        failed: { type: 'number', description: '处理失败' },
        totalSize: { type: 'number', description: '总文件大小' },
        totalChunks: { type: 'number', description: '总文档块数' },
        totalVectors: { type: 'number', description: '总向量数' },
      },
    },
  })
  async getProcessingStatistics(@Param('knowledgeBaseId') knowledgeBaseId: string) {
    return this.documentsService.getProcessingStatistics(knowledgeBaseId);
  }
}
