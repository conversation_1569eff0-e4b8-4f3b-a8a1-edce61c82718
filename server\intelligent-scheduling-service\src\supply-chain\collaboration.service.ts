import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions } from 'typeorm';
import { CollaborationPlan, CollaborationType, PlanStatus } from './entities/collaboration-plan.entity';

/**
 * 协同服务
 */
@Injectable()
export class CollaborationService {
  private readonly logger = new Logger(CollaborationService.name);

  constructor(
    @InjectRepository(CollaborationPlan)
    private readonly planRepository: Repository<CollaborationPlan>,
  ) {}

  /**
   * 创建协同计划
   */
  async createCollaborationPlan(request: any): Promise<CollaborationPlan> {
    try {
      this.logger.log('创建供应链协同计划');

      const {
        name,
        type = CollaborationType.DEMAND_PLANNING,
        participatingNodes = [],
        objectives = [],
        timeframe = { months: 6 },
      } = request;

      const plan = this.planRepository.create({
        planId: `COLLAB_${Date.now()}`,
        name,
        description: `${name} - 协同计划`,
        type,
        status: PlanStatus.DRAFT,
        startDate: new Date(),
        endDate: new Date(Date.now() + timeframe.months * 30 * 24 * 60 * 60 * 1000),
        participatingNodes: participatingNodes.map(node => ({
          nodeId: node.nodeId || node,
          nodeName: node.nodeName || `节点_${node.nodeId || node}`,
          role: node.role || 'participant',
          responsibilities: node.responsibilities || [],
          commitments: node.commitments || {},
        })),
        objectives: objectives.map(obj => ({
          objectiveType: obj.type || 'general',
          description: obj.description || obj,
          targetValue: obj.targetValue || 0,
          currentValue: obj.currentValue || 0,
          unit: obj.unit || '',
          priority: obj.priority || 'medium',
        })),
        collaborationActivities: this.generateCollaborationActivities(type, participatingNodes),
        performanceMetrics: this.generatePerformanceMetrics(objectives),
        riskManagement: this.generateRiskManagement(type),
        governanceStructure: this.generateGovernanceStructure(participatingNodes),
      });

      const savedPlan = await this.planRepository.save(plan);
      this.logger.log(`协同计划创建成功: ${savedPlan.planId}`);

      return savedPlan;
    } catch (error) {
      this.logger.error(`创建协同计划失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取协同计划列表
   */
  async getCollaborationPlans(options: {
    status?: string;
    type?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ plans: CollaborationPlan[]; total: number }> {
    try {
      const { status, type, limit = 20, offset = 0 } = options;

      const queryOptions: FindManyOptions<CollaborationPlan> = {
        take: limit,
        skip: offset,
        order: { createdAt: 'DESC' },
      };

      if (status || type) {
        queryOptions.where = {};
        if (status) {
          queryOptions.where.status = status as PlanStatus;
        }
        if (type) {
          queryOptions.where.type = type as CollaborationType;
        }
      }

      const [plans, total] = await this.planRepository.findAndCount(queryOptions);

      return { plans, total };
    } catch (error) {
      this.logger.error(`获取协同计划列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据ID获取协同计划
   */
  async getCollaborationPlanById(id: string): Promise<CollaborationPlan> {
    try {
      const plan = await this.planRepository.findOne({
        where: { id },
      });

      if (!plan) {
        throw new NotFoundException(`协同计划不存在: ${id}`);
      }

      return plan;
    } catch (error) {
      this.logger.error(`获取协同计划失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 生成协同活动
   */
  private generateCollaborationActivities(type: CollaborationType, nodes: any[]): any[] {
    const activities = [];

    switch (type) {
      case CollaborationType.DEMAND_PLANNING:
        activities.push(
          {
            activityId: 'DP_001',
            activityName: '需求信息共享',
            description: '定期共享需求预测和实际需求数据',
            responsibleNodes: nodes.slice(0, 2).map(n => n.nodeId || n),
            startDate: new Date(),
            endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            status: 'planned',
            deliverables: ['需求预测报告', '历史需求数据'],
          },
          {
            activityId: 'DP_002',
            activityName: '联合需求规划',
            description: '共同制定需求计划和库存策略',
            responsibleNodes: nodes.map(n => n.nodeId || n),
            startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            endDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
            status: 'planned',
            deliverables: ['联合需求计划', '库存策略'],
          }
        );
        break;

      case CollaborationType.INVENTORY_OPTIMIZATION:
        activities.push(
          {
            activityId: 'IO_001',
            activityName: '库存数据整合',
            description: '整合各节点库存数据，建立统一视图',
            responsibleNodes: nodes.map(n => n.nodeId || n),
            startDate: new Date(),
            endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
            status: 'planned',
            deliverables: ['库存数据报告', '数据质量评估'],
          }
        );
        break;

      default:
        activities.push({
          activityId: 'GEN_001',
          activityName: '协同启动会议',
          description: '召开协同计划启动会议，明确目标和责任',
          responsibleNodes: nodes.map(n => n.nodeId || n),
          startDate: new Date(),
          endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          status: 'planned',
          deliverables: ['会议纪要', '行动计划'],
        });
    }

    return activities;
  }

  /**
   * 生成绩效指标
   */
  private generatePerformanceMetrics(objectives: any[]): any[] {
    return [
      {
        metricName: 'collaboration_effectiveness',
        targetValue: 0.85,
        currentValue: 0,
        unit: 'score',
        measurementFrequency: 'monthly',
        responsibleNode: 'lead_node',
      },
      {
        metricName: 'cost_savings',
        targetValue: 100000,
        currentValue: 0,
        unit: 'yuan',
        measurementFrequency: 'quarterly',
        responsibleNode: 'finance_node',
      },
      {
        metricName: 'response_time',
        targetValue: 24,
        currentValue: 72,
        unit: 'hours',
        measurementFrequency: 'weekly',
        responsibleNode: 'operations_node',
      },
    ];
  }

  /**
   * 生成风险管理
   */
  private generateRiskManagement(type: CollaborationType): any[] {
    return [
      {
        riskType: 'information_sharing_risk',
        probability: 0.3,
        impact: 0.6,
        mitigationStrategy: '建立信息安全协议和访问控制',
        responsibleNodes: ['all'],
        contingencyPlan: '启用备用通信渠道',
      },
      {
        riskType: 'coordination_failure',
        probability: 0.4,
        impact: 0.7,
        mitigationStrategy: '建立清晰的治理结构和决策流程',
        responsibleNodes: ['lead_node'],
        contingencyPlan: '升级到高级管理层决策',
      },
      {
        riskType: 'technology_integration_risk',
        probability: 0.5,
        impact: 0.5,
        mitigationStrategy: '分阶段实施和充分测试',
        responsibleNodes: ['tech_team'],
        contingencyPlan: '回退到手工流程',
      },
    ];
  }

  /**
   * 生成治理结构
   */
  private generateGovernanceStructure(nodes: any[]): any {
    return {
      governanceLevel: 'strategic',
      decisionMakers: nodes.slice(0, 3).map(n => n.nodeId || n),
      meetingFrequency: 'bi-weekly',
      escalationProcess: '节点负责人 -> 项目经理 -> 执行委员会',
      conflictResolution: '协商 -> 调解 -> 仲裁',
    };
  }
}
