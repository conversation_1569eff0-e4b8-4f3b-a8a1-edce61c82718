import { ConfigModule } from '@nestjs/config';

// 测试环境配置
process.env.NODE_ENV = 'test';
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '3306';
process.env.DB_USERNAME = 'test';
process.env.DB_PASSWORD = 'test';
process.env.DB_DATABASE = 'monitoring_test';
process.env.DB_SYNCHRONIZE = 'true';

process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';
process.env.REDIS_DB = '1';

process.env.ELASTICSEARCH_NODE = 'http://localhost:9200';
process.env.ELASTICSEARCH_INDEX_PREFIX = 'monitoring_test';

// 设置测试超时时间
jest.setTimeout(30000);

// 全局测试钩子
beforeAll(async () => {
  console.log('开始运行监控服务 E2E 测试');
});

afterAll(async () => {
  console.log('监控服务 E2E 测试完成');
});
