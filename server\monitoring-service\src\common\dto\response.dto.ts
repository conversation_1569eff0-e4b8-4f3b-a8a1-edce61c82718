/**
 * 统一响应格式
 */
export class ResponseDto<T = any> {
  /**
   * 状态码
   */
  code: number;

  /**
   * 响应消息
   */
  message: string;

  /**
   * 响应数据
   */
  data?: T;

  /**
   * 时间戳
   */
  timestamp: string;

  /**
   * 请求路径
   */
  path?: string;

  constructor(code: number, message: string, data?: T, path?: string) {
    this.code = code;
    this.message = message;
    this.data = data;
    this.timestamp = new Date().toISOString();
    this.path = path;
  }

  /**
   * 成功响应
   */
  static success<T>(data?: T, message = '操作成功'): ResponseDto<T> {
    return new ResponseDto(200, message, data);
  }

  /**
   * 创建响应
   */
  static created<T>(data?: T, message = '创建成功'): ResponseDto<T> {
    return new ResponseDto(201, message, data);
  }

  /**
   * 错误响应
   */
  static error(message = '操作失败', code = 500): ResponseDto {
    return new ResponseDto(code, message);
  }

  /**
   * 参数错误响应
   */
  static badRequest(message = '参数错误'): ResponseDto {
    return new ResponseDto(400, message);
  }

  /**
   * 未授权响应
   */
  static unauthorized(message = '未授权'): ResponseDto {
    return new ResponseDto(401, message);
  }

  /**
   * 禁止访问响应
   */
  static forbidden(message = '禁止访问'): ResponseDto {
    return new ResponseDto(403, message);
  }

  /**
   * 未找到响应
   */
  static notFound(message = '资源未找到'): ResponseDto {
    return new ResponseDto(404, message);
  }

  /**
   * 服务器错误响应
   */
  static internalServerError(message = '服务器内部错误'): ResponseDto {
    return new ResponseDto(500, message);
  }
}
