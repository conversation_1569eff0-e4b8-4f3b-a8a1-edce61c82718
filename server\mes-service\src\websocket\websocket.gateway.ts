import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { WebSocketService } from './websocket.service';

/**
 * MES WebSocket网关
 */
@WebSocketGateway({
  cors: {
    origin: '*',
  },
  namespace: '/mes',
})
export class MESWebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(MESWebSocketGateway.name);

  constructor(private readonly webSocketService: WebSocketService) {}

  /**
   * 客户端连接处理
   */
  handleConnection(client: Socket) {
    const clientId = client.id;
    this.webSocketService.addClient(clientId, client);
    this.logger.log(`MES客户端连接: ${clientId}`);
    
    // 发送连接确认
    client.emit('connected', {
      clientId,
      message: 'MES系统连接成功',
      timestamp: new Date(),
    });
  }

  /**
   * 客户端断开处理
   */
  handleDisconnect(client: Socket) {
    const clientId = client.id;
    this.webSocketService.removeClient(clientId);
    this.logger.log(`MES客户端断开: ${clientId}`);
  }

  /**
   * 订阅订单更新
   */
  @SubscribeMessage('subscribe-order-updates')
  handleSubscribeOrderUpdates(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { orderIds?: string[] },
  ) {
    this.logger.log(`客户端 ${client.id} 订阅订单更新`);
    client.join('order-updates');
    
    if (data.orderIds && data.orderIds.length > 0) {
      data.orderIds.forEach(orderId => {
        client.join(`order-${orderId}`);
      });
    }

    client.emit('subscription-confirmed', {
      type: 'order-updates',
      message: '订单更新订阅成功',
      timestamp: new Date(),
    });
  }

  /**
   * 订阅质量检验更新
   */
  @SubscribeMessage('subscribe-quality-updates')
  handleSubscribeQualityUpdates(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { inspectionIds?: string[] },
  ) {
    this.logger.log(`客户端 ${client.id} 订阅质量检验更新`);
    client.join('quality-updates');
    
    if (data.inspectionIds && data.inspectionIds.length > 0) {
      data.inspectionIds.forEach(inspectionId => {
        client.join(`inspection-${inspectionId}`);
      });
    }

    client.emit('subscription-confirmed', {
      type: 'quality-updates',
      message: '质量检验更新订阅成功',
      timestamp: new Date(),
    });
  }

  /**
   * 订阅库存变化
   */
  @SubscribeMessage('subscribe-inventory-changes')
  handleSubscribeInventoryChanges(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { materialCodes?: string[] },
  ) {
    this.logger.log(`客户端 ${client.id} 订阅库存变化`);
    client.join('inventory-changes');
    
    if (data.materialCodes && data.materialCodes.length > 0) {
      data.materialCodes.forEach(materialCode => {
        client.join(`material-${materialCode}`);
      });
    }

    client.emit('subscription-confirmed', {
      type: 'inventory-changes',
      message: '库存变化订阅成功',
      timestamp: new Date(),
    });
  }

  /**
   * 订阅生产进度
   */
  @SubscribeMessage('subscribe-production-progress')
  handleSubscribeProductionProgress(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { orderIds?: string[] },
  ) {
    this.logger.log(`客户端 ${client.id} 订阅生产进度`);
    client.join('production-progress');
    
    if (data.orderIds && data.orderIds.length > 0) {
      data.orderIds.forEach(orderId => {
        client.join(`progress-${orderId}`);
      });
    }

    client.emit('subscription-confirmed', {
      type: 'production-progress',
      message: '生产进度订阅成功',
      timestamp: new Date(),
    });
  }

  /**
   * 订阅系统警告
   */
  @SubscribeMessage('subscribe-system-alerts')
  handleSubscribeSystemAlerts(@ConnectedSocket() client: Socket) {
    this.logger.log(`客户端 ${client.id} 订阅系统警告`);
    client.join('system-alerts');

    client.emit('subscription-confirmed', {
      type: 'system-alerts',
      message: '系统警告订阅成功',
      timestamp: new Date(),
    });
  }

  /**
   * 取消订阅
   */
  @SubscribeMessage('unsubscribe')
  handleUnsubscribe(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { type: string },
  ) {
    this.logger.log(`客户端 ${client.id} 取消订阅: ${data.type}`);
    
    switch (data.type) {
      case 'order-updates':
        client.leave('order-updates');
        break;
      case 'quality-updates':
        client.leave('quality-updates');
        break;
      case 'inventory-changes':
        client.leave('inventory-changes');
        break;
      case 'production-progress':
        client.leave('production-progress');
        break;
      case 'system-alerts':
        client.leave('system-alerts');
        break;
    }

    client.emit('unsubscription-confirmed', {
      type: data.type,
      message: '取消订阅成功',
      timestamp: new Date(),
    });
  }

  /**
   * 获取服务器状态
   */
  @SubscribeMessage('get-server-status')
  handleGetServerStatus(@ConnectedSocket() client: Socket) {
    const status = {
      connectedClients: this.webSocketService.getClientCount(),
      serverTime: new Date(),
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
    };

    client.emit('server-status', status);
  }

  /**
   * 心跳检测
   */
  @SubscribeMessage('ping')
  handlePing(@ConnectedSocket() client: Socket) {
    client.emit('pong', {
      timestamp: new Date(),
    });
  }

  /**
   * 广播订单更新
   */
  broadcastOrderUpdate(orderId: string, status: string, data: any) {
    this.server.to('order-updates').emit('order-updated', {
      orderId,
      status,
      data,
      timestamp: new Date(),
    });

    this.server.to(`order-${orderId}`).emit('order-updated', {
      orderId,
      status,
      data,
      timestamp: new Date(),
    });
  }

  /**
   * 广播质量检验结果
   */
  broadcastQualityResult(inspectionId: string, result: string, data: any) {
    this.server.to('quality-updates').emit('quality-result', {
      inspectionId,
      result,
      data,
      timestamp: new Date(),
    });

    this.server.to(`inspection-${inspectionId}`).emit('quality-result', {
      inspectionId,
      result,
      data,
      timestamp: new Date(),
    });
  }

  /**
   * 广播库存变化
   */
  broadcastInventoryChange(materialCode: string, change: any) {
    this.server.to('inventory-changes').emit('inventory-changed', {
      materialCode,
      change,
      timestamp: new Date(),
    });

    this.server.to(`material-${materialCode}`).emit('inventory-changed', {
      materialCode,
      change,
      timestamp: new Date(),
    });
  }

  /**
   * 广播生产进度
   */
  broadcastProductionProgress(orderId: string, progress: number, data: any) {
    this.server.to('production-progress').emit('progress-updated', {
      orderId,
      progress,
      data,
      timestamp: new Date(),
    });

    this.server.to(`progress-${orderId}`).emit('progress-updated', {
      orderId,
      progress,
      data,
      timestamp: new Date(),
    });
  }

  /**
   * 广播系统警告
   */
  broadcastSystemAlert(type: string, message: string, level: string = 'info') {
    this.server.to('system-alerts').emit('system-alert', {
      type,
      message,
      level,
      timestamp: new Date(),
    });
  }
}
