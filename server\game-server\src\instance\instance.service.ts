import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AgonesService } from '../agones/agones.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * 实例资源配置
 */
export interface InstanceResources {
  cpu: string;
  memory: string;
  storage: string;
}

/**
 * 实例监控配置
 */
export interface InstanceMonitoring {
  enableMetrics: boolean;
  metricsInterval: number;
  alertThresholds: {
    cpuUsage: number;
    memoryUsage: number;
    responseTime: number;
  };
}

/**
 * 实例网络配置
 */
export interface InstanceNetworking {
  maxConnections: number;
  connectionTimeout: number;
  enableCompression: boolean;
  enableBatching: boolean;
}

/**
 * 增强的实例类型
 */
export interface Instance {
  id: string;
  sceneId?: string;
  locationId?: string;
  channelId?: string;
  ipAddress: string;
  port: number;
  podName?: string;
  status: 'creating' | 'ready' | 'allocated' | 'error' | 'closed' | 'closing';
  currentUsers: number;
  maxUsers: number;
  isMediaInstance: boolean;
  createdAt: Date;
  updatedAt: Date;
  assignedAt?: Date;
  ended: boolean;
  // 新增的配置
  resources?: InstanceResources;
  monitoring?: InstanceMonitoring;
  networking?: InstanceNetworking;
}

/**
 * 实例用户类型
 */
export interface InstanceUser {
  id: string;
  instanceId: string;
  userId: string;
  joinedAt: Date;
  lastActiveAt: Date;
}

/**
 * 实例服务
 * 负责管理游戏实例的创建、分配和销毁
 */
@Injectable()
export class InstanceService {
  private readonly logger = new Logger(InstanceService.name);
  private instances: Map<string, Instance> = new Map();
  private instanceUsers: Map<string, InstanceUser[]> = new Map();
  private readonly maxUsersPerInstance: number;
  private readonly instanceMetrics: Map<string, {
    cpuUsage: number;
    memoryUsage: number;
    networkUsage: number;
    lastUpdated: Date;
  }> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly agonesService: AgonesService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    // 提升单实例用户容量到100+
    this.maxUsersPerInstance = this.configService.get<number>('MAX_USERS_PER_INSTANCE', 100);

    // 监听Agones事件
    this.eventEmitter.on('agones.ready', this.handleAgonesReady.bind(this));
    this.eventEmitter.on('agones.allocated', this.handleAgonesAllocated.bind(this));
    this.eventEmitter.on('agones.shutdown', this.handleAgonesShutdown.bind(this));

    // 启动性能监控
    this.startPerformanceMonitoring();
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    // 每5秒收集一次实例指标
    setInterval(() => {
      this.collectInstanceMetrics();
    }, 5000);

    // 每30秒进行一次健康检查
    setInterval(() => {
      this.performHealthCheck();
    }, 30000);
  }

  /**
   * 收集实例指标
   */
  private collectInstanceMetrics(): void {
    for (const [instanceId, instance] of this.instances) {
      if (instance.status === 'ready' || instance.status === 'allocated') {
        // 模拟指标收集（实际应该从系统获取）
        const metrics = {
          cpuUsage: this.calculateCPUUsage(instance),
          memoryUsage: this.calculateMemoryUsage(instance),
          networkUsage: this.calculateNetworkUsage(instance),
          lastUpdated: new Date(),
        };

        this.instanceMetrics.set(instanceId, metrics);

        // 发出指标更新事件
        this.eventEmitter.emit('instance.metrics', {
          instanceId,
          metrics,
        });
      }
    }
  }

  /**
   * 计算CPU使用率
   */
  private calculateCPUUsage(instance: Instance): number {
    // 基于用户数量的简化计算
    const baseUsage = (instance.currentUsers / instance.maxUsers) * 60; // 基础60%
    const randomVariation = Math.random() * 20 - 10; // ±10%的随机变化
    return Math.max(0, Math.min(100, baseUsage + randomVariation));
  }

  /**
   * 计算内存使用率
   */
  private calculateMemoryUsage(instance: Instance): number {
    // 基于用户数量的简化计算
    const baseUsage = (instance.currentUsers / instance.maxUsers) * 70; // 基础70%
    const randomVariation = Math.random() * 15 - 7.5; // ±7.5%的随机变化
    return Math.max(0, Math.min(100, baseUsage + randomVariation));
  }

  /**
   * 计算网络使用率
   */
  private calculateNetworkUsage(instance: Instance): number {
    // 基于用户数量的简化计算
    const baseUsage = (instance.currentUsers / instance.maxUsers) * 50; // 基础50%
    const randomVariation = Math.random() * 20 - 10; // ±10%的随机变化
    return Math.max(0, Math.min(100, baseUsage + randomVariation));
  }

  /**
   * 执行健康检查
   */
  private performHealthCheck(): void {
    for (const [instanceId, instance] of this.instances) {
      const metrics = this.instanceMetrics.get(instanceId);

      if (metrics) {
        // 检查是否需要告警
        if (metrics.cpuUsage > 90 || metrics.memoryUsage > 90) {
          this.logger.warn(`实例 ${instanceId} 资源使用率过高: CPU ${metrics.cpuUsage.toFixed(1)}%, 内存 ${metrics.memoryUsage.toFixed(1)}%`);

          this.eventEmitter.emit('instance.highLoad', {
            instanceId,
            metrics,
          });
        }

        // 检查实例是否响应
        if (Date.now() - metrics.lastUpdated.getTime() > 60000) {
          this.logger.error(`实例 ${instanceId} 可能无响应，最后更新时间: ${metrics.lastUpdated}`);

          this.eventEmitter.emit('instance.unresponsive', {
            instanceId,
            lastUpdate: metrics.lastUpdated,
          });
        }
      }
    }
  }

  /**
   * 处理Agones就绪事件
   */
  private async handleAgonesReady(gameServerInfo: any) {
    this.logger.log('Agones游戏服务器就绪');

    // 创建默认实例
    if (gameServerInfo && !this.instances.size) {
      await this.createInstance({
        sceneId: this.configService.get<string>('DEFAULT_SCENE_ID'),
      });
    }
  }

  /**
   * 处理Agones分配事件
   */
  private handleAgonesAllocated(gameServerInfo: any) {
    this.logger.log('Agones游戏服务器已分配');

    // 更新所有实例状态为已分配
    for (const [id, instance] of this.instances.entries()) {
      if (instance.status === 'ready') {
        instance.status = 'allocated';
        instance.assignedAt = new Date();
        this.instances.set(id, instance);

        // 触发实例分配事件
        this.eventEmitter.emit('instance.allocated', instance);
      }
    }
  }

  /**
   * 处理Agones关闭事件
   */
  private handleAgonesShutdown() {
    this.logger.log('Agones游戏服务器已关闭');

    // 标记所有实例为已结束
    for (const [id, instance] of this.instances.entries()) {
      instance.status = 'closed';
      instance.ended = true;
      instance.updatedAt = new Date();
      this.instances.set(id, instance);

      // 触发实例关闭事件
      this.eventEmitter.emit('instance.closed', instance);
    }
  }

  /**
   * 创建实例
   */
  async createInstance(options: {
    sceneId?: string;
    locationId?: string;
    channelId?: string;
    isMediaInstance?: boolean;
  }): Promise<Instance> {
    const gameServerInfo = await this.agonesService.getGameServer();

    if (!gameServerInfo) {
      throw new Error('无法获取游戏服务器信息');
    }

    const { status } = gameServerInfo;
    const ipAddress = status.address;
    const port = status.ports[0]?.port || this.configService.get<number>('DEFAULT_INSTANCE_PORT', 3031);

    // 增强的实例配置，支持100+用户
    const instance: Instance = {
      id: uuidv4(),
      sceneId: options.sceneId,
      locationId: options.locationId,
      channelId: options.channelId,
      ipAddress,
      port,
      podName: gameServerInfo.objectMeta?.name,
      status: 'creating',
      currentUsers: 0,
      maxUsers: this.maxUsersPerInstance, // 现在是100
      isMediaInstance: options.isMediaInstance || false,
      createdAt: new Date(),
      updatedAt: new Date(),
      ended: false,
      // 增强的资源配置
      resources: {
        cpu: this.configService.get<string>('INSTANCE_CPU_LIMIT', '2000m'), // 2核CPU
        memory: this.configService.get<string>('INSTANCE_MEMORY_LIMIT', '4Gi'), // 4GB内存
        storage: this.configService.get<string>('INSTANCE_STORAGE_LIMIT', '10Gi'), // 10GB存储
      },
      // 性能监控配置
      monitoring: {
        enableMetrics: true,
        metricsInterval: 5000,
        alertThresholds: {
          cpuUsage: this.configService.get<number>('CPU_ALERT_THRESHOLD', 80),
          memoryUsage: this.configService.get<number>('MEMORY_ALERT_THRESHOLD', 85),
          responseTime: this.configService.get<number>('RESPONSE_TIME_THRESHOLD', 100),
        },
      },
      // 网络配置
      networking: {
        maxConnections: this.configService.get<number>('MAX_CONNECTIONS_PER_INSTANCE', 1000),
        connectionTimeout: this.configService.get<number>('CONNECTION_TIMEOUT', 30000),
        enableCompression: this.configService.get<boolean>('ENABLE_COMPRESSION', true),
        enableBatching: this.configService.get<boolean>('ENABLE_BATCHING', true),
      },
    };

    this.instances.set(instance.id, instance);

    // 标记实例为就绪状态
    instance.status = 'ready';
    instance.updatedAt = new Date();
    this.instances.set(instance.id, instance);

    // 触发实例创建事件
    this.eventEmitter.emit('instance.created', instance);

    this.logger.log(`实例已创建: ${instance.id}`);

    return instance;
  }

  /**
   * 获取实例
   */
  getInstance(id: string): Instance | undefined {
    return this.instances.get(id);
  }

  /**
   * 销毁实例
   */
  async destroyInstance(id: string): Promise<void> {
    const instance = this.instances.get(id);
    if (!instance) {
      throw new Error(`实例不存在: ${id}`);
    }

    // 清理实例用户
    this.instanceUsers.delete(id);

    // 清理实例指标
    this.instanceMetrics.delete(id);

    // 移除实例
    this.instances.delete(id);

    this.logger.log(`实例已销毁: ${id}`);
  }

  /**
   * 获取所有实例
   */
  getAllInstances(): Instance[] {
    return Array.from(this.instances.values());
  }

  /**
   * 获取可用实例
   */
  getAvailableInstances(): Instance[] {
    return Array.from(this.instances.values()).filter(
      instance => instance.status === 'ready' || instance.status === 'allocated'
    );
  }

  /**
   * 加入实例
   */
  async joinInstance(instanceId: string, userId: string): Promise<boolean> {
    const instance = this.instances.get(instanceId);

    if (!instance) {
      this.logger.error(`实例不存在: ${instanceId}`);
      return false;
    }

    if (instance.currentUsers >= instance.maxUsers) {
      this.logger.error(`实例已满: ${instanceId}`);
      return false;
    }

    // 检查用户是否已经在实例中
    const users = this.instanceUsers.get(instanceId) || [];
    const existingUser = users.find(user => user.userId === userId);

    if (existingUser) {
      // 更新用户的最后活动时间
      existingUser.lastActiveAt = new Date();
      this.instanceUsers.set(instanceId, users);
      return true;
    }

    // 创建新的实例用户
    const instanceUser: InstanceUser = {
      id: uuidv4(),
      instanceId,
      userId,
      joinedAt: new Date(),
      lastActiveAt: new Date(),
    };

    // 添加用户到实例
    users.push(instanceUser);
    this.instanceUsers.set(instanceId, users);

    // 增加当前用户数
    instance.currentUsers += 1;
    instance.updatedAt = new Date();
    this.instances.set(instanceId, instance);

    // 触发用户加入事件
    this.eventEmitter.emit('instance.userJoined', { instance, userId, instanceUser });

    return true;
  }

  /**
   * 离开实例
   */
  async leaveInstance(instanceId: string, userId: string): Promise<boolean> {
    const instance = this.instances.get(instanceId);

    if (!instance) {
      this.logger.error(`实例不存在: ${instanceId}`);
      return false;
    }

    // 获取实例用户
    const users = this.instanceUsers.get(instanceId) || [];
    const userIndex = users.findIndex(user => user.userId === userId);

    if (userIndex === -1) {
      this.logger.warn(`用户 ${userId} 不在实例 ${instanceId} 中`);
      return false;
    }

    // 移除用户
    const removedUser = users.splice(userIndex, 1)[0];
    this.instanceUsers.set(instanceId, users);

    // 减少当前用户数
    if (instance.currentUsers > 0) {
      instance.currentUsers -= 1;
    }

    instance.updatedAt = new Date();
    this.instances.set(instanceId, instance);

    // 触发用户离开事件
    this.eventEmitter.emit('instance.userLeft', { instance, userId, instanceUser: removedUser });

    // 如果实例中没有用户，考虑关闭实例
    if (instance.currentUsers === 0 && this.configService.get<boolean>('AUTO_CLOSE_EMPTY_INSTANCES', true)) {
      this.logger.log(`实例空闲，准备关闭: ${instanceId}`);

      // 设置延迟关闭
      setTimeout(() => {
        const currentInstance = this.instances.get(instanceId);
        if (currentInstance && currentInstance.currentUsers === 0) {
          this.closeInstance(instanceId);
        }
      }, this.configService.get<number>('INSTANCE_SHUTDOWN_DELAY_MS', 30000));
    }

    return true;
  }

  /**
   * 获取实例用户
   * @param instanceId 实例ID
   * @returns 用户ID列表
   */
  async getInstanceUsers(instanceId: string): Promise<string[]> {
    const users = this.instanceUsers.get(instanceId) || [];
    return users.map(user => user.userId);
  }

  /**
   * 获取实例用户详情
   * @param instanceId 实例ID
   * @returns 用户详情列表
   */
  getInstanceUserDetails(instanceId: string): InstanceUser[] {
    return this.instanceUsers.get(instanceId) || [];
  }

  /**
   * 更新用户活动时间
   * @param instanceId 实例ID
   * @param userId 用户ID
   */
  updateUserActivity(instanceId: string, userId: string): void {
    const users = this.instanceUsers.get(instanceId) || [];
    const user = users.find(u => u.userId === userId);

    if (user) {
      user.lastActiveAt = new Date();
      this.instanceUsers.set(instanceId, users);
    }
  }

  /**
   * 关闭实例
   */
  async closeInstance(instanceId: string): Promise<boolean> {
    const instance = this.instances.get(instanceId);

    if (!instance) {
      this.logger.error(`实例不存在: ${instanceId}`);
      return false;
    }

    // 如果实例中有用户，先触发实例关闭中事件
    if (instance.currentUsers > 0) {
      // 标记实例为关闭中
      instance.status = 'closing';
      instance.updatedAt = new Date();
      this.instances.set(instanceId, instance);

      // 触发实例关闭中事件
      this.eventEmitter.emit('instance.closing', { instance });

      // 等待一段时间，让迁移过程完成
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    // 标记实例为已关闭
    instance.status = 'closed';
    instance.ended = true;
    instance.updatedAt = new Date();
    this.instances.set(instanceId, instance);

    // 清理实例用户
    this.instanceUsers.delete(instanceId);

    // 清理实例指标
    this.instanceMetrics.delete(instanceId);

    // 触发实例关闭事件
    this.eventEmitter.emit('instance.closed', instance);

    this.logger.log(`实例已关闭: ${instanceId}`);

    return true;
  }

  /**
   * 更新实例指标
   * @param instanceId 实例ID
   * @param metrics 指标数据
   */
  updateInstanceMetrics(
    instanceId: string,
    metrics: {
      cpuUsage: number;
      memoryUsage: number;
      networkUsage: number;
    }
  ): void {
    const instance = this.instances.get(instanceId);

    if (!instance) {
      this.logger.warn(`更新指标失败: 实例 ${instanceId} 不存在`);
      return;
    }

    // 更新实例指标
    this.instanceMetrics.set(instanceId, {
      ...metrics,
      lastUpdated: new Date(),
    });

    // 触发实例指标更新事件
    this.eventEmitter.emit('instance.metrics', {
      instanceId,
      ...metrics,
    });
  }

  /**
   * 获取实例指标
   * @param instanceId 实例ID
   */
  getInstanceMetrics(instanceId: string): any {
    const metrics = this.instanceMetrics.get(instanceId);

    if (!metrics) {
      return null;
    }

    return metrics;
  }

  /**
   * 准备迁移实例
   * @param instanceId 实例ID
   */
  async prepareInstanceMigration(instanceId: string): Promise<boolean> {
    const instance = this.instances.get(instanceId);

    if (!instance) {
      this.logger.error(`准备迁移失败: 实例 ${instanceId} 不存在`);
      return false;
    }

    // 标记实例为关闭中
    instance.status = 'closing';
    instance.updatedAt = new Date();
    this.instances.set(instanceId, instance);

    // 触发实例迁移准备事件
    this.eventEmitter.emit('instance.migration.preparing', { instance });

    return true;
  }
}
