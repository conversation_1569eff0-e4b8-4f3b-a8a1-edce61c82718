/**
 * 应用E2E测试
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('LearningTrackingService (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/ (GET)', () => {
    it('应该返回服务信息', () => {
      return request(app.getHttpServer())
        .get('/')
        .expect(200)
        .expect((res) => {
          expect(res.body.name).toBe('学习记录跟踪服务');
          expect(res.body.version).toBe('1.0.0');
          expect(res.body.features).toBeInstanceOf(Array);
        });
    });
  });

  describe('/version (GET)', () => {
    it('应该返回版本信息', () => {
      return request(app.getHttpServer())
        .get('/version')
        .expect(200)
        .expect((res) => {
          expect(res.body.version).toBe('1.0.0');
          expect(res.body.nodeVersion).toBeDefined();
        });
    });
  });

  describe('/api/v1/health (GET)', () => {
    it('应该返回健康状态', () => {
      return request(app.getHttpServer())
        .get('/api/v1/health')
        .expect(200);
    });
  });

  describe('/api/v1/health/live (GET)', () => {
    it('应该返回存活状态', () => {
      return request(app.getHttpServer())
        .get('/api/v1/health/live')
        .expect(200)
        .expect((res) => {
          expect(res.body.status).toBe('alive');
        });
    });
  });
});
