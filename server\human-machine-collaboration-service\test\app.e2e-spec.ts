import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('AppController (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [__dirname + '/../src/**/*.entity{.ts,.js}'],
          synchronize: true,
          logging: false,
        }),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    
    // 设置全局前缀
    app.setGlobalPrefix('api/v1');
    
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Health Check', () => {
    it('/api/v1/health (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/health')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status', 'ok');
          expect(res.body).toHaveProperty('service', 'human-machine-collaboration-service');
          expect(res.body).toHaveProperty('version', '1.0.0');
          expect(res.body).toHaveProperty('uptime');
          expect(res.body).toHaveProperty('memory');
          expect(res.body).toHaveProperty('features');
        });
    });
  });

  describe('Service Info', () => {
    it('/api/v1/info (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/info')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('name', '人机协作增强服务');
          expect(res.body).toHaveProperty('description');
          expect(res.body).toHaveProperty('version', '1.0.0');
          expect(res.body).toHaveProperty('author', 'DL Engine Team');
          expect(res.body).toHaveProperty('features');
          expect(Array.isArray(res.body.features)).toBe(true);
          expect(res.body.features.length).toBeGreaterThan(0);
        });
    });
  });

  describe('System Stats', () => {
    it('/api/v1/stats (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/stats')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('timestamp');
          expect(res.body).toHaveProperty('system');
          expect(res.body).toHaveProperty('service');
          expect(res.body).toHaveProperty('features');
          
          expect(res.body.system).toHaveProperty('platform');
          expect(res.body.system).toHaveProperty('arch');
          expect(res.body.system).toHaveProperty('nodeVersion');
          
          expect(res.body.features).toHaveProperty('arvrEnabled', true);
          expect(res.body.features).toHaveProperty('aiEnabled', true);
          expect(res.body.features).toHaveProperty('voiceEnabled', true);
          expect(res.body.features).toHaveProperty('gestureEnabled', true);
        });
    });
  });

  describe('AI Assistant', () => {
    it('/api/v1/ai/chat (POST)', () => {
      const chatData = {
        userId: 'test-user-123',
        message: '你好，我需要帮助',
        context: {},
      };

      return request(app.getHttpServer())
        .post('/api/v1/ai/chat')
        .send(chatData)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body).toHaveProperty('response');
          expect(res.body).toHaveProperty('intent');
          expect(res.body).toHaveProperty('confidence');
          expect(res.body).toHaveProperty('responseTime');
        });
    });

    it('/api/v1/ai/statistics (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/ai/statistics')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('totalConversations');
          expect(res.body).toHaveProperty('averageResponseTime');
          expect(typeof res.body.totalConversations).toBe('number');
          expect(typeof res.body.averageResponseTime).toBe('number');
        });
    });
  });

  describe('Voice Interaction', () => {
    it('/api/v1/voice/languages (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/voice/languages')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('speechRecognition');
          expect(res.body).toHaveProperty('textToSpeech');
          expect(res.body).toHaveProperty('common');
          expect(Array.isArray(res.body.speechRecognition)).toBe(true);
          expect(Array.isArray(res.body.textToSpeech)).toBe(true);
          expect(Array.isArray(res.body.common)).toBe(true);
        });
    });

    it('/api/v1/voice/synthesize (POST)', () => {
      const ttsData = {
        text: '你好，欢迎使用语音助手',
        options: {
          language: 'zh-CN',
          voice: 'zh-CN-XiaoxiaoNeural',
        },
      };

      return request(app.getHttpServer())
        .post('/api/v1/voice/synthesize')
        .send(ttsData)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('success');
          if (res.body.success) {
            expect(res.body).toHaveProperty('audioData');
            expect(res.body).toHaveProperty('audioFormat');
            expect(res.body).toHaveProperty('duration');
            expect(res.body).toHaveProperty('processingTime');
          }
        });
    });

    it('/api/v1/voice/statistics (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/voice/statistics')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('totalCommands');
          expect(res.body).toHaveProperty('successRate');
          expect(res.body).toHaveProperty('averageConfidence');
          expect(res.body).toHaveProperty('averageProcessingTime');
          expect(res.body).toHaveProperty('commandTypeDistribution');
        });
    });
  });

  describe('Gesture Recognition', () => {
    it('/api/v1/gesture/patterns (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/gesture/patterns')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
          // 应该有一些默认的手势模式
          if (res.body.length > 0) {
            expect(res.body[0]).toHaveProperty('id');
            expect(res.body[0]).toHaveProperty('patternName');
            expect(res.body[0]).toHaveProperty('gestureType');
            expect(res.body[0]).toHaveProperty('description');
          }
        });
    });

    it('/api/v1/gesture/sessions (POST)', () => {
      const sessionData = {
        userId: 'test-user-123',
        sessionId: 'gesture-session-123',
        options: {
          confidenceThreshold: 0.8,
          trackingMode: 'hands',
        },
      };

      return request(app.getHttpServer())
        .post('/api/v1/gesture/sessions')
        .send(sessionData)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('success', true);
          expect(res.body).toHaveProperty('sessionId', 'gesture-session-123');
          expect(res.body).toHaveProperty('message');
          expect(res.body).toHaveProperty('options');
        });
    });

    it('/api/v1/gesture/statistics (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/gesture/statistics')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('activeSessions');
          expect(res.body).toHaveProperty('totalPatterns');
          expect(res.body).toHaveProperty('systemPerformance');
          expect(typeof res.body.activeSessions).toBe('number');
          expect(typeof res.body.totalPatterns).toBe('number');
        });
    });
  });

  describe('AR/VR Guidance', () => {
    it('/api/v1/arvr/scenes (POST)', () => {
      const sceneConfig = {
        name: '测试维护场景',
        contentType: 'maintenance_guide',
        equipmentModel: 'Test Equipment',
        description: '这是一个测试场景',
        difficulty: 'beginner',
        estimatedDuration: 30,
      };

      return request(app.getHttpServer())
        .post('/api/v1/arvr/scenes')
        .send(sceneConfig)
        .expect(201)
        .expect((res) => {
          expect(typeof res.body).toBe('string'); // 应该返回场景ID
          expect(res.body).toMatch(/^scene_\d+$/);
        });
    });

    it('/api/v1/arvr/statistics (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/arvr/statistics')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('totalSessions');
          expect(res.body).toHaveProperty('averageCompletionTime');
          expect(res.body).toHaveProperty('averageAccuracy');
          expect(res.body).toHaveProperty('commonErrors');
          expect(res.body).toHaveProperty('sceneUsage');
          expect(typeof res.body.totalSessions).toBe('number');
          expect(typeof res.body.averageCompletionTime).toBe('number');
          expect(typeof res.body.averageAccuracy).toBe('number');
        });
    });
  });

  describe('Error Handling', () => {
    it('should return 404 for non-existent endpoints', () => {
      return request(app.getHttpServer())
        .get('/api/v1/non-existent')
        .expect(404);
    });

    it('should handle malformed JSON in POST requests', () => {
      return request(app.getHttpServer())
        .post('/api/v1/ai/chat')
        .send('invalid json')
        .set('Content-Type', 'application/json')
        .expect(400);
    });
  });
});
