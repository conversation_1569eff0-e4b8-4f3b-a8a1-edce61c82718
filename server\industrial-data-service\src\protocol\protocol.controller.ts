import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Param, 
  Delete,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
  BadRequestException
} from '@nestjs/common';
import { ProtocolService } from './protocol.service';
import { ProtocolConfig } from './interfaces/protocol.interface';

@Controller('protocols')
export class ProtocolController {
  constructor(private readonly protocolService: ProtocolService) {}

  /**
   * 获取支持的协议列表
   */
  @Get('supported')
  getSupportedProtocols() {
    const protocols = this.protocolService.getSupportedProtocols();
    return {
      success: true,
      message: '获取支持的协议列表成功',
      data: protocols
    };
  }

  /**
   * 获取协议信息
   */
  @Get(':protocol/info')
  getProtocolInfo(@Param('protocol') protocol: string) {
    const info = this.protocolService.getProtocolInfo(protocol);
    return {
      success: true,
      message: '获取协议信息成功',
      data: info
    };
  }

  /**
   * 测试协议连接
   */
  @Post('test-connection')
  async testConnection(@Body() config: ProtocolConfig) {
    const result = await this.protocolService.testProtocolConnection(config);
    return {
      success: result.success,
      message: result.message,
      data: {
        protocol: config.protocol,
        host: config.host,
        port: config.port,
        responseTime: result.responseTime
      }
    };
  }

  /**
   * 连接设备
   */
  @Post('connect')
  async connectDevice(@Body() config: ProtocolConfig) {
    const result = await this.protocolService.connectDevice(config);
    return {
      success: result.success,
      message: result.success ? '设备连接成功' : '设备连接失败',
      data: result
    };
  }

  /**
   * 断开设备连接
   */
  @Delete(':deviceId/disconnect')
  @HttpCode(HttpStatus.OK)
  async disconnectDevice(
    @Param('deviceId') deviceId: string,
    @Body('protocol') protocol: string
  ) {
    if (!protocol) {
      throw new BadRequestException('协议类型不能为空');
    }

    const result = await this.protocolService.disconnectDevice(deviceId, protocol);
    return {
      success: result,
      message: result ? '设备断开连接成功' : '设备断开连接失败',
      data: { deviceId, protocol }
    };
  }

  /**
   * 读取设备数据
   */
  @Post(':deviceId/read')
  async readDeviceData(
    @Param('deviceId') deviceId: string,
    @Body() body: { protocol: string; address: string }
  ) {
    const { protocol, address } = body;
    
    if (!protocol || !address) {
      throw new BadRequestException('协议类型和地址不能为空');
    }

    const result = await this.protocolService.readDeviceData(deviceId, protocol, address);
    return {
      success: result.success,
      message: result.success ? '数据读取成功' : '数据读取失败',
      data: result
    };
  }

  /**
   * 批量读取设备数据
   */
  @Post(':deviceId/read-multiple')
  async readMultipleDeviceData(
    @Param('deviceId') deviceId: string,
    @Body() body: { protocol: string; addresses: string[] }
  ) {
    const { protocol, addresses } = body;
    
    if (!protocol || !addresses || !Array.isArray(addresses)) {
      throw new BadRequestException('协议类型和地址列表不能为空');
    }

    const results = await this.protocolService.readMultipleDeviceData(deviceId, protocol, addresses);
    const successCount = results.filter(r => r.success).length;
    
    return {
      success: successCount > 0,
      message: `批量数据读取完成 (${successCount}/${addresses.length})`,
      data: results
    };
  }

  /**
   * 写入设备数据
   */
  @Post(':deviceId/write')
  async writeDeviceData(
    @Param('deviceId') deviceId: string,
    @Body() body: { protocol: string; address: string; value: any }
  ) {
    const { protocol, address, value } = body;
    
    if (!protocol || !address || value === undefined) {
      throw new BadRequestException('协议类型、地址和值不能为空');
    }

    const result = await this.protocolService.writeDeviceData(deviceId, protocol, address, value);
    return {
      success: result.success,
      message: result.success ? '数据写入成功' : '数据写入失败',
      data: result
    };
  }

  /**
   * 批量写入设备数据
   */
  @Post(':deviceId/write-multiple')
  async writeMultipleDeviceData(
    @Param('deviceId') deviceId: string,
    @Body() body: { protocol: string; data: { address: string; value: any }[] }
  ) {
    const { protocol, data } = body;
    
    if (!protocol || !data || !Array.isArray(data)) {
      throw new BadRequestException('协议类型和数据列表不能为空');
    }

    const results = await this.protocolService.writeMultipleDeviceData(deviceId, protocol, data);
    const successCount = results.filter(r => r.success).length;
    
    return {
      success: successCount > 0,
      message: `批量数据写入完成 (${successCount}/${data.length})`,
      data: results
    };
  }

  /**
   * 检查设备连接状态
   */
  @Get(':deviceId/connection-status')
  getConnectionStatus(
    @Param('deviceId') deviceId: string,
    @Body('protocol') protocol: string
  ) {
    if (!protocol) {
      throw new BadRequestException('协议类型不能为空');
    }

    const isConnected = this.protocolService.isDeviceConnected(deviceId, protocol);
    const connectionInfo = this.protocolService.getDeviceConnectionInfo(deviceId, protocol);
    
    return {
      success: true,
      message: '获取设备连接状态成功',
      data: {
        deviceId,
        protocol,
        connected: isConnected,
        connectionInfo
      }
    };
  }

  /**
   * 获取所有连接的设备
   */
  @Get('connected-devices')
  getAllConnectedDevices() {
    const devices = this.protocolService.getAllConnectedDevices();
    return {
      success: true,
      message: '获取所有连接设备成功',
      data: devices
    };
  }
}
