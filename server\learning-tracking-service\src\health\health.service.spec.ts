/**
 * 健康检查服务测试
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { getDataSourceToken } from '@nestjs/typeorm';
import { HealthService } from './health.service';

describe('HealthService', () => {
  let service: HealthService;
  let mockDataSource: any;

  beforeEach(async () => {
    mockDataSource = {
      query: jest.fn().mockResolvedValue([{ result: 1 }]),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HealthService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              const config = {
                NODE_ENV: 'test',
                PORT: 3030,
              };
              return config[key] || defaultValue;
            }),
          },
        },
        {
          provide: getDataSourceToken(),
          useValue: mockDataSource,
        },
      ],
    }).compile();

    service = module.get<HealthService>(HealthService);
  });

  it('应该被定义', () => {
    expect(service).toBeDefined();
  });

  describe('checkLiveness', () => {
    it('应该返回存活状态', async () => {
      const result = await service.checkLiveness();
      
      expect(result).toBeDefined();
      expect(result.status).toBe('alive');
      expect(result.timestamp).toBeDefined();
      expect(result.uptime).toBeDefined();
      expect(result.pid).toBeDefined();
    });
  });

  describe('checkReadiness', () => {
    it('应该返回就绪状态', async () => {
      const result = await service.checkReadiness();
      
      expect(result).toBeDefined();
      expect(result.status).toBe('ready');
      expect(result.timestamp).toBeDefined();
      expect(result.message).toBe('服务已就绪');
    });

    it('数据库连接失败时应该返回未就绪状态', async () => {
      mockDataSource.query.mockRejectedValue(new Error('数据库连接失败'));
      
      const result = await service.checkReadiness();
      
      expect(result).toBeDefined();
      expect(result.status).toBe('not_ready');
      expect(result.error).toBe('数据库连接失败');
    });
  });

  describe('getDetailedHealthStatus', () => {
    it('应该返回详细健康状态', async () => {
      const result = await service.getDetailedHealthStatus();
      
      expect(result).toBeDefined();
      expect(result.status).toBe('healthy');
      expect(result.timestamp).toBeDefined();
      expect(result.responseTime).toBeDefined();
      expect(result.service).toBeDefined();
      expect(result.checks).toBeDefined();
      expect(result.checks.database).toBeDefined();
      expect(result.checks.memory).toBeDefined();
      expect(result.checks.system).toBeDefined();
    });
  });
});
