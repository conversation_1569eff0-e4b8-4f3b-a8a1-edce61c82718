import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * 能耗类型枚举
 */
export enum EnergyType {
  ELECTRICITY = 'electricity',
  GAS = 'gas',
  STEAM = 'steam',
  COMPRESSED_AIR = 'compressed_air',
  COOLING = 'cooling',
  HEATING = 'heating',
}

/**
 * 能耗实体
 */
@Entity('energy_consumption')
@Index(['deviceId', 'timestamp'])
@Index(['energyType', 'timestamp'])
export class EnergyConsumption {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'device_id', length: 100 })
  @Index()
  deviceId: string;

  @Column({ name: 'device_name', length: 200 })
  deviceName: string;

  @Column({
    type: 'enum',
    enum: EnergyType,
    default: EnergyType.ELECTRICITY,
  })
  energyType: EnergyType;

  @Column({ type: 'datetime' })
  timestamp: Date;

  @Column({ type: 'decimal', precision: 12, scale: 4, comment: '能耗量' })
  consumption: number;

  @Column({ length: 20, comment: '单位' })
  unit: string;

  @Column({ name: 'peak_demand', type: 'decimal', precision: 12, scale: 4, nullable: true, comment: '峰值需求' })
  peakDemand: number;

  @Column({ name: 'average_demand', type: 'decimal', precision: 12, scale: 4, nullable: true, comment: '平均需求' })
  averageDemand: number;

  @Column({ name: 'cost_per_unit', type: 'decimal', precision: 8, scale: 4, nullable: true, comment: '单位成本' })
  costPerUnit: number;

  @Column({ name: 'total_cost', type: 'decimal', precision: 12, scale: 2, nullable: true, comment: '总成本' })
  totalCost: number;

  @Column({ name: 'efficiency_rating', type: 'decimal', precision: 5, scale: 2, nullable: true, comment: '效率评级' })
  efficiencyRating: number;

  @Column({ name: 'carbon_footprint', type: 'decimal', precision: 10, scale: 4, nullable: true, comment: '碳足迹' })
  carbonFootprint: number;

  @Column({ name: 'task_id', length: 100, nullable: true, comment: '关联任务ID' })
  taskId: string;

  @Column({ name: 'operation_mode', length: 50, nullable: true, comment: '运行模式' })
  operationMode: string;

  @Column({ name: 'ambient_temperature', type: 'decimal', precision: 5, scale: 2, nullable: true, comment: '环境温度' })
  ambientTemperature: number;

  @Column({ name: 'load_factor', type: 'decimal', precision: 5, scale: 2, nullable: true, comment: '负载因子' })
  loadFactor: number;

  @Column({ type: 'json', nullable: true, comment: '扩展属性' })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
