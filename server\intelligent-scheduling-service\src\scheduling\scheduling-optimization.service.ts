import { Injectable, Logger } from '@nestjs/common';
import { OptimizationConfigDto } from './dto/optimization-config.dto';

/**
 * 调度优化服务
 */
@Injectable()
export class SchedulingOptimizationService {
  private readonly logger = new Logger(SchedulingOptimizationService.name);
  private optimizationConfig: any = {};
  private optimizationStats = {
    totalOptimizations: 0,
    successfulOptimizations: 0,
    averageOptimizationTime: 0,
    bestOptimality: 0,
    lastOptimizationTime: new Date(),
  };

  /**
   * 设置优化配置
   */
  async setOptimizationConfig(config: OptimizationConfigDto): Promise<void> {
    try {
      this.optimizationConfig = { ...this.optimizationConfig, ...config };
      this.logger.log('优化配置更新成功');
    } catch (error) {
      this.logger.error(`设置优化配置失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取优化配置
   */
  getOptimizationConfig(): any {
    return this.optimizationConfig;
  }

  /**
   * 获取优化统计
   */
  async getOptimizationStats(): Promise<any> {
    return {
      ...this.optimizationStats,
      configuredAlgorithms: this.getConfiguredAlgorithms(),
      performanceMetrics: await this.getPerformanceMetrics(),
    };
  }

  /**
   * 更新优化统计
   */
  updateOptimizationStats(optimizationTime: number, optimality: number, success: boolean): void {
    this.optimizationStats.totalOptimizations++;
    if (success) {
      this.optimizationStats.successfulOptimizations++;
    }
    
    // 更新平均优化时间
    const total = this.optimizationStats.totalOptimizations;
    this.optimizationStats.averageOptimizationTime = 
      (this.optimizationStats.averageOptimizationTime * (total - 1) + optimizationTime) / total;
    
    // 更新最佳最优性
    if (optimality > this.optimizationStats.bestOptimality) {
      this.optimizationStats.bestOptimality = optimality;
    }
    
    this.optimizationStats.lastOptimizationTime = new Date();
  }

  /**
   * 获取配置的算法
   */
  private getConfiguredAlgorithms(): string[] {
    return [
      'genetic_algorithm',
      'simulated_annealing',
      'particle_swarm',
      'linear_programming',
      'heuristic'
    ];
  }

  /**
   * 获取性能指标
   */
  private async getPerformanceMetrics(): Promise<any> {
    return {
      successRate: this.optimizationStats.totalOptimizations > 0 
        ? this.optimizationStats.successfulOptimizations / this.optimizationStats.totalOptimizations 
        : 0,
      averageOptimizationTime: this.optimizationStats.averageOptimizationTime,
      bestOptimality: this.optimizationStats.bestOptimality,
    };
  }
}
