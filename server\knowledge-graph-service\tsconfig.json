{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "resolveJsonModule": true, "esModuleInterop": true, "paths": {"@/*": ["src/*"], "@/common/*": ["src/common/*"], "@/knowledge/*": ["src/knowledge/*"], "@/inference/*": ["src/inference/*"], "@/expert/*": ["src/expert/*"], "@/health/*": ["src/health/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}