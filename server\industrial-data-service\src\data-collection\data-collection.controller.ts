import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Param, 
  Query,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe
} from '@nestjs/common';
import { DataCollectionService } from './data-collection.service';

@Controller('data-collection')
export class DataCollectionController {
  constructor(private readonly dataCollectionService: DataCollectionService) {}

  /**
   * 启动数据采集
   */
  @Post('start/:deviceId')
  @HttpCode(HttpStatus.OK)
  async startCollection(@Param('deviceId', ParseUUIDPipe) deviceId: string) {
    const result = await this.dataCollectionService.startCollection(deviceId);
    return {
      success: result,
      message: result ? '数据采集启动成功' : '数据采集启动失败',
      data: { deviceId }
    };
  }

  /**
   * 停止数据采集
   */
  @Post('stop/:deviceId')
  @HttpCode(HttpStatus.OK)
  async stopCollection(@Param('deviceId', ParseUUIDPipe) deviceId: string) {
    const result = await this.dataCollectionService.stopCollection(deviceId);
    return {
      success: result,
      message: result ? '数据采集停止成功' : '数据采集停止失败',
      data: { deviceId }
    };
  }

  /**
   * 获取采集状态
   */
  @Get('status/:deviceId')
  async getCollectionStatus(@Param('deviceId', ParseUUIDPipe) deviceId: string) {
    const status = await this.dataCollectionService.getCollectionStatus(deviceId);
    return {
      success: true,
      message: '获取采集状态成功',
      data: status
    };
  }

  /**
   * 获取所有采集任务状态
   */
  @Get('status')
  async getAllCollectionStatus() {
    const statuses = await this.dataCollectionService.getAllCollectionStatus();
    return {
      success: true,
      message: '获取所有采集状态成功',
      data: statuses
    };
  }

  /**
   * 手动触发数据采集
   */
  @Post('trigger/:deviceId')
  @HttpCode(HttpStatus.OK)
  async triggerCollection(@Param('deviceId', ParseUUIDPipe) deviceId: string) {
    const result = await this.dataCollectionService.triggerCollection(deviceId);
    return {
      success: result.success,
      message: result.success ? '手动采集成功' : '手动采集失败',
      data: result
    };
  }

  /**
   * 获取采集统计信息
   */
  @Get('statistics')
  async getCollectionStatistics() {
    const statistics = await this.dataCollectionService.getCollectionStatistics();
    return {
      success: true,
      message: '获取采集统计信息成功',
      data: statistics
    };
  }

  /**
   * 更新采集配置
   */
  @Post('config/:deviceId')
  @HttpCode(HttpStatus.OK)
  async updateCollectionConfig(
    @Param('deviceId', ParseUUIDPipe) deviceId: string,
    @Body() config: any
  ) {
    const result = await this.dataCollectionService.updateCollectionConfig(deviceId, config);
    return {
      success: result,
      message: result ? '采集配置更新成功' : '采集配置更新失败',
      data: { deviceId, config }
    };
  }
}
