import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AIAssistantService } from './ai-assistant.service';
import { KnowledgeBaseService } from './knowledge-base.service';
import { NLPService } from './nlp.service';
import { AIConversation } from '../database/entities/ai-conversation.entity';

/**
 * AI智能助手模块
 * 提供自然语言理解、智能问答、知识库管理等功能
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([AIConversation]),
  ],
  providers: [
    AIAssistantService,
    KnowledgeBaseService,
    NLPService,
  ],
  exports: [
    AIAssistantService,
    KnowledgeBaseService,
    NLPService,
  ],
})
export class AIAssistantModule {}
