import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AlertService } from './alert.service';
import { AlertController } from './alert.controller';
import { AlertRule } from './entities/alert-rule.entity';
import { AlertInstance } from './entities/alert-instance.entity';
import { StorageModule } from '../storage/storage.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([AlertRule, AlertInstance]),
    StorageModule
  ],
  controllers: [AlertController],
  providers: [AlertService],
  exports: [AlertService]
})
export class AlertModule {}
