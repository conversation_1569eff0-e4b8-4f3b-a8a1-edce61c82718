import { IsOptional, IsUUID, <PERSON>String, IsDateString, IsEnum, IsArray, IsNumber, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';
import { DataQuality } from '../entities/time-series-data.entity';

export enum AggregationType {
  NONE = 'none',
  AVG = 'avg',
  MIN = 'min',
  MAX = 'max',
  SUM = 'sum',
  COUNT = 'count',
  FIRST = 'first',
  LAST = 'last'
}

export class QueryDataDto {
  @IsOptional()
  @IsUUID()
  deviceId?: string;

  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  deviceIds?: string[];

  @IsOptional()
  @IsString()
  tagName?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tagNames?: string[];

  @IsDateString()
  startTime: string;

  @IsDateString()
  endTime: string;

  @IsOptional()
  @IsEnum(DataQuality)
  quality?: DataQuality;

  @IsOptional()
  @IsEnum(AggregationType)
  aggregation?: AggregationType = AggregationType.NONE;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  interval?: number; // 聚合间隔（秒）

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @Max(10000)
  limit?: number = 1000;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(0)
  offset?: number = 0;

  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'ASC';
}
