# DL引擎知识库服务 (Knowledge Base Service)

## 概述

知识库服务是DL引擎生态系统的核心组件之一，提供智能知识管理、文档处理、向量存储和语义检索功能。该服务支持多种文档格式的上传、解析和智能检索，为虚拟展厅和数字人应用提供强大的知识支撑。

## 主要功能

### 🗂️ 知识库管理
- 创建和管理多个知识库
- 支持场景关联和权限控制
- 知识库配置和统计信息

### 📄 文档处理
- 支持多种文档格式：PDF、Word、PowerPoint、Excel、文本文件
- 智能文档解析和内容提取
- 文档分块和元数据管理
- 异步处理队列

### 🔍 语义检索
- 基于向量的语义搜索
- 混合搜索（语义+关键词）
- 搜索结果重排序和优化
- 查询扩展和建议

### 🧠 向量嵌入
- 多语言文本嵌入生成
- 批量嵌入处理
- 文本相似度计算
- 支持多种嵌入模型

## 技术架构

### 核心技术栈
- **框架**: NestJS + TypeScript
- **数据库**: MySQL (关系数据) + ChromaDB (向量数据)
- **缓存**: Redis
- **队列**: Bull Queue
- **嵌入模型**: Transformers.js + multilingual-e5-large

### 服务架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HTTP API      │    │   微服务接口     │    │   健康检查       │
│   (Port 4008)   │    │   (Port 3008)   │    │   /health       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
┌─────────────────────────────────────────────────────────────────┐
│                    知识库服务核心                                │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│   知识库管理     │   文档处理       │   语义检索       │  向量嵌入  │
│   - 创建/删除    │   - 文件上传     │   - 向量搜索     │  - 文本嵌入│
│   - 权限控制     │   - 内容解析     │   - 混合搜索     │  - 相似度  │
│   - 统计信息     │   - 分块处理     │   - 结果优化     │  - 批处理  │
└─────────────────┴─────────────────┴─────────────────┴───────────┘
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     MySQL       │    │    ChromaDB     │    │     Redis       │
│   关系数据存储   │    │   向量数据存储   │    │   缓存和队列     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## API文档

### 基础信息
- **基础URL**: `http://localhost:4008/api/v1`
- **认证方式**: Bearer Token (JWT)
- **API文档**: `http://localhost:4008/api/docs`

### 主要端点

#### 知识库管理
```http
POST   /knowledge-base              # 创建知识库
GET    /knowledge-base              # 获取知识库列表
GET    /knowledge-base/:id          # 获取知识库详情
PATCH  /knowledge-base/:id          # 更新知识库
DELETE /knowledge-base/:id          # 删除知识库
POST   /knowledge-base/:id/search   # 搜索知识库
GET    /knowledge-base/:id/statistics # 获取统计信息
```

#### 文档管理
```http
POST   /documents/upload/:knowledgeBaseId    # 上传文档
GET    /documents/knowledge-base/:knowledgeBaseId # 获取文档列表
GET    /documents/:id                        # 获取文档详情
DELETE /documents/:id                        # 删除文档
POST   /documents/:id/reprocess              # 重新处理文档
```

#### 搜索服务
```http
POST   /search/knowledge-base/:knowledgeBaseId         # 语义搜索
POST   /search/knowledge-base/:knowledgeBaseId/hybrid  # 混合搜索
GET    /search/knowledge-base/:knowledgeBaseId/suggestions # 搜索建议
```

#### 嵌入服务
```http
POST   /embeddings/generate     # 生成文本嵌入
POST   /embeddings/batch        # 批量生成嵌入
POST   /embeddings/similarity   # 计算相似度
GET    /embeddings/model-info   # 获取模型信息
```

## 快速开始

### 环境要求
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- ChromaDB

### 安装和运行

1. **克隆项目**
```bash
git clone <repository-url>
cd server/knowledge-base-service
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和服务连接信息
```

4. **启动依赖服务**
```bash
# 使用Docker Compose启动依赖服务
docker-compose up -d mysql redis chromadb
```

5. **运行服务**
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

### Docker部署

1. **构建镜像**
```bash
docker build -t dl-knowledge-base-service .
```

2. **使用Docker Compose**
```bash
docker-compose up -d
```

## 配置说明

### 环境变量
详细的环境变量配置请参考 `.env.example` 文件。

### 主要配置项
- **数据库配置**: MySQL连接信息
- **Redis配置**: 缓存和队列配置
- **ChromaDB配置**: 向量数据库连接
- **嵌入模型配置**: 文本嵌入模型设置
- **文件上传配置**: 上传路径和大小限制

## 开发指南

### 项目结构
```
src/
├── auth/                 # 认证模块
├── common/              # 通用组件
│   ├── decorators/      # 装饰器
│   ├── filters/         # 异常过滤器
│   ├── guards/          # 守卫
│   └── interceptors/    # 拦截器
├── knowledge-base/      # 知识库模块
├── documents/           # 文档模块
├── search/             # 搜索模块
├── embeddings/         # 嵌入模块
├── vector-store/       # 向量存储模块
├── app.module.ts       # 主模块
└── main.ts            # 启动文件
```

### 测试
```bash
# 单元测试
npm run test

# E2E测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

### 代码规范
```bash
# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 监控和运维

### 健康检查
- **端点**: `GET /health`
- **响应**: 服务状态和版本信息

### 日志
- 日志级别：error, warn, info, debug
- 日志文件：`./logs/knowledge-base-service.log`

### 性能监控
- 支持Prometheus指标收集
- 提供API响应时间和错误率监控

## 故障排除

### 常见问题

1. **嵌入模型加载失败**
   - 检查网络连接和模型下载
   - 确认内存是否充足

2. **ChromaDB连接失败**
   - 检查ChromaDB服务状态
   - 验证连接配置

3. **文档处理失败**
   - 检查文件格式支持
   - 确认文件大小限制

### 日志分析
```bash
# 查看服务日志
docker logs dl-knowledge-base-service

# 查看错误日志
grep "ERROR" logs/knowledge-base-service.log
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。
