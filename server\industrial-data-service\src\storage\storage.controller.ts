import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Query, 
  Param,
  Delete,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
  ValidationPipe
} from '@nestjs/common';
import { StorageService } from './storage.service';
import { StoreDataDto, BatchStoreDataDto } from './dto/store-data.dto';
import { QueryDataDto } from './dto/query-data.dto';

@Controller('storage')
export class StorageController {
  constructor(private readonly storageService: StorageService) {}

  /**
   * 存储数据
   */
  @Post('store')
  @HttpCode(HttpStatus.CREATED)
  async storeData(@Body() storeDataDto: StoreDataDto) {
    const result = await this.storageService.storeData(storeDataDto);
    return {
      success: true,
      message: '数据存储成功',
      data: {
        deviceId: storeDataDto.deviceId,
        storedCount: result.length
      }
    };
  }

  /**
   * 批量存储数据
   */
  @Post('batch-store')
  @HttpCode(HttpStatus.CREATED)
  async batchStoreData(@Body() batchStoreDataDto: BatchStoreDataDto) {
    const result = await this.storageService.batchStoreData(batchStoreDataDto);
    return {
      success: true,
      message: '批量数据存储完成',
      data: result
    };
  }

  /**
   * 查询数据
   */
  @Get('query')
  async queryData(@Query(new ValidationPipe({ transform: true })) queryDto: QueryDataDto) {
    const result = await this.storageService.queryData(queryDto);
    return {
      success: true,
      message: '数据查询成功',
      data: result.data,
      meta: {
        total: result.total,
        aggregated: result.aggregated,
        query: queryDto
      }
    };
  }

  /**
   * 获取设备最新数据
   */
  @Get('latest/:deviceId')
  async getLatestData(
    @Param('deviceId', ParseUUIDPipe) deviceId: string,
    @Query('tagName') tagName?: string
  ) {
    const data = await this.storageService.getLatestData(deviceId, tagName);
    return {
      success: true,
      message: '获取最新数据成功',
      data
    };
  }

  /**
   * 获取存储统计信息
   */
  @Get('statistics')
  async getStorageStatistics() {
    const statistics = await this.storageService.getStorageStatistics();
    return {
      success: true,
      message: '获取存储统计信息成功',
      data: statistics
    };
  }

  /**
   * 删除过期数据
   */
  @Delete('expired')
  @HttpCode(HttpStatus.OK)
  async deleteExpiredData(@Query('retentionDays') retentionDays: string = '365') {
    const days = parseInt(retentionDays);
    if (isNaN(days) || days < 1) {
      return {
        success: false,
        message: '保留天数必须是大于0的整数'
      };
    }

    const result = await this.storageService.deleteExpiredData(days);
    return {
      success: true,
      message: '过期数据删除成功',
      data: result
    };
  }
}
