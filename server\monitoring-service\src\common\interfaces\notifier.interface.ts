/**
 * 通知器接口
 */
export interface NotifierInterface {
  /**
   * 发送通知
   * @param recipient 接收者
   * @param subject 主题
   * @param content 内容
   * @param options 额外选项
   */
  send(
    recipient: string,
    subject: string,
    content: string,
    options?: Record<string, any>,
  ): Promise<boolean>;

  /**
   * 测试通知配置
   */
  test(): Promise<boolean>;

  /**
   * 获取通知器类型
   */
  getType(): string;
}

/**
 * 通知配置接口
 */
export interface NotificationConfig {
  enabled: boolean;
  [key: string]: any;
}

/**
 * 通知消息接口
 */
export interface NotificationMessage {
  recipient: string;
  subject: string;
  content: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  template?: string;
  variables?: Record<string, any>;
}

/**
 * 通知结果接口
 */
export interface NotificationResult {
  success: boolean;
  messageId?: string;
  error?: string;
  timestamp: Date;
}
