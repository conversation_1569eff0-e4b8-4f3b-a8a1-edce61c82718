import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Device, DeviceStatus } from './entities/device.entity';
import { CreateDeviceDto } from './dto/create-device.dto';
import { UpdateDeviceDto } from './dto/update-device.dto';
import { DeviceQueryDto } from './dto/device-query.dto';

@Injectable()
export class DeviceManagementService {
  private readonly logger = new Logger(DeviceManagementService.name);

  constructor(
    @InjectRepository(Device)
    private deviceRepository: Repository<Device>,
  ) {}

  /**
   * 创建设备
   */
  async create(createDeviceDto: CreateDeviceDto): Promise<Device> {
    try {
      // 检查设备名称是否已存在
      const existingDevice = await this.deviceRepository.findOne({
        where: { name: createDeviceDto.name }
      });

      if (existingDevice) {
        throw new BadRequestException(`设备名称 "${createDeviceDto.name}" 已存在`);
      }

      // 检查IP地址和端口组合是否已存在
      const existingConnection = await this.deviceRepository.findOne({
        where: { 
          ipAddress: createDeviceDto.ipAddress,
          port: createDeviceDto.port
        }
      });

      if (existingConnection) {
        throw new BadRequestException(`设备连接 "${createDeviceDto.ipAddress}:${createDeviceDto.port}" 已存在`);
      }

      const device = this.deviceRepository.create({
        ...createDeviceDto,
        status: DeviceStatus.OFFLINE
      });

      const savedDevice = await this.deviceRepository.save(device);
      this.logger.log(`设备创建成功: ${savedDevice.name} (${savedDevice.id})`);
      
      return savedDevice;
    } catch (error) {
      this.logger.error(`创建设备失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 查询设备列表
   */
  async findAll(query: DeviceQueryDto): Promise<{ devices: Device[]; total: number; page: number; limit: number }> {
    try {
      const { page, limit, search, sortBy, sortOrder, ...filters } = query;
      
      const queryBuilder = this.deviceRepository.createQueryBuilder('device');

      // 应用过滤条件
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          queryBuilder.andWhere(`device.${key} = :${key}`, { [key]: value });
        }
      });

      // 搜索条件
      if (search) {
        queryBuilder.andWhere(
          '(device.name LIKE :search OR device.description LIKE :search OR device.location LIKE :search)',
          { search: `%${search}%` }
        );
      }

      // 排序
      queryBuilder.orderBy(`device.${sortBy}`, sortOrder);

      // 分页
      const offset = (page - 1) * limit;
      queryBuilder.skip(offset).take(limit);

      const [devices, total] = await queryBuilder.getManyAndCount();

      return {
        devices,
        total,
        page,
        limit
      };
    } catch (error) {
      this.logger.error(`查询设备列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据ID查询设备
   */
  async findOne(id: string): Promise<Device> {
    try {
      const device = await this.deviceRepository.findOne({
        where: { id },
        relations: ['dataPoints']
      });

      if (!device) {
        throw new NotFoundException(`设备不存在: ${id}`);
      }

      return device;
    } catch (error) {
      this.logger.error(`查询设备失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新设备
   */
  async update(id: string, updateDeviceDto: UpdateDeviceDto): Promise<Device> {
    try {
      const device = await this.findOne(id);

      // 如果更新名称，检查是否重复
      if (updateDeviceDto.name && updateDeviceDto.name !== device.name) {
        const existingDevice = await this.deviceRepository.findOne({
          where: { name: updateDeviceDto.name }
        });

        if (existingDevice) {
          throw new BadRequestException(`设备名称 "${updateDeviceDto.name}" 已存在`);
        }
      }

      Object.assign(device, updateDeviceDto);
      const updatedDevice = await this.deviceRepository.save(device);
      
      this.logger.log(`设备更新成功: ${updatedDevice.name} (${updatedDevice.id})`);
      return updatedDevice;
    } catch (error) {
      this.logger.error(`更新设备失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除设备
   */
  async remove(id: string): Promise<void> {
    try {
      const device = await this.findOne(id);
      await this.deviceRepository.remove(device);
      
      this.logger.log(`设备删除成功: ${device.name} (${id})`);
    } catch (error) {
      this.logger.error(`删除设备失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新设备状态
   */
  async updateStatus(id: string, status: DeviceStatus): Promise<Device> {
    try {
      const device = await this.findOne(id);
      device.status = status;
      
      if (status === DeviceStatus.ONLINE) {
        device.lastConnected = new Date();
        device.errorCount = 0;
      } else if (status === DeviceStatus.ERROR) {
        device.errorCount += 1;
      }

      const updatedDevice = await this.deviceRepository.save(device);
      this.logger.log(`设备状态更新: ${device.name} -> ${status}`);
      
      return updatedDevice;
    } catch (error) {
      this.logger.error(`更新设备状态失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取设备统计信息
   */
  async getStatistics(): Promise<any> {
    try {
      const total = await this.deviceRepository.count();
      const online = await this.deviceRepository.count({ where: { status: DeviceStatus.ONLINE } });
      const offline = await this.deviceRepository.count({ where: { status: DeviceStatus.OFFLINE } });
      const error = await this.deviceRepository.count({ where: { status: DeviceStatus.ERROR } });
      const maintenance = await this.deviceRepository.count({ where: { status: DeviceStatus.MAINTENANCE } });

      const typeStats = await this.deviceRepository
        .createQueryBuilder('device')
        .select('device.type', 'type')
        .addSelect('COUNT(*)', 'count')
        .groupBy('device.type')
        .getRawMany();

      return {
        total,
        statusStats: {
          online,
          offline,
          error,
          maintenance
        },
        typeStats: typeStats.reduce((acc, item) => {
          acc[item.type] = parseInt(item.count);
          return acc;
        }, {})
      };
    } catch (error) {
      this.logger.error(`获取设备统计信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
