import { Controller, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from './health.service';

/**
 * 健康检查控制器
 */
@ApiTags('health')
@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(private readonly healthService: HealthService) {}

  /**
   * 基础健康检查
   */
  @Get()
  @ApiOperation({ summary: '基础健康检查', description: '检查服务基本状态' })
  @ApiResponse({
    status: 200,
    description: '服务正常',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string' },
        uptime: { type: 'number' },
        version: { type: 'string' },
      },
    },
  })
  async check() {
    this.logger.log('执行基础健康检查');
    return await this.healthService.getBasicHealth();
  }

  /**
   * 详细健康检查
   */
  @Get('detailed')
  @ApiOperation({ summary: '详细健康检查', description: '检查服务详细状态包括数据库连接等' })
  @ApiResponse({
    status: 200,
    description: '详细健康状态',
  })
  async detailedCheck() {
    this.logger.log('执行详细健康检查');
    return await this.healthService.getDetailedHealth();
  }

  /**
   * 数据库健康检查
   */
  @Get('database')
  @ApiOperation({ summary: '数据库健康检查', description: '检查数据库连接状态' })
  @ApiResponse({
    status: 200,
    description: '数据库状态',
  })
  async databaseCheck() {
    this.logger.log('执行数据库健康检查');
    return await this.healthService.getDatabaseHealth();
  }

  /**
   * Redis健康检查
   */
  @Get('redis')
  @ApiOperation({ summary: 'Redis健康检查', description: '检查Redis连接状态' })
  @ApiResponse({
    status: 200,
    description: 'Redis状态',
  })
  async redisCheck() {
    this.logger.log('执行Redis健康检查');
    return await this.healthService.getRedisHealth();
  }

  /**
   * 系统资源检查
   */
  @Get('system')
  @ApiOperation({ summary: '系统资源检查', description: '检查系统资源使用情况' })
  @ApiResponse({
    status: 200,
    description: '系统资源状态',
  })
  async systemCheck() {
    this.logger.log('执行系统资源检查');
    return await this.healthService.getSystemHealth();
  }
}
