import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AIAssistantService } from '../ai-assistant/ai-assistant.service';

/**
 * AI智能助手控制器
 */
@ApiTags('AI Assistant')
@Controller('ai')
export class AIAssistantController {
  constructor(private readonly aiAssistantService: AIAssistantService) {}

  /**
   * 处理用户消息
   */
  @Post('chat')
  @ApiOperation({ summary: '与AI助手对话' })
  @ApiResponse({ status: 200, description: 'AI响应' })
  async chat(@Body() chatData: any) {
    const { userId, message, context } = chatData;
    return await this.aiAssistantService.processMessage(userId, message, context);
  }

  /**
   * 获取对话历史
   */
  @Get('conversations/:userId')
  @ApiOperation({ summary: '获取对话历史' })
  @ApiResponse({ status: 200, description: '对话历史' })
  async getConversationHistory(
    @Param('userId') userId: string,
    @Query('limit') limit?: number,
  ) {
    return await this.aiAssistantService.getConversationHistory(userId, limit);
  }

  /**
   * 更新对话反馈
   */
  @Post('conversations/:conversationId/feedback')
  @ApiOperation({ summary: '更新对话反馈' })
  @ApiResponse({ status: 200, description: '反馈已更新' })
  async updateFeedback(
    @Param('conversationId') conversationId: string,
    @Body() feedback: any,
  ) {
    await this.aiAssistantService.updateFeedback(conversationId, feedback);
    return { success: true, message: '反馈已更新' };
  }

  /**
   * 获取AI助手统计
   */
  @Get('statistics')
  @ApiOperation({ summary: '获取AI助手统计' })
  @ApiResponse({ status: 200, description: '统计数据' })
  async getStatistics() {
    return await this.aiAssistantService.getStatistics();
  }
}
