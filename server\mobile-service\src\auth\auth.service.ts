/**
 * 认证服务
 * 
 * 提供JWT令牌验证和用户认证功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

/**
 * JWT载荷接口
 */
export interface JwtPayload {
  sub: string; // 用户ID
  username?: string;
  email?: string;
  roles?: string[];
  deviceId?: string;
  iat?: number;
  exp?: number;
}

/**
 * 令牌验证结果接口
 */
export interface TokenValidationResult {
  valid: boolean;
  payload?: JwtPayload;
  error?: string;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 验证JWT令牌
   */
  async validateToken(token: string): Promise<TokenValidationResult> {
    try {
      const payload = await this.jwtService.verifyAsync(token);
      
      return {
        valid: true,
        payload,
      };
    } catch (error) {
      this.logger.warn(`令牌验证失败: ${error.message}`);
      
      return {
        valid: false,
        error: error.message,
      };
    }
  }

  /**
   * 生成访问令牌
   */
  async generateAccessToken(payload: Omit<JwtPayload, 'iat' | 'exp'>): Promise<string> {
    return this.jwtService.signAsync(payload);
  }

  /**
   * 生成刷新令牌
   */
  async generateRefreshToken(payload: Omit<JwtPayload, 'iat' | 'exp'>): Promise<string> {
    const refreshExpiresIn = this.configService.get<string>('JWT_REFRESH_EXPIRES_IN', '30d');
    
    return this.jwtService.signAsync(payload, {
      expiresIn: refreshExpiresIn,
    });
  }

  /**
   * 刷新访问令牌
   */
  async refreshAccessToken(refreshToken: string): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    const validation = await this.validateToken(refreshToken);
    
    if (!validation.valid || !validation.payload) {
      throw new Error('无效的刷新令牌');
    }

    // 生成新的访问令牌和刷新令牌
    const { iat, exp, ...payload } = validation.payload;
    
    const [newAccessToken, newRefreshToken] = await Promise.all([
      this.generateAccessToken(payload),
      this.generateRefreshToken(payload),
    ]);

    return {
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
    };
  }

  /**
   * 解码令牌（不验证）
   */
  decodeToken(token: string): JwtPayload | null {
    try {
      return this.jwtService.decode(token) as JwtPayload;
    } catch (error) {
      this.logger.warn(`令牌解码失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 检查令牌是否即将过期
   */
  isTokenExpiringSoon(token: string, thresholdMinutes: number = 30): boolean {
    const payload = this.decodeToken(token);
    
    if (!payload || !payload.exp) {
      return true;
    }

    const expirationTime = payload.exp * 1000; // 转换为毫秒
    const currentTime = Date.now();
    const thresholdTime = thresholdMinutes * 60 * 1000; // 转换为毫秒

    return (expirationTime - currentTime) <= thresholdTime;
  }

  /**
   * 获取令牌剩余有效时间
   */
  getTokenRemainingTime(token: string): number {
    const payload = this.decodeToken(token);
    
    if (!payload || !payload.exp) {
      return 0;
    }

    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();

    return Math.max(0, expirationTime - currentTime);
  }

  /**
   * 验证用户权限
   */
  hasPermission(payload: JwtPayload, requiredRoles: string[]): boolean {
    if (!payload.roles || payload.roles.length === 0) {
      return false;
    }

    return requiredRoles.some(role => payload.roles!.includes(role));
  }

  /**
   * 验证设备权限
   */
  hasDeviceAccess(payload: JwtPayload, deviceId: string): boolean {
    // 如果令牌中包含设备ID，则必须匹配
    if (payload.deviceId) {
      return payload.deviceId === deviceId;
    }

    // 如果令牌中没有设备ID，则允许访问（由其他守卫进行设备验证）
    return true;
  }
}
