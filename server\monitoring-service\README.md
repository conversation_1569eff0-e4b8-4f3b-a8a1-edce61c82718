# 监控服务 (Monitoring Service)

监控服务是一个全面的系统监控解决方案，提供指标收集、告警管理、健康检查、日志分析和多渠道通知功能。

## 功能特性

### 🔍 监控功能
- **指标收集**: 自动收集系统和服务指标
- **实时监控**: 实时监控服务状态和性能
- **数据聚合**: 智能数据聚合和存储
- **Prometheus集成**: 支持Prometheus指标导出

### 🚨 告警管理
- **灵活规则**: 支持自定义告警规则
- **智能评估**: 实时告警条件评估
- **告警分级**: 支持多级别告警管理
- **告警抑制**: 防止告警风暴

### 💊 健康检查
- **服务监控**: 监控微服务健康状态
- **自动恢复**: 支持服务自动恢复
- **依赖检查**: 检查服务依赖状态
- **健康报告**: 生成详细健康报告

### 📊 日志分析
- **日志收集**: 集中收集应用日志
- **实时分析**: 实时日志分析和处理
- **Elasticsearch集成**: 支持全文搜索
- **日志聚合**: 智能日志聚合和统计

### 📢 通知服务
- **多渠道通知**: 支持邮件、钉钉、企业微信、Slack等
- **通知模板**: 可自定义通知模板
- **通知历史**: 完整的通知历史记录
- **失败重试**: 通知失败自动重试

## 技术栈

- **框架**: NestJS + TypeScript
- **数据库**: MySQL + Redis
- **搜索引擎**: Elasticsearch
- **监控**: Prometheus + Grafana
- **消息队列**: Redis
- **容器化**: Docker + Docker Compose

## 快速开始

### 环境要求
- Node.js >= 18
- MySQL >= 8.0
- Redis >= 6.0
- Elasticsearch >= 8.0

### 安装依赖
```bash
npm install
```

### 配置环境
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 数据库初始化
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE monitoring;"

# 运行迁移
npm run migration:run
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run start:prod
```

### Docker部署
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f monitoring-service
```

## API文档

服务启动后，可以通过以下地址访问API文档：
- Swagger UI: http://localhost:3003/api/docs
- OpenAPI JSON: http://localhost:3003/api/docs-json

## 主要接口

### 监控接口
- `GET /api/v1/monitoring/metrics` - 获取系统指标
- `GET /api/v1/monitoring/services` - 获取服务状态
- `POST /api/v1/monitoring/collect` - 手动收集指标

### 告警接口
- `GET /api/v1/alerts` - 获取告警列表
- `POST /api/v1/alerts/rules` - 创建告警规则
- `PUT /api/v1/alerts/rules/:id` - 更新告警规则
- `DELETE /api/v1/alerts/rules/:id` - 删除告警规则

### 健康检查接口
- `GET /api/v1/health` - 服务健康检查
- `GET /api/v1/health/services` - 获取所有服务健康状态
- `POST /api/v1/health/recovery/:service` - 触发服务恢复

### 日志接口
- `GET /api/v1/logs` - 查询日志
- `POST /api/v1/logs/search` - 搜索日志
- `GET /api/v1/logs/analysis` - 日志分析报告

### 通知接口
- `GET /api/v1/notifications` - 获取通知历史
- `POST /api/v1/notifications/test` - 测试通知配置
- `PUT /api/v1/notifications/channels/:id` - 更新通知渠道

## 配置说明

### 数据库配置
```env
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=monitoring
```

### Redis配置
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

### Elasticsearch配置
```env
ELASTICSEARCH_NODE=http://localhost:9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
ELASTICSEARCH_INDEX_PREFIX=monitoring
```

### 通知配置
```env
# 邮件通知
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-password

# 钉钉通知
DINGTALK_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=xxx
DINGTALK_SECRET=your-secret

# 企业微信通知
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx
```

## 监控指标

### 系统指标
- CPU使用率
- 内存使用率
- 磁盘使用率
- 网络流量

### 应用指标
- 请求响应时间
- 请求成功率
- 错误率
- 吞吐量

### 业务指标
- 用户活跃度
- 业务操作成功率
- 关键业务流程监控

## 告警规则示例

```json
{
  "name": "CPU使用率过高",
  "description": "当CPU使用率超过80%时触发告警",
  "condition": "cpu_usage > 80",
  "severity": "warning",
  "duration": "5m",
  "notifications": ["email", "dingtalk"]
}
```

## 开发指南

### 项目结构
```
src/
├── monitoring/     # 监控模块
├── alert/         # 告警模块
├── health/        # 健康检查模块
├── logging/       # 日志模块
├── notification/  # 通知模块
└── common/        # 公共模块
```

### 添加新的监控指标
1. 在 `monitoring/entities` 中定义实体
2. 在 `monitoring/metrics-collector.service.ts` 中添加收集逻辑
3. 在 `monitoring/metrics-storage.service.ts` 中添加存储逻辑

### 添加新的通知渠道
1. 在 `notification/notifiers` 中创建通知器
2. 实现 `NotifierInterface` 接口
3. 在 `notification.service.ts` 中注册通知器

## 测试

```bash
# 运行单元测试
npm run test

# 运行集成测试
npm run test:e2e

# 运行测试覆盖率
npm run test:cov
```

## 部署

### 生产环境部署
1. 构建应用: `npm run build`
2. 配置环境变量
3. 启动服务: `npm run start:prod`

### Docker部署
```bash
# 构建镜像
docker build -t monitoring-service .

# 运行容器
docker run -d -p 3003:3003 --name monitoring-service monitoring-service
```

## 监控面板

访问 Grafana 面板查看监控数据：
- URL: http://localhost:3001
- 用户名: admin
- 密码: admin123

## 故障排除

### 常见问题
1. **服务无法启动**: 检查数据库连接配置
2. **指标收集失败**: 检查目标服务是否可访问
3. **告警不触发**: 检查告警规则配置
4. **通知发送失败**: 检查通知渠道配置

### 日志查看
```bash
# 查看应用日志
tail -f logs/application.log

# 查看错误日志
tail -f logs/error.log

# Docker环境查看日志
docker-compose logs -f monitoring-service
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
