import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';

// 实体
import { ProductionTask } from './entities/production-task.entity';
import { SchedulingSolution } from './entities/scheduling-solution.entity';
import { ScheduledTask } from './entities/scheduled-task.entity';
import { TaskConstraint } from './entities/task-constraint.entity';

// 控制器
import { SchedulingController } from './scheduling.controller';

// 服务
import { IntelligentSchedulerService } from './intelligent-scheduler.service';
import { SchedulingOptimizationService } from './scheduling-optimization.service';
import { TaskManagementService } from './task-management.service';
import { ConstraintSolverService } from './constraint-solver.service';

// 队列处理器
import { SchedulingProcessor } from './processors/scheduling.processor';

@Module({
  imports: [
    // 数据库实体
    TypeOrmModule.forFeature([
      ProductionTask,
      SchedulingSolution,
      ScheduledTask,
      TaskConstraint,
    ]),

    // 队列注册
    BullModule.registerQueue({
      name: 'scheduling-optimization',
    }),

    BullModule.registerQueue({
      name: 'task-processing',
    }),

    BullModule.registerQueue({
      name: 'solution-validation',
    }),
  ],

  controllers: [SchedulingController],

  providers: [
    IntelligentSchedulerService,
    SchedulingOptimizationService,
    TaskManagementService,
    ConstraintSolverService,
    SchedulingProcessor,
  ],

  exports: [
    IntelligentSchedulerService,
    SchedulingOptimizationService,
    TaskManagementService,
  ],
})
export class SchedulingModule {}
