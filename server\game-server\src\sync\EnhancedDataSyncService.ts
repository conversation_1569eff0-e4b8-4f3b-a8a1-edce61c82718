/**
 * 增强的数据同步服务
 * 专为支持100+并发用户优化的实时数据同步系统
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Redis } from 'ioredis';

// 同步操作类型
enum SyncOperationType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  TRANSFORM = 'transform',
  BATCH = 'batch',
}

// 同步数据项
interface SyncDataItem {
  id: string;
  type: string;
  operation: SyncOperationType;
  data: any;
  timestamp: number;
  userId: string;
  version: number;
  checksum?: string;
}

// 同步批次
interface SyncBatch {
  id: string;
  roomId: string;
  items: SyncDataItem[];
  timestamp: number;
  size: number;
  compressed: boolean;
}

// 冲突解决策略
enum ConflictResolutionStrategy {
  LAST_WRITE_WINS = 'last_write_wins',
  FIRST_WRITE_WINS = 'first_write_wins',
  MERGE = 'merge',
  USER_PRIORITY = 'user_priority',
}

// 同步配置
interface SyncConfig {
  batchSize: number;
  batchTimeout: number;
  enableCompression: boolean;
  enableDeltaSync: boolean;
  conflictResolution: ConflictResolutionStrategy;
  maxRetries: number;
  retryDelay: number;
  enableChecksums: boolean;
  enableVersioning: boolean;
}

// 同步统计
interface SyncStats {
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  conflictsResolved: number;
  averageLatency: number;
  compressionRatio: number;
  deltaCompressionRatio: number;
}

// 版本控制信息
interface VersionInfo {
  objectId: string;
  version: number;
  lastModified: number;
  lastModifiedBy: string;
  checksum: string;
}

/**
 * 增强的数据同步服务类
 */
@Injectable()
export class EnhancedDataSyncService {
  private readonly logger = new Logger(EnhancedDataSyncService.name);
  
  // Redis客户端
  private redisClient: Redis;
  
  // 同步队列
  private syncQueues: Map<string, SyncDataItem[]> = new Map();
  private batchTimers: Map<string, NodeJS.Timeout> = new Map();
  
  // 版本控制
  private versionStore: Map<string, VersionInfo> = new Map();
  
  // 冲突检测
  private conflictQueue: SyncDataItem[] = [];
  
  // 统计信息
  private stats: SyncStats = {
    totalOperations: 0,
    successfulOperations: 0,
    failedOperations: 0,
    conflictsResolved: 0,
    averageLatency: 0,
    compressionRatio: 0,
    deltaCompressionRatio: 0,
  };
  
  // 配置
  private config: SyncConfig = {
    batchSize: 50,
    batchTimeout: 100, // 100ms
    enableCompression: true,
    enableDeltaSync: true,
    conflictResolution: ConflictResolutionStrategy.LAST_WRITE_WINS,
    maxRetries: 3,
    retryDelay: 1000,
    enableChecksums: true,
    enableVersioning: true,
  };
  
  // 定时器
  private statsTimer?: NodeJS.Timeout;
  private conflictTimer?: NodeJS.Timeout;
  
  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.loadConfig();
    this.initialize();
  }
  
  /**
   * 加载配置
   */
  private loadConfig(): void {
    this.config = {
      batchSize: this.configService.get<number>('SYNC_BATCH_SIZE', 50),
      batchTimeout: this.configService.get<number>('SYNC_BATCH_TIMEOUT', 100),
      enableCompression: this.configService.get<boolean>('SYNC_ENABLE_COMPRESSION', true),
      enableDeltaSync: this.configService.get<boolean>('SYNC_ENABLE_DELTA', true),
      conflictResolution: this.configService.get<ConflictResolutionStrategy>(
        'SYNC_CONFLICT_RESOLUTION',
        ConflictResolutionStrategy.LAST_WRITE_WINS
      ),
      maxRetries: this.configService.get<number>('SYNC_MAX_RETRIES', 3),
      retryDelay: this.configService.get<number>('SYNC_RETRY_DELAY', 1000),
      enableChecksums: this.configService.get<boolean>('SYNC_ENABLE_CHECKSUMS', true),
      enableVersioning: this.configService.get<boolean>('SYNC_ENABLE_VERSIONING', true),
    };
  }
  
  /**
   * 初始化同步服务
   */
  private async initialize(): Promise<void> {
    try {
      // 初始化Redis连接
      await this.initializeRedis();
      
      // 启动统计监控
      this.startStatsMonitoring();
      
      // 启动冲突解决
      this.startConflictResolution();
      
      this.logger.log('增强数据同步服务初始化完成');
      
    } catch (error) {
      this.logger.error('数据同步服务初始化失败:', error);
      throw error;
    }
  }
  
  /**
   * 初始化Redis连接
   */
  private async initializeRedis(): Promise<void> {
    const redisConfig = {
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD'),
      db: this.configService.get<number>('REDIS_SYNC_DB', 1),
      keyPrefix: 'sync:',
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    };
    
    this.redisClient = new Redis(redisConfig);
    
    this.redisClient.on('connect', () => {
      this.logger.log('Redis同步连接建立');
    });
    
    this.redisClient.on('error', (error) => {
      this.logger.error('Redis同步连接错误:', error);
    });
    
    await this.redisClient.ping();
  }
  
  /**
   * 添加同步操作
   */
  public async addSyncOperation(
    roomId: string,
    objectId: string,
    type: string,
    operation: SyncOperationType,
    data: any,
    userId: string
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      // 创建同步数据项
      const syncItem: SyncDataItem = {
        id: this.generateSyncId(),
        type,
        operation,
        data,
        timestamp: Date.now(),
        userId,
        version: await this.getNextVersion(objectId),
      };
      
      // 计算校验和
      if (this.config.enableChecksums) {
        syncItem.checksum = this.calculateChecksum(syncItem.data);
      }
      
      // 检测冲突
      if (this.config.enableVersioning) {
        const conflict = await this.detectConflict(objectId, syncItem);
        if (conflict) {
          this.conflictQueue.push(syncItem);
          this.logger.warn(`检测到冲突: ${objectId}, 用户: ${userId}`);
          return;
        }
      }
      
      // 添加到同步队列
      this.addToSyncQueue(roomId, syncItem);
      
      // 更新版本信息
      if (this.config.enableVersioning) {
        await this.updateVersionInfo(objectId, syncItem);
      }
      
      // 更新统计
      this.stats.totalOperations++;
      this.stats.successfulOperations++;
      this.updateLatencyStats(startTime);
      
    } catch (error) {
      this.stats.failedOperations++;
      this.logger.error('添加同步操作失败:', error);
      throw error;
    }
  }
  
  /**
   * 添加到同步队列
   */
  private addToSyncQueue(roomId: string, syncItem: SyncDataItem): void {
    if (!this.syncQueues.has(roomId)) {
      this.syncQueues.set(roomId, []);
    }
    
    const queue = this.syncQueues.get(roomId)!;
    queue.push(syncItem);
    
    // 检查是否需要立即发送批次
    if (queue.length >= this.config.batchSize) {
      this.flushSyncQueue(roomId);
    } else if (!this.batchTimers.has(roomId)) {
      // 设置批处理定时器
      const timer = setTimeout(() => {
        this.flushSyncQueue(roomId);
      }, this.config.batchTimeout);
      
      this.batchTimers.set(roomId, timer);
    }
  }
  
  /**
   * 刷新同步队列
   */
  private async flushSyncQueue(roomId: string): Promise<void> {
    const queue = this.syncQueues.get(roomId);
    if (!queue || queue.length === 0) return;
    
    // 清除定时器
    const timer = this.batchTimers.get(roomId);
    if (timer) {
      clearTimeout(timer);
      this.batchTimers.delete(roomId);
    }
    
    try {
      // 创建同步批次
      const batch: SyncBatch = {
        id: this.generateBatchId(),
        roomId,
        items: [...queue],
        timestamp: Date.now(),
        size: queue.length,
        compressed: false,
      };
      
      // 压缩批次数据
      if (this.config.enableCompression) {
        batch.compressed = await this.compressBatch(batch);
      }
      
      // 应用增量同步
      if (this.config.enableDeltaSync) {
        await this.applyDeltaCompression(batch);
      }
      
      // 发送同步批次
      await this.sendSyncBatch(batch);
      
      // 清空队列
      this.syncQueues.set(roomId, []);
      
      this.logger.debug(`发送同步批次: ${batch.id}, 房间: ${roomId}, 项目数: ${batch.size}`);
      
    } catch (error) {
      this.logger.error('刷新同步队列失败:', error);
      
      // 重试机制
      setTimeout(() => {
        this.flushSyncQueue(roomId);
      }, this.config.retryDelay);
    }
  }
  
  /**
   * 发送同步批次
   */
  private async sendSyncBatch(batch: SyncBatch): Promise<void> {
    // 存储到Redis
    await this.redisClient.setex(
      `batch:${batch.id}`,
      300, // 5分钟过期
      JSON.stringify(batch)
    );
    
    // 发出同步事件
    this.eventEmitter.emit('sync.batch', {
      batchId: batch.id,
      roomId: batch.roomId,
      size: batch.size,
      compressed: batch.compressed,
    });
  }
  
  /**
   * 压缩批次数据
   */
  private async compressBatch(batch: SyncBatch): Promise<boolean> {
    try {
      const zlib = require('zlib');
      const originalSize = JSON.stringify(batch.items).length;
      
      // 压缩数据
      const compressed = zlib.gzipSync(JSON.stringify(batch.items));
      const compressedSize = compressed.length;
      
      // 如果压缩效果显著，使用压缩数据
      if (compressedSize < originalSize * 0.8) {
        (batch as any).compressedData = compressed;
        this.stats.compressionRatio = compressedSize / originalSize;
        return true;
      }
      
      return false;
      
    } catch (error) {
      this.logger.error('批次压缩失败:', error);
      return false;
    }
  }
  
  /**
   * 应用增量压缩
   */
  private async applyDeltaCompression(batch: SyncBatch): Promise<void> {
    if (!this.config.enableDeltaSync) return;
    
    try {
      const deltaItems: SyncDataItem[] = [];
      
      for (const item of batch.items) {
        if (item.operation === SyncOperationType.UPDATE) {
          const delta = await this.calculateDelta(item);
          if (delta) {
            deltaItems.push({
              ...item,
              data: delta,
            });
          } else {
            deltaItems.push(item);
          }
        } else {
          deltaItems.push(item);
        }
      }
      
      batch.items = deltaItems;
      
    } catch (error) {
      this.logger.error('增量压缩失败:', error);
    }
  }
  
  /**
   * 计算增量数据
   */
  private async calculateDelta(item: SyncDataItem): Promise<any> {
    try {
      // 获取上一个版本的数据
      const previousData = await this.getPreviousData(item.id);
      if (!previousData) return null;
      
      // 计算差异
      const delta = this.computeDifference(previousData, item.data);
      
      // 如果差异较小，返回增量数据
      if (JSON.stringify(delta).length < JSON.stringify(item.data).length * 0.5) {
        return { __delta: true, ...delta };
      }
      
      return null;
      
    } catch (error) {
      this.logger.error('计算增量数据失败:', error);
      return null;
    }
  }
  
  /**
   * 计算数据差异
   */
  private computeDifference(oldData: any, newData: any): any {
    const diff: any = {};
    
    for (const key in newData) {
      if (newData[key] !== oldData[key]) {
        diff[key] = newData[key];
      }
    }
    
    return diff;
  }
  
  /**
   * 检测冲突
   */
  private async detectConflict(objectId: string, syncItem: SyncDataItem): Promise<boolean> {
    const versionInfo = this.versionStore.get(objectId);
    
    if (!versionInfo) return false;
    
    // 检查版本冲突
    if (syncItem.version <= versionInfo.version) {
      return true;
    }
    
    // 检查时间戳冲突
    const timeDiff = Math.abs(syncItem.timestamp - versionInfo.lastModified);
    if (timeDiff < 100) { // 100ms内的操作可能冲突
      return true;
    }
    
    return false;
  }
  
  /**
   * 解决冲突
   */
  private async resolveConflict(conflictItem: SyncDataItem): Promise<void> {
    try {
      switch (this.config.conflictResolution) {
        case ConflictResolutionStrategy.LAST_WRITE_WINS:
          await this.applyLastWriteWins(conflictItem);
          break;
          
        case ConflictResolutionStrategy.FIRST_WRITE_WINS:
          // 忽略冲突项
          break;
          
        case ConflictResolutionStrategy.MERGE:
          await this.applyMergeStrategy(conflictItem);
          break;
          
        case ConflictResolutionStrategy.USER_PRIORITY:
          await this.applyUserPriorityStrategy(conflictItem);
          break;
      }
      
      this.stats.conflictsResolved++;
      
    } catch (error) {
      this.logger.error('解决冲突失败:', error);
    }
  }
  
  /**
   * 应用最后写入获胜策略
   */
  private async applyLastWriteWins(conflictItem: SyncDataItem): Promise<void> {
    // 直接应用冲突项
    const roomId = await this.getRoomIdForObject(conflictItem.id);
    if (roomId) {
      this.addToSyncQueue(roomId, conflictItem);
    }
  }
  
  /**
   * 应用合并策略
   */
  private async applyMergeStrategy(conflictItem: SyncDataItem): Promise<void> {
    // 实现智能合并逻辑
    const existingData = await this.getPreviousData(conflictItem.id);
    if (existingData) {
      const mergedData = this.mergeData(existingData, conflictItem.data);
      conflictItem.data = mergedData;
      
      const roomId = await this.getRoomIdForObject(conflictItem.id);
      if (roomId) {
        this.addToSyncQueue(roomId, conflictItem);
      }
    }
  }
  
  /**
   * 应用用户优先级策略
   */
  private async applyUserPriorityStrategy(conflictItem: SyncDataItem): Promise<void> {
    // 根据用户优先级决定是否应用更改
    // 这里可以实现基于用户权限、角色等的优先级逻辑
    const roomId = await this.getRoomIdForObject(conflictItem.id);
    if (roomId) {
      this.addToSyncQueue(roomId, conflictItem);
    }
  }

  /**
   * 合并数据
   */
  private mergeData(existingData: any, newData: any): any {
    // 简单的合并策略：深度合并对象
    return { ...existingData, ...newData };
  }
  
  /**
   * 获取下一个版本号
   */
  private async getNextVersion(objectId: string): Promise<number> {
    const versionInfo = this.versionStore.get(objectId);
    return versionInfo ? versionInfo.version + 1 : 1;
  }
  
  /**
   * 更新版本信息
   */
  private async updateVersionInfo(objectId: string, syncItem: SyncDataItem): Promise<void> {
    const versionInfo: VersionInfo = {
      objectId,
      version: syncItem.version,
      lastModified: syncItem.timestamp,
      lastModifiedBy: syncItem.userId,
      checksum: syncItem.checksum || '',
    };
    
    this.versionStore.set(objectId, versionInfo);
    
    // 同时存储到Redis
    await this.redisClient.setex(
      `version:${objectId}`,
      3600, // 1小时过期
      JSON.stringify(versionInfo)
    );
  }
  
  /**
   * 计算校验和
   */
  private calculateChecksum(data: any): string {
    const crypto = require('crypto');
    return crypto.createHash('md5').update(JSON.stringify(data)).digest('hex');
  }
  
  /**
   * 生成同步ID
   */
  private generateSyncId(): string {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 生成批次ID
   */
  private generateBatchId(): string {
    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 获取上一个版本的数据
   */
  private async getPreviousData(objectId: string): Promise<any> {
    try {
      const data = await this.redisClient.get(`data:${objectId}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      return null;
    }
  }
  
  /**
   * 获取对象所属房间ID
   */
  private async getRoomIdForObject(objectId: string): Promise<string | null> {
    try {
      return await this.redisClient.get(`object_room:${objectId}`);
    } catch (error) {
      return null;
    }
  }
  
  /**
   * 更新延迟统计
   */
  private updateLatencyStats(startTime: number): void {
    const latency = Date.now() - startTime;
    this.stats.averageLatency = 
      (this.stats.averageLatency * (this.stats.totalOperations - 1) + latency) / 
      this.stats.totalOperations;
  }
  
  /**
   * 启动统计监控
   */
  private startStatsMonitoring(): void {
    this.statsTimer = setInterval(() => {
      this.logger.debug('同步统计:', this.stats);
      
      // 发出统计事件
      this.eventEmitter.emit('sync.stats', this.stats);
    }, 30000); // 每30秒输出一次统计
  }
  
  /**
   * 启动冲突解决
   */
  private startConflictResolution(): void {
    this.conflictTimer = setInterval(() => {
      this.processConflictQueue();
    }, 1000); // 每秒处理一次冲突队列
  }
  
  /**
   * 处理冲突队列
   */
  private async processConflictQueue(): Promise<void> {
    while (this.conflictQueue.length > 0) {
      const conflictItem = this.conflictQueue.shift()!;
      await this.resolveConflict(conflictItem);
    }
  }
  
  /**
   * 获取统计信息
   */
  public getStats(): SyncStats {
    return { ...this.stats };
  }
  
  /**
   * 获取配置
   */
  public getConfig(): SyncConfig {
    return { ...this.config };
  }
  
  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<SyncConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}
