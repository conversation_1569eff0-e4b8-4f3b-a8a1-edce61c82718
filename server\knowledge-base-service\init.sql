-- 知识库服务数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS dl_knowledge_base CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE dl_knowledge_base;

-- 创建知识库表
CREATE TABLE IF NOT EXISTS knowledge_bases (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    scene_id VARCHAR(36),
    owner_id VARCHAR(36) NOT NULL,
    status ENUM('active', 'inactive', 'processing') DEFAULT 'active',
    config JSON,
    statistics JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_scene_id (scene_id),
    INDEX idx_owner_id (owner_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建文档表
CREATE TABLE IF NOT EXISTS documents (
    id VARCHAR(36) PRIMARY KEY,
    knowledge_base_id VARCHAR(36) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500),
    file_size BIGINT,
    mime_type VARCHAR(100),
    content LONGTEXT,
    summary TEXT,
    metadata JSON,
    status ENUM('uploading', 'processing', 'completed', 'failed') DEFAULT 'uploading',
    error_message TEXT,
    chunk_count INT DEFAULT 0,
    vector_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_knowledge_base_id (knowledge_base_id),
    INDEX idx_status (status),
    FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_bases(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建搜索日志表（可选）
CREATE TABLE IF NOT EXISTS search_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    knowledge_base_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36),
    query TEXT NOT NULL,
    result_count INT DEFAULT 0,
    search_time_ms INT DEFAULT 0,
    search_type VARCHAR(50) DEFAULT 'semantic',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_knowledge_base_id (knowledge_base_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入示例数据（可选）
INSERT IGNORE INTO knowledge_bases (id, name, description, owner_id, config, statistics) VALUES
('kb_example_1', '医疗知识库示例', '包含医疗设备和健康知识的综合知识库', 'user_admin', 
 '{"chunkSize": 1000, "chunkOverlap": 200, "embeddingModel": "text-embedding-ada-002", "searchThreshold": 0.7, "maxResults": 10}',
 '{"documentCount": 0, "totalSize": 0, "vectorCount": 0, "lastUpdated": "2024-01-01T00:00:00.000Z"}');

-- 创建用户和权限（如果需要）
-- CREATE USER IF NOT EXISTS 'dl_kb_user'@'%' IDENTIFIED BY 'dl_kb_password';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON dl_knowledge_base.* TO 'dl_kb_user'@'%';
-- FLUSH PRIVILEGES;
