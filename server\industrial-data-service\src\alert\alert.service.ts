import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { AlertRule, AlertCondition, AlertStatus } from './entities/alert-rule.entity';
import { AlertInstance, AlertInstanceStatus } from './entities/alert-instance.entity';
import { StorageService } from '../storage/storage.service';

@Injectable()
export class AlertService {
  private readonly logger = new Logger(AlertService.name);

  constructor(
    @InjectRepository(AlertRule)
    private alertRuleRepository: Repository<AlertRule>,
    @InjectRepository(AlertInstance)
    private alertInstanceRepository: Repository<AlertInstance>,
    private storageService: StorageService,
  ) {}

  /**
   * 创建告警规则
   */
  async createAlertRule(ruleData: Partial<AlertRule>): Promise<AlertRule> {
    try {
      const rule = this.alertRuleRepository.create(ruleData);
      const savedRule = await this.alertRuleRepository.save(rule);
      
      this.logger.log(`创建告警规则: ${savedRule.name} (${savedRule.id})`);
      return savedRule;
    } catch (error) {
      this.logger.error(`创建告警规则失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新告警规则
   */
  async updateAlertRule(id: string, updateData: Partial<AlertRule>): Promise<AlertRule> {
    try {
      const rule = await this.alertRuleRepository.findOne({ where: { id } });
      if (!rule) {
        throw new Error(`告警规则不存在: ${id}`);
      }

      Object.assign(rule, updateData);
      const updatedRule = await this.alertRuleRepository.save(rule);
      
      this.logger.log(`更新告警规则: ${updatedRule.name} (${id})`);
      return updatedRule;
    } catch (error) {
      this.logger.error(`更新告警规则失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除告警规则
   */
  async deleteAlertRule(id: string): Promise<void> {
    try {
      const rule = await this.alertRuleRepository.findOne({ where: { id } });
      if (!rule) {
        throw new Error(`告警规则不存在: ${id}`);
      }

      await this.alertRuleRepository.remove(rule);
      this.logger.log(`删除告警规则: ${rule.name} (${id})`);
    } catch (error) {
      this.logger.error(`删除告警规则失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取所有告警规则
   */
  async getAllAlertRules(): Promise<AlertRule[]> {
    return this.alertRuleRepository.find({
      order: { createdAt: 'DESC' }
    });
  }

  /**
   * 根据设备ID获取告警规则
   */
  async getAlertRulesByDevice(deviceId: string): Promise<AlertRule[]> {
    return this.alertRuleRepository.find({
      where: { deviceId, status: AlertStatus.ACTIVE },
      order: { createdAt: 'DESC' }
    });
  }

  /**
   * 评估告警规则
   */
  async evaluateAlertRule(rule: AlertRule): Promise<boolean> {
    try {
      // 获取最新数据
      const latestData = await this.storageService.getLatestData(rule.deviceId, rule.tagName);
      
      if (!latestData || latestData.length === 0) {
        // 检查是否为无数据告警
        if (rule.condition === AlertCondition.NO_DATA) {
          return this.triggerAlert(rule, null, '设备无数据');
        }
        return false;
      }

      const dataPoint = latestData[0];
      const value = dataPoint.value;

      // 根据条件评估
      const isTriggered = this.evaluateCondition(rule.condition, value, rule.thresholds);
      
      if (isTriggered) {
        return this.triggerAlert(rule, value, `值 ${value} 触发了告警条件`);
      }

      // 更新最后评估时间
      rule.lastEvaluation = new Date();
      await this.alertRuleRepository.save(rule);

      return false;
    } catch (error) {
      this.logger.error(`评估告警规则失败: ${rule.name}`, error.stack);
      return false;
    }
  }

  /**
   * 评估告警条件
   */
  private evaluateCondition(condition: AlertCondition, value: any, thresholds: any): boolean {
    switch (condition) {
      case AlertCondition.GREATER_THAN:
        return value > thresholds.value;
      case AlertCondition.LESS_THAN:
        return value < thresholds.value;
      case AlertCondition.EQUALS:
        return value === thresholds.value;
      case AlertCondition.NOT_EQUALS:
        return value !== thresholds.value;
      case AlertCondition.BETWEEN:
        return value >= thresholds.lowerValue && value <= thresholds.upperValue;
      case AlertCondition.OUTSIDE_RANGE:
        return value < thresholds.lowerValue || value > thresholds.upperValue;
      default:
        return false;
    }
  }

  /**
   * 触发告警
   */
  private async triggerAlert(rule: AlertRule, triggerValue: any, message: string): Promise<boolean> {
    try {
      // 检查是否已有未解决的告警实例
      const existingInstance = await this.alertInstanceRepository.findOne({
        where: {
          ruleId: rule.id,
          status: AlertInstanceStatus.TRIGGERED
        }
      });

      if (existingInstance) {
        // 已有未解决的告警，不重复创建
        return false;
      }

      // 创建新的告警实例
      const alertInstance = this.alertInstanceRepository.create({
        ruleId: rule.id,
        deviceId: rule.deviceId,
        tagName: rule.tagName,
        message,
        severity: rule.severity,
        status: AlertInstanceStatus.TRIGGERED,
        triggerValue,
        thresholdValue: rule.thresholds,
        triggeredAt: new Date()
      });

      await this.alertInstanceRepository.save(alertInstance);

      // 更新规则统计
      rule.lastTriggered = new Date();
      rule.triggerCount += 1;
      rule.lastEvaluation = new Date();
      await this.alertRuleRepository.save(rule);

      // 发送通知
      await this.sendNotification(rule, alertInstance);

      this.logger.warn(`告警触发: ${rule.name} - ${message}`);
      return true;
    } catch (error) {
      this.logger.error(`触发告警失败: ${rule.name}`, error.stack);
      return false;
    }
  }

  /**
   * 发送告警通知
   */
  private async sendNotification(rule: AlertRule, alertInstance: AlertInstance): Promise<void> {
    try {
      // 这里实现具体的通知逻辑
      // 可以发送邮件、短信、Webhook等
      
      if (rule.notificationChannels?.includes('email')) {
        await this.sendEmailNotification(rule, alertInstance);
      }
      
      if (rule.notificationChannels?.includes('webhook')) {
        await this.sendWebhookNotification(rule, alertInstance);
      }

      // 标记通知已发送
      alertInstance.notificationSent = true;
      alertInstance.notificationAttempts += 1;
      await this.alertInstanceRepository.save(alertInstance);

    } catch (error) {
      this.logger.error(`发送告警通知失败: ${rule.name}`, error.stack);
    }
  }

  /**
   * 发送邮件通知
   */
  private async sendEmailNotification(rule: AlertRule, alertInstance: AlertInstance): Promise<void> {
    // 模拟邮件发送
    this.logger.log(`发送邮件通知: ${rule.name} - ${alertInstance.message}`);
  }

  /**
   * 发送Webhook通知
   */
  private async sendWebhookNotification(rule: AlertRule, alertInstance: AlertInstance): Promise<void> {
    // 模拟Webhook发送
    this.logger.log(`发送Webhook通知: ${rule.name} - ${alertInstance.message}`);
  }

  /**
   * 确认告警
   */
  async acknowledgeAlert(instanceId: string, acknowledgedBy: string): Promise<AlertInstance> {
    try {
      const instance = await this.alertInstanceRepository.findOne({ where: { id: instanceId } });
      if (!instance) {
        throw new Error(`告警实例不存在: ${instanceId}`);
      }

      instance.status = AlertInstanceStatus.ACKNOWLEDGED;
      instance.acknowledgedAt = new Date();
      instance.acknowledgedBy = acknowledgedBy;

      const updatedInstance = await this.alertInstanceRepository.save(instance);
      this.logger.log(`告警已确认: ${instanceId} by ${acknowledgedBy}`);
      
      return updatedInstance;
    } catch (error) {
      this.logger.error(`确认告警失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 解决告警
   */
  async resolveAlert(instanceId: string, resolvedBy?: string): Promise<AlertInstance> {
    try {
      const instance = await this.alertInstanceRepository.findOne({ where: { id: instanceId } });
      if (!instance) {
        throw new Error(`告警实例不存在: ${instanceId}`);
      }

      instance.status = AlertInstanceStatus.RESOLVED;
      instance.resolvedAt = new Date();
      instance.resolvedBy = resolvedBy;
      instance.autoResolved = !resolvedBy;

      const updatedInstance = await this.alertInstanceRepository.save(instance);
      this.logger.log(`告警已解决: ${instanceId} ${resolvedBy ? `by ${resolvedBy}` : '(自动解决)'}`);
      
      return updatedInstance;
    } catch (error) {
      this.logger.error(`解决告警失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取活跃告警
   */
  async getActiveAlerts(): Promise<AlertInstance[]> {
    return this.alertInstanceRepository.find({
      where: { status: AlertInstanceStatus.TRIGGERED },
      relations: ['rule'],
      order: { triggeredAt: 'DESC' }
    });
  }

  /**
   * 获取告警统计
   */
  async getAlertStatistics(): Promise<any> {
    const totalRules = await this.alertRuleRepository.count();
    const activeRules = await this.alertRuleRepository.count({ where: { status: AlertStatus.ACTIVE } });
    const totalAlerts = await this.alertInstanceRepository.count();
    const activeAlerts = await this.alertInstanceRepository.count({ where: { status: AlertInstanceStatus.TRIGGERED } });

    return {
      totalRules,
      activeRules,
      totalAlerts,
      activeAlerts,
      resolvedAlerts: totalAlerts - activeAlerts
    };
  }

  /**
   * 定时评估告警规则
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async evaluateAllAlertRules(): Promise<void> {
    try {
      const activeRules = await this.alertRuleRepository.find({
        where: { status: AlertStatus.ACTIVE }
      });

      for (const rule of activeRules) {
        const now = new Date();
        const timeSinceLastEvaluation = rule.lastEvaluation 
          ? now.getTime() - rule.lastEvaluation.getTime()
          : Infinity;

        // 检查是否到了评估时间
        if (timeSinceLastEvaluation >= rule.evaluationInterval * 1000) {
          await this.evaluateAlertRule(rule);
        }
      }
    } catch (error) {
      this.logger.error(`定时评估告警规则失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 定时自动解决告警
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async autoResolveAlerts(): Promise<void> {
    try {
      const activeAlerts = await this.alertInstanceRepository.find({
        where: { status: AlertInstanceStatus.TRIGGERED },
        relations: ['rule']
      });

      for (const alert of activeAlerts) {
        if (!alert.rule.autoResolve) continue;

        const now = new Date();
        const timeSinceTriggered = now.getTime() - alert.triggeredAt.getTime();

        // 检查是否超过自动解决时间
        if (timeSinceTriggered >= alert.rule.resolveTimeout * 1000) {
          await this.resolveAlert(alert.id);
        }
      }
    } catch (error) {
      this.logger.error(`自动解决告警失败: ${error.message}`, error.stack);
    }
  }
}
