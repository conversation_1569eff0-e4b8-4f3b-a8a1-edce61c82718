import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';

export enum DeviceType {
  PLC = 'plc',
  SENSOR = 'sensor',
  ACTUATOR = 'actuator',
  ROBOT = 'robot',
  MACHINE = 'machine',
  GATEWAY = 'gateway',
  HMI = 'hmi',
  DRIVE = 'drive'
}

export enum DeviceStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  ERROR = 'error',
  MAINTENANCE = 'maintenance',
  UNKNOWN = 'unknown'
}

export enum ProtocolType {
  MODBUS_TCP = 'modbus_tcp',
  MODBUS_RTU = 'modbus_rtu',
  OPC_UA = 'opc_ua',
  MQTT = 'mqtt',
  ETHERNET_IP = 'ethernet_ip',
  PROFINET = 'profinet',
  HTTP = 'http',
  SERIAL = 'serial'
}

@Entity('devices')
export class Device {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  name: string;

  @Column({ length: 500, nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: DeviceType,
    default: DeviceType.SENSOR
  })
  type: DeviceType;

  @Column({
    type: 'enum',
    enum: DeviceStatus,
    default: DeviceStatus.OFFLINE
  })
  status: DeviceStatus;

  @Column({
    type: 'enum',
    enum: ProtocolType,
    default: ProtocolType.MODBUS_TCP
  })
  protocol: ProtocolType;

  @Column({ length: 100 })
  ipAddress: string;

  @Column({ type: 'int', default: 502 })
  port: number;

  @Column({ type: 'int', nullable: true })
  slaveId: number;

  @Column({ type: 'json', nullable: true })
  configuration: any;

  @Column({ type: 'json', nullable: true })
  tags: any;

  @Column({ length: 100, nullable: true })
  location: string;

  @Column({ length: 100, nullable: true })
  manufacturer: string;

  @Column({ length: 100, nullable: true })
  model: string;

  @Column({ length: 100, nullable: true })
  serialNumber: string;

  @Column({ type: 'timestamp', nullable: true })
  lastConnected: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastDataReceived: Date;

  @Column({ type: 'int', default: 0 })
  errorCount: number;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @OneToMany('DeviceDataPoint', 'device')
  dataPoints: any[];
}
