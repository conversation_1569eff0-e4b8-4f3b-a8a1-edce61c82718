import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions } from 'typeorm';
import { SystemAlert, AlertLevel, AlertStatus, AlertType } from './entities/system-alert.entity';

/**
 * 警告服务
 */
@Injectable()
export class AlertService {
  private readonly logger = new Logger(AlertService.name);

  constructor(
    @InjectRepository(SystemAlert)
    private readonly alertRepository: Repository<SystemAlert>,
  ) {}

  /**
   * 获取警告列表
   */
  async getAlerts(options: {
    level?: string;
    status?: string;
    alertType?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ alerts: SystemAlert[]; total: number }> {
    try {
      const { level, status, alertType, limit = 20, offset = 0 } = options;

      const queryOptions: FindManyOptions<SystemAlert> = {
        take: limit,
        skip: offset,
        order: { timestamp: 'DESC' },
      };

      if (level || status || alertType) {
        queryOptions.where = {};
        if (level) {
          queryOptions.where.level = level as AlertLevel;
        }
        if (status) {
          queryOptions.where.status = status as AlertStatus;
        }
        if (alertType) {
          queryOptions.where.alertType = alertType as AlertType;
        }
      }

      const [alerts, total] = await this.alertRepository.findAndCount(queryOptions);

      return { alerts, total };
    } catch (error) {
      this.logger.error(`获取警告列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 创建警告
   */
  async createAlert(alertData: {
    title: string;
    message: string;
    level: AlertLevel;
    alertType: AlertType;
    source: string;
    sourceId?: string;
    relatedMetrics?: any[];
    impactScope?: any;
    recommendedActions?: string[];
  }): Promise<SystemAlert> {
    try {
      const alert = this.alertRepository.create({
        alertId: `ALERT_${Date.now()}`,
        ...alertData,
        timestamp: new Date(),
        firstOccurrence: new Date(),
        lastOccurrence: new Date(),
        status: AlertStatus.ACTIVE,
      });

      const savedAlert = await this.alertRepository.save(alert);
      this.logger.log(`警告创建成功: ${savedAlert.alertId}`);

      // 发送通知
      await this.sendAlertNotification(savedAlert);

      return savedAlert;
    } catch (error) {
      this.logger.error(`创建警告失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 确认警告
   */
  async acknowledgeAlert(id: string, acknowledgedBy: string, notes?: string): Promise<void> {
    try {
      const alert = await this.alertRepository.findOne({ where: { id } });

      if (!alert) {
        throw new NotFoundException(`警告不存在: ${id}`);
      }

      alert.status = AlertStatus.ACKNOWLEDGED;
      alert.acknowledgedBy = acknowledgedBy;
      alert.acknowledgedAt = new Date();

      if (notes) {
        alert.metadata = { ...alert.metadata, acknowledgmentNotes: notes };
      }

      await this.alertRepository.save(alert);
      this.logger.log(`警告确认成功: ${alert.alertId}`);
    } catch (error) {
      this.logger.error(`确认警告失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 解决警告
   */
  async resolveAlert(id: string, resolvedBy: string, resolutionNotes?: string): Promise<void> {
    try {
      const alert = await this.alertRepository.findOne({ where: { id } });

      if (!alert) {
        throw new NotFoundException(`警告不存在: ${id}`);
      }

      alert.status = AlertStatus.RESOLVED;
      alert.resolvedBy = resolvedBy;
      alert.resolutionTime = new Date();
      alert.resolutionNotes = resolutionNotes;

      await this.alertRepository.save(alert);
      this.logger.log(`警告解决成功: ${alert.alertId}`);
    } catch (error) {
      this.logger.error(`解决警告失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 批量处理警告
   */
  async batchProcessAlerts(alertIds: string[], action: string, operator: string): Promise<void> {
    try {
      const alerts = await this.alertRepository.findByIds(alertIds);

      for (const alert of alerts) {
        switch (action) {
          case 'acknowledge':
            alert.status = AlertStatus.ACKNOWLEDGED;
            alert.acknowledgedBy = operator;
            alert.acknowledgedAt = new Date();
            break;
          case 'resolve':
            alert.status = AlertStatus.RESOLVED;
            alert.resolvedBy = operator;
            alert.resolutionTime = new Date();
            break;
          case 'suppress':
            alert.status = AlertStatus.SUPPRESSED;
            break;
        }
      }

      await this.alertRepository.save(alerts);
      this.logger.log(`批量处理警告成功: ${alertIds.length} 个警告`);
    } catch (error) {
      this.logger.error(`批量处理警告失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取警告统计
   */
  async getAlertStatistics(period: string = 'week'): Promise<any> {
    try {
      const now = new Date();
      let startDate: Date;

      switch (period) {
        case 'day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      }

      const alerts = await this.alertRepository.find({
        where: {
          timestamp: {
            $gte: startDate,
          } as any,
        },
      });

      const statistics = {
        total: alerts.length,
        byLevel: this.groupByLevel(alerts),
        byStatus: this.groupByStatus(alerts),
        byType: this.groupByType(alerts),
        bySource: this.groupBySource(alerts),
        trends: this.calculateAlertTrends(alerts),
        topSources: this.getTopAlertSources(alerts),
        resolutionMetrics: this.calculateResolutionMetrics(alerts),
      };

      return statistics;
    } catch (error) {
      this.logger.error(`获取警告统计失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 发送警告通知
   */
  private async sendAlertNotification(alert: SystemAlert): Promise<void> {
    try {
      // 根据警告级别决定通知方式
      const notificationMethods = this.getNotificationMethods(alert.level);

      for (const method of notificationMethods) {
        await this.sendNotification(method, alert);
      }

      alert.notificationSent = true;
      await this.alertRepository.save(alert);
    } catch (error) {
      this.logger.error(`发送警告通知失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取通知方式
   */
  private getNotificationMethods(level: AlertLevel): string[] {
    switch (level) {
      case AlertLevel.CRITICAL:
        return ['email', 'sms', 'webhook'];
      case AlertLevel.ERROR:
        return ['email', 'webhook'];
      case AlertLevel.WARNING:
        return ['webhook'];
      case AlertLevel.INFO:
        return [];
      default:
        return [];
    }
  }

  /**
   * 发送通知
   */
  private async sendNotification(method: string, alert: SystemAlert): Promise<void> {
    // 模拟发送通知
    this.logger.log(`发送${method}通知: ${alert.title}`);
  }

  /**
   * 按级别分组
   */
  private groupByLevel(alerts: SystemAlert[]): any {
    return alerts.reduce((acc, alert) => {
      acc[alert.level] = (acc[alert.level] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * 按状态分组
   */
  private groupByStatus(alerts: SystemAlert[]): any {
    return alerts.reduce((acc, alert) => {
      acc[alert.status] = (acc[alert.status] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * 按类型分组
   */
  private groupByType(alerts: SystemAlert[]): any {
    return alerts.reduce((acc, alert) => {
      acc[alert.alertType] = (acc[alert.alertType] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * 按来源分组
   */
  private groupBySource(alerts: SystemAlert[]): any {
    return alerts.reduce((acc, alert) => {
      acc[alert.source] = (acc[alert.source] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * 计算警告趋势
   */
  private calculateAlertTrends(alerts: SystemAlert[]): any {
    // 简化的趋势计算
    return {
      trend: alerts.length > 10 ? 'increasing' : 'stable',
      changePercent: Math.random() * 20 - 10,
    };
  }

  /**
   * 获取主要警告来源
   */
  private getTopAlertSources(alerts: SystemAlert[]): any[] {
    const sources = this.groupBySource(alerts);
    return Object.entries(sources)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, 5)
      .map(([source, count]) => ({ source, count }));
  }

  /**
   * 计算解决指标
   */
  private calculateResolutionMetrics(alerts: SystemAlert[]): any {
    const resolvedAlerts = alerts.filter(a => a.status === AlertStatus.RESOLVED);
    
    if (resolvedAlerts.length === 0) {
      return { averageResolutionTime: 0, resolutionRate: 0 };
    }

    const totalResolutionTime = resolvedAlerts.reduce((sum, alert) => {
      if (alert.resolutionTime && alert.timestamp) {
        return sum + (alert.resolutionTime.getTime() - alert.timestamp.getTime());
      }
      return sum;
    }, 0);

    return {
      averageResolutionTime: totalResolutionTime / resolvedAlerts.length / (1000 * 60), // 分钟
      resolutionRate: resolvedAlerts.length / alerts.length,
    };
  }
}
