import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// 实体
import { Resource } from './entities/resource.entity';
import { ResourceAllocation } from './entities/resource-allocation.entity';
import { ResourceCapacity } from './entities/resource-capacity.entity';

// 控制器
import { ResourceController } from './resource.controller';

// 服务
import { ResourceManagementService } from './resource-management.service';
import { ResourceOptimizationService } from './resource-optimization.service';
import { ResourceMonitoringService } from './resource-monitoring.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Resource,
      ResourceAllocation,
      ResourceCapacity,
    ]),
  ],

  controllers: [ResourceController],

  providers: [
    ResourceManagementService,
    ResourceOptimizationService,
    ResourceMonitoringService,
  ],

  exports: [
    ResourceManagementService,
    ResourceOptimizationService,
    ResourceMonitoringService,
  ],
})
export class ResourceModule {}
