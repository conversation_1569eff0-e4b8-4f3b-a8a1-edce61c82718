/**
 * 健康检查控制器
 * 
 * 提供服务健康状态检查接口
 */

import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  HealthCheckService,
  HealthCheck,
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from '@nestjs/terminus';

import { HealthService } from './health.service';

@ApiTags('健康检查')
@Controller('api/v1/health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly db: TypeOrmHealthIndicator,
    private readonly memory: MemoryHealthIndicator,
    private readonly disk: DiskHealthIndicator,
    private readonly healthService: HealthService,
  ) {}

  /**
   * 基础健康检查
   */
  @Get()
  @ApiOperation({ summary: '基础健康检查' })
  @ApiResponse({ status: 200, description: '服务健康' })
  @ApiResponse({ status: 503, description: '服务不健康' })
  @HealthCheck()
  check() {
    return this.health.check([
      // 数据库健康检查
      () => this.db.pingCheck('database'),
      
      // 内存使用检查
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
      
      // 磁盘空间检查
      () => this.disk.checkStorage('storage', {
        path: '/',
        thresholdPercent: 0.9,
      }),
    ]);
  }

  /**
   * 详细健康检查
   */
  @Get('detailed')
  @ApiOperation({ summary: '详细健康检查' })
  @ApiResponse({ status: 200, description: '详细健康信息' })
  async getDetailedHealth() {
    return this.healthService.getDetailedHealthInfo();
  }

  /**
   * 服务状态检查
   */
  @Get('status')
  @ApiOperation({ summary: '服务状态检查' })
  @ApiResponse({ status: 200, description: '服务状态信息' })
  async getServiceStatus() {
    return this.healthService.getServiceStatus();
  }

  /**
   * 依赖服务检查
   */
  @Get('dependencies')
  @ApiOperation({ summary: '依赖服务检查' })
  @ApiResponse({ status: 200, description: '依赖服务状态' })
  async checkDependencies() {
    return this.healthService.checkDependencies();
  }
}
