import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 质量检验状态枚举
 */
export enum InspectionStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

/**
 * 检验结果枚举
 */
export enum InspectionResult {
  PASS = 'pass',
  FAIL = 'fail',
  REWORK = 'rework',
  SCRAP = 'scrap'
}

/**
 * 检验类型枚举
 */
export enum InspectionType {
  INCOMING = 'incoming',        // 来料检验
  IN_PROCESS = 'in_process',    // 过程检验
  FINAL = 'final',              // 最终检验
  PATROL = 'patrol',            // 巡检
  SPECIAL = 'special'           // 特殊检验
}

/**
 * 质量检验实体
 */
@Entity('quality_inspections')
export class QualityInspection {
  @ApiProperty({ description: '检验ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '检验单号' })
  @Column({ name: 'inspection_number', length: 50, unique: true })
  inspectionNumber: string;

  @ApiProperty({ description: '检验类型', enum: InspectionType })
  @Column({ name: 'inspection_type', type: 'enum', enum: InspectionType })
  inspectionType: InspectionType;

  @ApiProperty({ description: '生产订单ID' })
  @Column({ name: 'production_order_id', nullable: true })
  productionOrderId: string;

  @ApiProperty({ description: '产品编码' })
  @Column({ name: 'product_code', length: 100 })
  productCode: string;

  @ApiProperty({ description: '产品名称' })
  @Column({ name: 'product_name', length: 200 })
  productName: string;

  @ApiProperty({ description: '批次号' })
  @Column({ name: 'batch_number', length: 100 })
  batchNumber: string;

  @ApiProperty({ description: '检验数量' })
  @Column({ name: 'inspection_quantity', type: 'int' })
  inspectionQuantity: number;

  @ApiProperty({ description: '合格数量' })
  @Column({ name: 'qualified_quantity', type: 'int', default: 0 })
  qualifiedQuantity: number;

  @ApiProperty({ description: '不合格数量' })
  @Column({ name: 'defective_quantity', type: 'int', default: 0 })
  defectiveQuantity: number;

  @ApiProperty({ description: '返工数量' })
  @Column({ name: 'rework_quantity', type: 'int', default: 0 })
  reworkQuantity: number;

  @ApiProperty({ description: '报废数量' })
  @Column({ name: 'scrap_quantity', type: 'int', default: 0 })
  scrapQuantity: number;

  @ApiProperty({ description: '检验状态', enum: InspectionStatus })
  @Column({ type: 'enum', enum: InspectionStatus, default: InspectionStatus.PENDING })
  status: InspectionStatus;

  @ApiProperty({ description: '检验结果', enum: InspectionResult })
  @Column({ name: 'inspection_result', type: 'enum', enum: InspectionResult, nullable: true })
  inspectionResult: InspectionResult;

  @ApiProperty({ description: '检验标准' })
  @Column({ name: 'inspection_standard', type: 'json', nullable: true })
  inspectionStandard: any;

  @ApiProperty({ description: '检验项目' })
  @Column({ name: 'inspection_items', type: 'json' })
  inspectionItems: any[];

  @ApiProperty({ description: '检验数据' })
  @Column({ name: 'inspection_data', type: 'json', nullable: true })
  inspectionData: any;

  @ApiProperty({ description: '不合格项' })
  @Column({ name: 'defect_items', type: 'json', nullable: true })
  defectItems: any[];

  @ApiProperty({ description: '检验员' })
  @Column({ name: 'inspector', length: 100 })
  inspector: string;

  @ApiProperty({ description: '检验设备' })
  @Column({ name: 'inspection_equipment', length: 200, nullable: true })
  inspectionEquipment: string;

  @ApiProperty({ description: '检验环境' })
  @Column({ name: 'inspection_environment', type: 'json', nullable: true })
  inspectionEnvironment: any;

  @ApiProperty({ description: '计划检验时间' })
  @Column({ name: 'planned_inspection_time', type: 'datetime' })
  plannedInspectionTime: Date;

  @ApiProperty({ description: '实际开始时间' })
  @Column({ name: 'actual_start_time', type: 'datetime', nullable: true })
  actualStartTime: Date;

  @ApiProperty({ description: '实际完成时间' })
  @Column({ name: 'actual_end_time', type: 'datetime', nullable: true })
  actualEndTime: Date;

  @ApiProperty({ description: '检验备注' })
  @Column({ type: 'text', nullable: true })
  remarks: string;

  @ApiProperty({ description: '附件信息' })
  @Column({ type: 'json', nullable: true })
  attachments: any[];

  @ApiProperty({ description: '创建人' })
  @Column({ name: 'created_by', length: 100 })
  createdBy: string;

  @ApiProperty({ description: '更新人' })
  @Column({ name: 'updated_by', length: 100, nullable: true })
  updatedBy: string;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 计算属性
  get qualityRate(): number {
    return this.inspectionQuantity > 0 ? (this.qualifiedQuantity / this.inspectionQuantity) * 100 : 0;
  }

  get defectRate(): number {
    return this.inspectionQuantity > 0 ? (this.defectiveQuantity / this.inspectionQuantity) * 100 : 0;
  }

  get isCompleted(): boolean {
    return this.status === InspectionStatus.COMPLETED;
  }

  get isOverdue(): boolean {
    if (!this.plannedInspectionTime || this.isCompleted) return false;
    return new Date() > this.plannedInspectionTime;
  }

  get inspectionDuration(): number {
    if (!this.actualStartTime || !this.actualEndTime) return 0;
    return this.actualEndTime.getTime() - this.actualStartTime.getTime();
  }
}

/**
 * 质量控制计划实体
 */
@Entity('quality_control_plans')
export class QualityControlPlan {
  @ApiProperty({ description: '质控计划ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '计划编号' })
  @Column({ name: 'plan_number', length: 50, unique: true })
  planNumber: string;

  @ApiProperty({ description: '计划名称' })
  @Column({ name: 'plan_name', length: 200 })
  planName: string;

  @ApiProperty({ description: '产品编码' })
  @Column({ name: 'product_code', length: 100 })
  productCode: string;

  @ApiProperty({ description: '产品名称' })
  @Column({ name: 'product_name', length: 200 })
  productName: string;

  @ApiProperty({ description: '工艺路线ID' })
  @Column({ name: 'process_route_id', nullable: true })
  processRouteId: string;

  @ApiProperty({ description: '质量标准' })
  @Column({ name: 'quality_standards', type: 'json' })
  qualityStandards: any[];

  @ApiProperty({ description: '检验计划' })
  @Column({ name: 'inspection_plans', type: 'json' })
  inspectionPlans: any[];

  @ApiProperty({ description: '控制点' })
  @Column({ name: 'control_points', type: 'json' })
  controlPoints: any[];

  @ApiProperty({ description: '抽样方案' })
  @Column({ name: 'sampling_plan', type: 'json', nullable: true })
  samplingPlan: any;

  @ApiProperty({ description: '是否启用' })
  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @ApiProperty({ description: '生效日期' })
  @Column({ name: 'effective_date', type: 'date' })
  effectiveDate: Date;

  @ApiProperty({ description: '失效日期' })
  @Column({ name: 'expiry_date', type: 'date', nullable: true })
  expiryDate: Date;

  @ApiProperty({ description: '创建人' })
  @Column({ name: 'created_by', length: 100 })
  createdBy: string;

  @ApiProperty({ description: '更新人' })
  @Column({ name: 'updated_by', length: 100, nullable: true })
  updatedBy: string;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 计算属性
  get isCurrentlyActive(): boolean {
    const now = new Date();
    return this.isActive && 
           this.effectiveDate <= now && 
           (!this.expiryDate || this.expiryDate >= now);
  }
}
