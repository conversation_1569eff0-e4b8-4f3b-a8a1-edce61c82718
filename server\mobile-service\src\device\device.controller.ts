/**
 * 设备管理控制器
 * 
 * 提供移动设备管理的API接口
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Headers,
  HttpStatus,
  HttpException,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiHeader,
  ApiBearerAuth,
} from '@nestjs/swagger';

import { DeviceService } from './device.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

/**
 * 设备注册DTO
 */
export class DeviceRegistrationDto {
  deviceId: string;
  deviceName: string;
  platform: string;
  version: string;
  capabilities: string[];
  deviceInfo?: {
    model?: string;
    osVersion?: string;
    appVersion?: string;
    screenSize?: string;
    language?: string;
    timezone?: string;
  };
  settings?: {
    autoSync?: boolean;
    syncInterval?: number;
    wifiOnly?: boolean;
    backgroundSync?: boolean;
  };
}

/**
 * 设备更新DTO
 */
export class DeviceUpdateDto {
  deviceName?: string;
  version?: string;
  capabilities?: string[];
  deviceInfo?: any;
  settings?: any;
}

/**
 * 设备查询DTO
 */
export class DeviceQueryDto {
  platform?: string;
  isOnline?: boolean;
  isActive?: boolean;
  limit?: number = 50;
  offset?: number = 0;
}

@ApiTags('设备管理')
@Controller('api/mobile/devices')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DeviceController {
  private readonly logger = new Logger(DeviceController.name);

  constructor(private readonly deviceService: DeviceService) {}

  /**
   * 注册设备
   */
  @Post('register')
  @ApiOperation({ summary: '注册移动设备' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiResponse({ status: 201, description: '设备注册成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async registerDevice(
    @Body() registrationDto: DeviceRegistrationDto,
    @Headers('X-User-ID') userId: string
  ) {
    try {
      if (!userId) {
        throw new HttpException('缺少用户ID', HttpStatus.BAD_REQUEST);
      }

      const device = await this.deviceService.registerDevice(userId, registrationDto);

      this.logger.log(`用户 ${userId} 注册了设备 ${registrationDto.deviceId}`);

      return {
        success: true,
        data: device,
        message: '设备注册成功'
      };
    } catch (error) {
      this.logger.error('注册设备失败:', error);
      throw new HttpException(
        error.message || '注册设备失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取用户设备列表
   */
  @Get()
  @ApiOperation({ summary: '获取用户设备列表' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiQuery({ name: 'platform', required: false, description: '平台过滤' })
  @ApiQuery({ name: 'isOnline', required: false, description: '在线状态过滤' })
  @ApiQuery({ name: 'isActive', required: false, description: '激活状态过滤' })
  @ApiQuery({ name: 'limit', required: false, description: '限制数量' })
  @ApiQuery({ name: 'offset', required: false, description: '偏移量' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUserDevices(
    @Headers('X-User-ID') userId: string,
    @Query() queryDto: DeviceQueryDto
  ) {
    try {
      if (!userId) {
        throw new HttpException('缺少用户ID', HttpStatus.BAD_REQUEST);
      }

      const result = await this.deviceService.getUserDevices(userId, queryDto);

      return {
        success: true,
        data: result,
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error('获取设备列表失败:', error);
      throw new HttpException(
        error.message || '获取设备列表失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取设备详情
   */
  @Get(':deviceId')
  @ApiOperation({ summary: '获取设备详情' })
  @ApiParam({ name: 'deviceId', description: '设备ID' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '设备不存在' })
  async getDeviceDetails(
    @Param('deviceId') deviceId: string,
    @Headers('X-User-ID') userId: string
  ) {
    try {
      if (!userId) {
        throw new HttpException('缺少用户ID', HttpStatus.BAD_REQUEST);
      }

      const device = await this.deviceService.getDeviceDetails(userId, deviceId);

      return {
        success: true,
        data: device,
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error('获取设备详情失败:', error);
      throw new HttpException(
        error.message || '获取设备详情失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 更新设备信息
   */
  @Put(':deviceId')
  @ApiOperation({ summary: '更新设备信息' })
  @ApiParam({ name: 'deviceId', description: '设备ID' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '设备不存在' })
  async updateDevice(
    @Param('deviceId') deviceId: string,
    @Headers('X-User-ID') userId: string,
    @Body() updateDto: DeviceUpdateDto
  ) {
    try {
      if (!userId) {
        throw new HttpException('缺少用户ID', HttpStatus.BAD_REQUEST);
      }

      const device = await this.deviceService.updateDevice(userId, deviceId, updateDto);

      this.logger.log(`用户 ${userId} 更新了设备 ${deviceId} 的信息`);

      return {
        success: true,
        data: device,
        message: '设备信息更新成功'
      };
    } catch (error) {
      this.logger.error('更新设备信息失败:', error);
      throw new HttpException(
        error.message || '更新设备信息失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 删除设备
   */
  @Delete(':deviceId')
  @ApiOperation({ summary: '删除设备' })
  @ApiParam({ name: 'deviceId', description: '设备ID' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '设备不存在' })
  async deleteDevice(
    @Param('deviceId') deviceId: string,
    @Headers('X-User-ID') userId: string
  ) {
    try {
      if (!userId) {
        throw new HttpException('缺少用户ID', HttpStatus.BAD_REQUEST);
      }

      await this.deviceService.deleteDevice(userId, deviceId);

      this.logger.log(`用户 ${userId} 删除了设备 ${deviceId}`);

      return {
        success: true,
        message: '设备删除成功'
      };
    } catch (error) {
      this.logger.error('删除设备失败:', error);
      throw new HttpException(
        error.message || '删除设备失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 设置设备在线状态
   */
  @Put(':deviceId/online')
  @ApiOperation({ summary: '设置设备在线状态' })
  @ApiParam({ name: 'deviceId', description: '设备ID' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiResponse({ status: 200, description: '状态更新成功' })
  async setDeviceOnlineStatus(
    @Param('deviceId') deviceId: string,
    @Headers('X-User-ID') userId: string,
    @Body() body: { isOnline: boolean }
  ) {
    try {
      if (!userId) {
        throw new HttpException('缺少用户ID', HttpStatus.BAD_REQUEST);
      }

      await this.deviceService.setDeviceOnlineStatus(userId, deviceId, body.isOnline);

      return {
        success: true,
        message: '设备状态更新成功'
      };
    } catch (error) {
      this.logger.error('设置设备状态失败:', error);
      throw new HttpException(
        error.message || '设置设备状态失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取设备统计信息
   */
  @Get(':deviceId/stats')
  @ApiOperation({ summary: '获取设备统计信息' })
  @ApiParam({ name: 'deviceId', description: '设备ID' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getDeviceStats(
    @Param('deviceId') deviceId: string,
    @Headers('X-User-ID') userId: string
  ) {
    try {
      if (!userId) {
        throw new HttpException('缺少用户ID', HttpStatus.BAD_REQUEST);
      }

      const stats = await this.deviceService.getDeviceStats(userId, deviceId);

      return {
        success: true,
        data: stats,
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error('获取设备统计失败:', error);
      throw new HttpException(
        error.message || '获取设备统计失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
