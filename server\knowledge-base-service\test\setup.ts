/**
 * 测试环境设置
 */
import { ConfigModule } from '@nestjs/config';

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '3306';
process.env.DB_USERNAME = 'test';
process.env.DB_PASSWORD = 'test';
process.env.DB_DATABASE = 'dl_knowledge_base_test';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';
process.env.CHROMA_URL = 'http://localhost:8000';
process.env.JWT_SECRET = 'test-secret';

// 设置测试超时时间
jest.setTimeout(30000);
