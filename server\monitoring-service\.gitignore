# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/
*.tsbuildinfo

# 环境配置文件
.env
.env.local
.env.development
.env.test
.env.production

# 日志文件
logs/
*.log

# 运行时文件
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
.nyc_output

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
tmp/
temp/

# 数据库文件
*.sqlite
*.db

# 备份文件
*.bak
*.backup

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Docker文件
.dockerignore

# 测试文件
test-results/
junit.xml
