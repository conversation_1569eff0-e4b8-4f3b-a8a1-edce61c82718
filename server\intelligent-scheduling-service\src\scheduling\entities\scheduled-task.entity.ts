import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ProductionTask } from './production-task.entity';
import { SchedulingSolution } from './scheduling-solution.entity';

/**
 * 调度状态枚举
 */
export enum ScheduledTaskStatus {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  DELAYED = 'delayed',
  CANCELLED = 'cancelled',
  PAUSED = 'paused',
}

/**
 * 已调度任务实体
 */
@Entity('scheduled_tasks')
@Index(['status', 'startTime'])
@Index(['solutionId', 'taskId'])
@Index(['startTime', 'endTime'])
export class ScheduledTask {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'task_id', length: 100 })
  @Index()
  taskId: string;

  @Column({ name: 'solution_id', type: 'uuid' })
  solutionId: string;

  @Column({ name: 'production_task_id', type: 'uuid' })
  productionTaskId: string;

  @Column({
    type: 'enum',
    enum: ScheduledTaskStatus,
    default: ScheduledTaskStatus.SCHEDULED,
  })
  status: ScheduledTaskStatus;

  @Column({ name: 'start_time', type: 'datetime' })
  startTime: Date;

  @Column({ name: 'end_time', type: 'datetime' })
  endTime: Date;

  @Column({ name: 'planned_start_time', type: 'datetime' })
  plannedStartTime: Date;

  @Column({ name: 'planned_end_time', type: 'datetime' })
  plannedEndTime: Date;

  @Column({ name: 'actual_start_time', type: 'datetime', nullable: true })
  actualStartTime: Date;

  @Column({ name: 'actual_end_time', type: 'datetime', nullable: true })
  actualEndTime: Date;

  @Column({ name: 'planned_duration', type: 'int', comment: '计划时长(分钟)' })
  plannedDuration: number;

  @Column({ name: 'actual_duration', type: 'int', nullable: true, comment: '实际时长(分钟)' })
  actualDuration: number;

  @Column({ name: 'assigned_resources', type: 'json', comment: '分配的资源' })
  assignedResources: {
    resourceId: string;
    resourceType: string;
    quantity: number;
    startTime: Date;
    endTime: Date;
  }[];

  @Column({ name: 'resource_conflicts', type: 'json', nullable: true, comment: '资源冲突' })
  resourceConflicts: string[];

  @Column({ type: 'int', default: 0, comment: '延迟时间(分钟)' })
  delay: number;

  @Column({ name: 'delay_reason', type: 'text', nullable: true, comment: '延迟原因' })
  delayReason: string;

  @Column({ name: 'completion_rate', type: 'decimal', precision: 5, scale: 2, default: 0, comment: '完成率(%)' })
  completionRate: number;

  @Column({ name: 'quality_score', type: 'decimal', precision: 5, scale: 2, nullable: true, comment: '质量评分' })
  qualityScore: number;

  @Column({ name: 'cost_actual', type: 'decimal', precision: 10, scale: 2, nullable: true, comment: '实际成本' })
  costActual: number;

  @Column({ name: 'energy_actual', type: 'decimal', precision: 10, scale: 2, nullable: true, comment: '实际能耗' })
  energyActual: number;

  @Column({ name: 'worker_assignments', type: 'json', nullable: true, comment: '工人分配' })
  workerAssignments: {
    workerId: string;
    workerName: string;
    skillLevel: number;
    startTime: Date;
    endTime: Date;
  }[];

  @Column({ name: 'machine_assignments', type: 'json', nullable: true, comment: '设备分配' })
  machineAssignments: {
    machineId: string;
    machineName: string;
    startTime: Date;
    endTime: Date;
    setupTime: number;
    teardownTime: number;
  }[];

  @Column({ name: 'material_consumption', type: 'json', nullable: true, comment: '物料消耗' })
  materialConsumption: {
    materialId: string;
    materialName: string;
    plannedQuantity: number;
    actualQuantity: number;
    unit: string;
  }[];

  @Column({ name: 'execution_notes', type: 'text', nullable: true, comment: '执行备注' })
  executionNotes: string;

  @Column({ type: 'json', nullable: true, comment: '扩展属性' })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => ProductionTask, task => task.scheduledTasks)
  @JoinColumn({ name: 'production_task_id' })
  productionTask: ProductionTask;

  @ManyToOne(() => SchedulingSolution, solution => solution.scheduledTasks)
  @JoinColumn({ name: 'solution_id' })
  solution: SchedulingSolution;
}
