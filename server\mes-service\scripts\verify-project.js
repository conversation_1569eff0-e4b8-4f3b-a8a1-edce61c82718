#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 MES服务项目结构验证');
console.log('=' .repeat(50));

// 必需的文件和目录
const requiredStructure = [
  // 根文件
  'package.json',
  'tsconfig.json',
  '.env',
  'README.md',
  'Dockerfile',
  '.gitignore',
  '.dockerignore',
  
  // 源码目录
  'src/main.ts',
  'src/app.module.ts',
  
  // 订单模块
  'src/order/order.module.ts',
  'src/order/order.controller.ts',
  'src/order/order.service.ts',
  'src/order/dto/order.dto.ts',
  'src/order/entities/production-order.entity.ts',
  
  // 工艺模块
  'src/process/process.module.ts',
  'src/process/process.controller.ts',
  'src/process/process.service.ts',
  'src/process/dto/process.dto.ts',
  'src/process/entities/process-route.entity.ts',
  
  // 质量模块
  'src/quality/quality.module.ts',
  'src/quality/quality.controller.ts',
  'src/quality/quality.service.ts',
  'src/quality/entities/quality-inspection.entity.ts',
  
  // 库存模块
  'src/inventory/inventory.module.ts',
  'src/inventory/inventory.controller.ts',
  'src/inventory/inventory.service.ts',
  'src/inventory/entities/inventory.entity.ts',
  
  // 调度模块
  'src/scheduling/scheduling.module.ts',
  'src/scheduling/scheduling.controller.ts',
  'src/scheduling/scheduling.service.ts',
  
  // 跟踪模块
  'src/tracking/tracking.module.ts',
  'src/tracking/tracking.controller.ts',
  'src/tracking/tracking.service.ts',
  
  // 报表模块
  'src/report/report.module.ts',
  'src/report/report.controller.ts',
  'src/report/report.service.ts',
  
  // WebSocket模块
  'src/websocket/websocket.module.ts',
  'src/websocket/websocket.gateway.ts',
  'src/websocket/websocket.service.ts',
  
  // 健康检查模块
  'src/health/health.module.ts',
  'src/health/health.controller.ts',
  'src/health/health.service.ts',
  
  // 公共组件
  'src/common/filters/http-exception.filter.ts',
  'src/common/interceptors/logging.interceptor.ts',
  'src/common/utils/response.util.ts',
  
  // 脚本
  'scripts/verify-project.js',
];

let allValid = true;
let validCount = 0;
let totalCount = requiredStructure.length;

console.log('📁 检查文件和目录结构:');
console.log('-'.repeat(50));

requiredStructure.forEach(filePath => {
  const fullPath = path.join(__dirname, '..', filePath);
  const exists = fs.existsSync(fullPath);
  
  if (exists) {
    console.log(`✅ ${filePath}`);
    validCount++;
  } else {
    console.log(`❌ ${filePath} - 文件不存在`);
    allValid = false;
  }
});

console.log('\n' + '-'.repeat(50));
console.log(`📊 验证结果: ${validCount}/${totalCount} 文件存在`);

// 检查package.json内容
console.log('\n📦 检查 package.json 配置:');
console.log('-'.repeat(50));

try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
  
  const requiredDependencies = [
    '@nestjs/common',
    '@nestjs/core',
    '@nestjs/platform-express',
    '@nestjs/websockets',
    '@nestjs/platform-socket.io',
    '@nestjs/typeorm',
    '@nestjs/config',
    '@nestjs/schedule',
    '@nestjs/swagger',
    'typeorm',
    'mysql2',
    'redis',
    'socket.io',
    'class-validator',
    'class-transformer',
  ];
  
  let depsValid = true;
  requiredDependencies.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} - 依赖缺失`);
      depsValid = false;
      allValid = false;
    }
  });
  
  if (depsValid) {
    console.log('✅ 所有必需依赖都已配置');
  }
  
} catch (error) {
  console.log('❌ package.json 读取失败:', error.message);
  allValid = false;
}

// 检查TypeScript配置
console.log('\n🔧 检查 TypeScript 配置:');
console.log('-'.repeat(50));

try {
  const tsConfig = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'tsconfig.json'), 'utf8'));
  
  if (tsConfig.compilerOptions) {
    console.log('✅ TypeScript 编译选项已配置');
    console.log(`   - target: ${tsConfig.compilerOptions.target}`);
    console.log(`   - module: ${tsConfig.compilerOptions.module}`);
    console.log(`   - outDir: ${tsConfig.compilerOptions.outDir}`);
  } else {
    console.log('❌ TypeScript 编译选项缺失');
    allValid = false;
  }
  
} catch (error) {
  console.log('❌ tsconfig.json 读取失败:', error.message);
  allValid = false;
}

// 检查模块完整性
console.log('\n🏗️ 检查模块完整性:');
console.log('-'.repeat(50));

const modules = [
  'order', 'process', 'quality', 'inventory', 
  'scheduling', 'tracking', 'report', 'websocket', 'health'
];

modules.forEach(module => {
  let moduleFiles = [
    `src/${module}/${module}.module.ts`,
    `src/${module}/${module}.service.ts`,
  ];

  // WebSocket模块使用gateway而不是controller
  if (module === 'websocket') {
    moduleFiles.push(`src/${module}/${module}.gateway.ts`);
  } else {
    moduleFiles.push(`src/${module}/${module}.controller.ts`);
  }

  const moduleValid = moduleFiles.every(file =>
    fs.existsSync(path.join(__dirname, '..', file))
  );

  if (moduleValid) {
    console.log(`✅ ${module} 模块完整`);
  } else {
    console.log(`❌ ${module} 模块不完整`);
    moduleFiles.forEach(file => {
      const exists = fs.existsSync(path.join(__dirname, '..', file));
      console.log(`   ${exists ? '✅' : '❌'} ${file}`);
    });
    allValid = false;
  }
});

// 最终结果
console.log('\n' + '='.repeat(50));
if (allValid) {
  console.log('🎉 MES服务项目结构验证通过！');
  console.log('✅ 所有必需文件和配置都已就位');
  console.log('🚀 可以开始开发和部署');
  process.exit(0);
} else {
  console.log('💥 MES服务项目结构验证失败！');
  console.log('❌ 请检查上述缺失的文件和配置');
  console.log('🔧 修复问题后重新运行验证');
  process.exit(1);
}
