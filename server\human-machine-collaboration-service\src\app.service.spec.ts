import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { AppService } from './app.service';

describe('AppService', () => {
  let service: AppService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AppService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              const config = {
                NODE_ENV: 'test',
                PORT: 3005,
                DB_HOST: 'localhost',
                DB_PORT: 3306,
                DB_DATABASE: 'test_db',
                REDIS_HOST: 'localhost',
                REDIS_PORT: 6379,
              };
              return config[key] || defaultValue;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<AppService>(AppService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getHealth', () => {
    it('should return health status', () => {
      const health = service.getHealth();
      
      expect(health).toHaveProperty('status', 'ok');
      expect(health).toHaveProperty('timestamp');
      expect(health).toHaveProperty('service', 'human-machine-collaboration-service');
      expect(health).toHaveProperty('version', '1.0.0');
      expect(health).toHaveProperty('environment', 'test');
      expect(health).toHaveProperty('uptime');
      expect(health).toHaveProperty('memory');
      expect(health).toHaveProperty('features');
      expect(health.features).toHaveProperty('arvrGuidance', true);
      expect(health.features).toHaveProperty('aiAssistant', true);
      expect(health.features).toHaveProperty('voiceInteraction', true);
      expect(health.features).toHaveProperty('gestureRecognition', true);
      expect(health.features).toHaveProperty('realTimeCollaboration', true);
    });
  });

  describe('getServiceInfo', () => {
    it('should return service information', () => {
      const info = service.getServiceInfo();
      
      expect(info).toHaveProperty('name', '人机协作增强服务');
      expect(info).toHaveProperty('description');
      expect(info).toHaveProperty('version', '1.0.0');
      expect(info).toHaveProperty('author', 'DL Engine Team');
      expect(info).toHaveProperty('features');
      expect(info).toHaveProperty('documentation', '/api/docs');
      expect(info).toHaveProperty('websocket');
      
      expect(Array.isArray(info.features)).toBe(true);
      expect(info.features.length).toBeGreaterThan(0);
      
      expect(info.websocket).toHaveProperty('namespace', '/collaboration');
      expect(info.websocket).toHaveProperty('events');
      expect(Array.isArray(info.websocket.events)).toBe(true);
    });
  });

  describe('getSystemStats', () => {
    it('should return system statistics', async () => {
      const stats = await service.getSystemStats();
      
      expect(stats).toHaveProperty('timestamp');
      expect(stats).toHaveProperty('system');
      expect(stats).toHaveProperty('service');
      expect(stats).toHaveProperty('features');
      
      expect(stats.system).toHaveProperty('platform');
      expect(stats.system).toHaveProperty('arch');
      expect(stats.system).toHaveProperty('nodeVersion');
      expect(stats.system).toHaveProperty('uptime');
      expect(stats.system).toHaveProperty('memory');
      expect(stats.system).toHaveProperty('cpu');
      
      expect(stats.service).toHaveProperty('environment', 'test');
      expect(stats.service).toHaveProperty('port', 3005);
      expect(stats.service).toHaveProperty('database');
      expect(stats.service).toHaveProperty('redis');
      
      expect(stats.features).toHaveProperty('arvrEnabled', true);
      expect(stats.features).toHaveProperty('aiEnabled', true);
      expect(stats.features).toHaveProperty('voiceEnabled', true);
      expect(stats.features).toHaveProperty('gestureEnabled', true);
      expect(stats.features).toHaveProperty('websocketEnabled', true);
    });
  });
});
