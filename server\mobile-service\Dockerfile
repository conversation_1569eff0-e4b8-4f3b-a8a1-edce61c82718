# 移动端服务 Docker 镜像构建文件

# 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 复制包管理文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM node:18-alpine AS production

WORKDIR /app

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S mobile-service -u 1001

# 复制构建产物和依赖
COPY --from=builder --chown=mobile-service:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=mobile-service:nodejs /app/dist ./dist
COPY --from=builder --chown=mobile-service:nodejs /app/package*.json ./

# 创建日志目录
RUN mkdir -p /app/logs && \
    chown -R mobile-service:nodejs /app/logs

# 切换到非root用户
USER mobile-service

# 暴露端口
EXPOSE 3009

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3009/api/v1/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# 启动命令
CMD ["node", "dist/main.js"]
