import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';

import { IntelligentSchedulerService } from './intelligent-scheduler.service';
import { TaskManagementService } from './task-management.service';
import { SchedulingOptimizationService } from './scheduling-optimization.service';

// DTOs
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { SchedulingRequestDto } from './dto/scheduling-request.dto';
import { OptimizationConfigDto } from './dto/optimization-config.dto';
import { ReschedulingRequestDto } from './dto/rescheduling-request.dto';

/**
 * 智能调度控制器
 */
@ApiTags('scheduling')
@Controller('scheduling')
export class SchedulingController {
  constructor(
    private readonly schedulerService: IntelligentSchedulerService,
    private readonly taskService: TaskManagementService,
    private readonly optimizationService: SchedulingOptimizationService,
  ) {}

  /**
   * 生成调度方案
   */
  @Post('generate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '生成智能调度方案' })
  @ApiBody({ type: SchedulingRequestDto })
  @ApiResponse({ status: 200, description: '调度方案生成成功' })
  async generateSchedule(@Body() request: SchedulingRequestDto): Promise<any> {
    // 将 CreateTaskDto 转换为 ProductionTask 格式
    const tasks = request.tasks.map(taskDto => ({
      ...taskDto,
      id: '',
      status: 'pending' as any,
      actualDuration: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      scheduledTasks: [],
      constraints: [],
    }));

    const solution = await this.schedulerService.generateSchedule(
      tasks as any,
      request.objectives,
      request.algorithm as any,
    );

    return {
      success: true,
      message: '调度方案生成成功',
      data: solution,
    };
  }

  /**
   * 实时重调度
   */
  @Post('reschedule')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '实时重调度优化' })
  @ApiBody({ type: ReschedulingRequestDto })
  @ApiResponse({ status: 200, description: '重调度完成' })
  async realTimeReschedule(@Body() request: ReschedulingRequestDto): Promise<any> {
    const solution = await this.schedulerService.realTimeRescheduling(
      request.disruption,
    );

    return {
      success: true,
      message: '重调度完成',
      data: solution,
    };
  }

  /**
   * 获取当前调度方案
   */
  @Get('current')
  @ApiOperation({ summary: '获取当前调度方案' })
  @ApiResponse({ status: 200, description: '当前调度方案' })
  async getCurrentSchedule(): Promise<any> {
    const solution = await this.schedulerService.getCurrentSolution();

    return {
      success: true,
      message: '获取当前调度方案成功',
      data: solution,
    };
  }

  /**
   * 获取调度历史
   */
  @Get('history')
  @ApiOperation({ summary: '获取调度历史记录' })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'offset', required: false, type: Number })
  @ApiResponse({ status: 200, description: '调度历史记录' })
  async getSchedulingHistory(
    @Query('limit') limit: number = 10,
    @Query('offset') offset: number = 0,
  ): Promise<any> {
    const history = await this.schedulerService.getSchedulingHistory(
      limit,
      offset,
    );

    return {
      success: true,
      message: '获取调度历史成功',
      data: history,
    };
  }

  /**
   * 创建生产任务
   */
  @Post('tasks')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '创建生产任务' })
  @ApiBody({ type: CreateTaskDto })
  @ApiResponse({ status: 201, description: '任务创建成功' })
  async createTask(@Body() createTaskDto: CreateTaskDto) {
    const task = await this.taskService.createTask(createTaskDto);

    return {
      success: true,
      message: '任务创建成功',
      data: task,
    };
  }

  /**
   * 获取任务列表
   */
  @Get('tasks')
  @ApiOperation({ summary: '获取任务列表' })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'priority', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'offset', required: false, type: Number })
  @ApiResponse({ status: 200, description: '任务列表' })
  async getTasks(
    @Query('status') status?: string,
    @Query('priority') priority?: number,
    @Query('limit') limit: number = 20,
    @Query('offset') offset: number = 0,
  ) {
    const tasks = await this.taskService.getTasks({
      status,
      priority,
      limit,
      offset,
    });

    return {
      success: true,
      message: '获取任务列表成功',
      data: tasks,
    };
  }

  /**
   * 获取任务详情
   */
  @Get('tasks/:id')
  @ApiOperation({ summary: '获取任务详情' })
  @ApiParam({ name: 'id', description: '任务ID' })
  @ApiResponse({ status: 200, description: '任务详情' })
  async getTask(@Param('id', ParseUUIDPipe) id: string) {
    const task = await this.taskService.getTaskById(id);

    return {
      success: true,
      message: '获取任务详情成功',
      data: task,
    };
  }

  /**
   * 更新任务
   */
  @Put('tasks/:id')
  @ApiOperation({ summary: '更新任务信息' })
  @ApiParam({ name: 'id', description: '任务ID' })
  @ApiBody({ type: UpdateTaskDto })
  @ApiResponse({ status: 200, description: '任务更新成功' })
  async updateTask(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateTaskDto: UpdateTaskDto,
  ) {
    const task = await this.taskService.updateTask(id, updateTaskDto);

    return {
      success: true,
      message: '任务更新成功',
      data: task,
    };
  }

  /**
   * 删除任务
   */
  @Delete('tasks/:id')
  @ApiOperation({ summary: '删除任务' })
  @ApiParam({ name: 'id', description: '任务ID' })
  @ApiResponse({ status: 200, description: '任务删除成功' })
  async deleteTask(@Param('id', ParseUUIDPipe) id: string) {
    await this.taskService.deleteTask(id);

    return {
      success: true,
      message: '任务删除成功',
    };
  }

  /**
   * 优化配置
   */
  @Post('optimization/config')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '设置优化配置' })
  @ApiBody({ type: OptimizationConfigDto })
  @ApiResponse({ status: 200, description: '优化配置设置成功' })
  async setOptimizationConfig(@Body() config: OptimizationConfigDto) {
    await this.optimizationService.setOptimizationConfig(config);

    return {
      success: true,
      message: '优化配置设置成功',
    };
  }

  /**
   * 获取优化统计
   */
  @Get('optimization/stats')
  @ApiOperation({ summary: '获取优化统计信息' })
  @ApiResponse({ status: 200, description: '优化统计信息' })
  async getOptimizationStats() {
    const stats = await this.optimizationService.getOptimizationStats();

    return {
      success: true,
      message: '获取优化统计成功',
      data: stats,
    };
  }
}
