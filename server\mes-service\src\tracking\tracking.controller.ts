import { Controller, Get, Post, Put, Body, Param, Query, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { TrackingService } from './tracking.service';

@ApiTags('tracking')
@Controller('tracking')
@ApiBearerAuth()
export class TrackingController {
  private readonly logger = new Logger(TrackingController.name);

  constructor(private readonly trackingService: TrackingService) {}

  @Post()
  @ApiOperation({ summary: '创建跟踪记录' })
  async createTracking(@Body() trackingDto: any) {
    return await this.trackingService.createTracking(trackingDto);
  }

  @Get()
  @ApiOperation({ summary: '获取跟踪列表' })
  async getTrackings(@Query() query: any) {
    return await this.trackingService.getTrackings(query);
  }

  @Put(':id/status')
  @ApiOperation({ summary: '更新跟踪状态' })
  async updateTrackingStatus(@Param('id') id: string, @Body() body: { status: string }) {
    return await this.trackingService.updateTrackingStatus(id, body.status);
  }

  @Get('stats/overview')
  @ApiOperation({ summary: '获取跟踪统计' })
  async getTrackingStatistics() {
    return await this.trackingService.getTrackingStatistics();
  }
}
