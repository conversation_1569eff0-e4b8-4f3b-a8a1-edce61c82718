import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';

/**
 * 优化WebSocket网关
 */
@WebSocketGateway({
  cors: {
    origin: '*',
  },
  namespace: '/optimization',
})
export class OptimizationGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(OptimizationGateway.name);
  private connectedClients = new Map<string, Socket>();

  /**
   * 客户端连接
   */
  handleConnection(client: Socket) {
    this.connectedClients.set(client.id, client);
    this.logger.log(`优化客户端连接: ${client.id}`);
    
    client.emit('connected', {
      message: '已连接到优化服务',
      clientId: client.id,
      timestamp: new Date(),
    });
  }

  /**
   * 客户端断开连接
   */
  handleDisconnect(client: Socket) {
    this.connectedClients.delete(client.id);
    this.logger.log(`优化客户端断开连接: ${client.id}`);
  }

  /**
   * 订阅优化任务状态
   */
  @SubscribeMessage('subscribe-optimization-status')
  handleSubscribeOptimizationStatus(@ConnectedSocket() client: Socket) {
    client.join('optimization-status');
    this.logger.log(`客户端 ${client.id} 订阅优化状态`);
    
    client.emit('subscription-confirmed', {
      type: 'optimization-status',
      message: '已订阅优化状态',
    });
  }

  /**
   * 广播优化开始
   */
  broadcastOptimizationStarted(jobInfo: any) {
    this.server.to('optimization-status').emit('optimization-started', {
      ...jobInfo,
      timestamp: new Date(),
    });
    
    this.logger.log(`广播优化开始: ${jobInfo.jobId}`);
  }

  /**
   * 广播优化完成
   */
  broadcastOptimizationCompleted(result: any) {
    this.server.to('optimization-status').emit('optimization-completed', {
      ...result,
      timestamp: new Date(),
    });
    
    this.logger.log(`广播优化完成: ${result.jobId}`);
  }

  /**
   * 广播优化失败
   */
  broadcastOptimizationFailed(error: any) {
    this.server.to('optimization-status').emit('optimization-failed', {
      ...error,
      timestamp: new Date(),
    });
    
    this.logger.error(`广播优化失败: ${error.jobId}`);
  }
}
