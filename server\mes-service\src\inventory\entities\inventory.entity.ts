import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 库存类型枚举
 */
export enum InventoryType {
  RAW_MATERIAL = 'raw_material',    // 原材料
  SEMI_FINISHED = 'semi_finished',  // 半成品
  FINISHED_GOODS = 'finished_goods', // 成品
  CONSUMABLES = 'consumables',      // 消耗品
  TOOLS = 'tools'                   // 工具
}

/**
 * 库存状态枚举
 */
export enum InventoryStatus {
  AVAILABLE = 'available',          // 可用
  RESERVED = 'reserved',            // 预留
  IN_TRANSIT = 'in_transit',        // 在途
  QUARANTINE = 'quarantine',        // 隔离
  DAMAGED = 'damaged'               // 损坏
}

/**
 * 库存实体
 */
@Entity('inventories')
export class Inventory {
  @ApiProperty({ description: '库存ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '物料编码' })
  @Column({ name: 'material_code', length: 100, unique: true })
  materialCode: string;

  @ApiProperty({ description: '物料名称' })
  @Column({ name: 'material_name', length: 200 })
  materialName: string;

  @ApiProperty({ description: '物料规格' })
  @Column({ name: 'material_specification', type: 'text', nullable: true })
  materialSpecification: string;

  @ApiProperty({ description: '库存类型', enum: InventoryType })
  @Column({ name: 'inventory_type', type: 'enum', enum: InventoryType })
  inventoryType: InventoryType;

  @ApiProperty({ description: '仓库编码' })
  @Column({ name: 'warehouse_code', length: 50 })
  warehouseCode: string;

  @ApiProperty({ description: '仓库名称' })
  @Column({ name: 'warehouse_name', length: 200 })
  warehouseName: string;

  @ApiProperty({ description: '库位编码' })
  @Column({ name: 'location_code', length: 50, nullable: true })
  locationCode: string;

  @ApiProperty({ description: '当前库存数量' })
  @Column({ name: 'current_quantity', type: 'decimal', precision: 10, scale: 3, default: 0 })
  currentQuantity: number;

  @ApiProperty({ description: '可用数量' })
  @Column({ name: 'available_quantity', type: 'decimal', precision: 10, scale: 3, default: 0 })
  availableQuantity: number;

  @ApiProperty({ description: '预留数量' })
  @Column({ name: 'reserved_quantity', type: 'decimal', precision: 10, scale: 3, default: 0 })
  reservedQuantity: number;

  @ApiProperty({ description: '安全库存' })
  @Column({ name: 'safety_stock', type: 'decimal', precision: 10, scale: 3, default: 0 })
  safetyStock: number;

  @ApiProperty({ description: '最小库存' })
  @Column({ name: 'min_stock', type: 'decimal', precision: 10, scale: 3, default: 0 })
  minStock: number;

  @ApiProperty({ description: '最大库存' })
  @Column({ name: 'max_stock', type: 'decimal', precision: 10, scale: 3, default: 0 })
  maxStock: number;

  @ApiProperty({ description: '单位' })
  @Column({ length: 20 })
  unit: string;

  @ApiProperty({ description: '单价' })
  @Column({ name: 'unit_price', type: 'decimal', precision: 10, scale: 2, default: 0 })
  unitPrice: number;

  @ApiProperty({ description: '库存状态', enum: InventoryStatus })
  @Column({ type: 'enum', enum: InventoryStatus, default: InventoryStatus.AVAILABLE })
  status: InventoryStatus;

  @ApiProperty({ description: '批次号' })
  @Column({ name: 'batch_number', length: 100, nullable: true })
  batchNumber: string;

  @ApiProperty({ description: '生产日期' })
  @Column({ name: 'production_date', type: 'date', nullable: true })
  productionDate: Date;

  @ApiProperty({ description: '有效期' })
  @Column({ name: 'expiry_date', type: 'date', nullable: true })
  expiryDate: Date;

  @ApiProperty({ description: '供应商编码' })
  @Column({ name: 'supplier_code', length: 100, nullable: true })
  supplierCode: string;

  @ApiProperty({ description: '供应商名称' })
  @Column({ name: 'supplier_name', length: 200, nullable: true })
  supplierName: string;

  @ApiProperty({ description: '最后盘点时间' })
  @Column({ name: 'last_count_time', type: 'datetime', nullable: true })
  lastCountTime: Date;

  @ApiProperty({ description: '备注' })
  @Column({ type: 'text', nullable: true })
  remarks: string;

  @ApiProperty({ description: '创建人' })
  @Column({ name: 'created_by', length: 100 })
  createdBy: string;

  @ApiProperty({ description: '更新人' })
  @Column({ name: 'updated_by', length: 100, nullable: true })
  updatedBy: string;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 计算属性
  get totalValue(): number {
    return this.currentQuantity * this.unitPrice;
  }

  get isLowStock(): boolean {
    return this.currentQuantity <= this.minStock;
  }

  get isOverStock(): boolean {
    return this.currentQuantity >= this.maxStock;
  }

  get isExpired(): boolean {
    if (!this.expiryDate) return false;
    return new Date() > this.expiryDate;
  }

  get daysToExpiry(): number {
    if (!this.expiryDate) return Infinity;
    const now = new Date();
    const diffTime = this.expiryDate.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}

/**
 * 库存事务实体
 */
@Entity('inventory_transactions')
export class InventoryTransaction {
  @ApiProperty({ description: '事务ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '事务编号' })
  @Column({ name: 'transaction_number', length: 50, unique: true })
  transactionNumber: string;

  @ApiProperty({ description: '物料编码' })
  @Column({ name: 'material_code', length: 100 })
  materialCode: string;

  @ApiProperty({ description: '事务类型' })
  @Column({ name: 'transaction_type', length: 50 })
  transactionType: string; // 入库、出库、调拨、盘点等

  @ApiProperty({ description: '数量变化' })
  @Column({ name: 'quantity_change', type: 'decimal', precision: 10, scale: 3 })
  quantityChange: number;

  @ApiProperty({ description: '变化前数量' })
  @Column({ name: 'quantity_before', type: 'decimal', precision: 10, scale: 3 })
  quantityBefore: number;

  @ApiProperty({ description: '变化后数量' })
  @Column({ name: 'quantity_after', type: 'decimal', precision: 10, scale: 3 })
  quantityAfter: number;

  @ApiProperty({ description: '仓库编码' })
  @Column({ name: 'warehouse_code', length: 50 })
  warehouseCode: string;

  @ApiProperty({ description: '库位编码' })
  @Column({ name: 'location_code', length: 50, nullable: true })
  locationCode: string;

  @ApiProperty({ description: '关联单据号' })
  @Column({ name: 'reference_number', length: 100, nullable: true })
  referenceNumber: string;

  @ApiProperty({ description: '批次号' })
  @Column({ name: 'batch_number', length: 100, nullable: true })
  batchNumber: string;

  @ApiProperty({ description: '事务原因' })
  @Column({ type: 'text', nullable: true })
  reason: string;

  @ApiProperty({ description: '操作人' })
  @Column({ name: 'operated_by', length: 100 })
  operatedBy: string;

  @ApiProperty({ description: '操作时间' })
  @Column({ name: 'operated_at', type: 'datetime', default: () => 'CURRENT_TIMESTAMP' })
  operatedAt: Date;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}
