import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

export enum AlertSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum AlertCondition {
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  BETWEEN = 'between',
  OUTSIDE_RANGE = 'outside_range',
  RATE_OF_CHANGE = 'rate_of_change',
  NO_DATA = 'no_data'
}

export enum AlertStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

@Entity('alert_rules')
@Index(['deviceId'])
@Index(['tagName'])
@Index(['status'])
export class AlertRule {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 200 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'device_id', type: 'uuid', nullable: true })
  deviceId: string;

  @Column({ name: 'tag_name', length: 100, nullable: true })
  tagName: string;

  @Column({
    type: 'enum',
    enum: AlertCondition,
    default: AlertCondition.GREATER_THAN
  })
  condition: AlertCondition;

  @Column({ type: 'json' })
  thresholds: any; // { value: number, upperValue?: number }

  @Column({
    type: 'enum',
    enum: AlertSeverity,
    default: AlertSeverity.MEDIUM
  })
  severity: AlertSeverity;

  @Column({
    type: 'enum',
    enum: AlertStatus,
    default: AlertStatus.ACTIVE
  })
  status: AlertStatus;

  @Column({ name: 'evaluation_interval', type: 'int', default: 60 })
  evaluationInterval: number; // 秒

  @Column({ name: 'notification_channels', type: 'json', nullable: true })
  notificationChannels: string[]; // ['email', 'sms', 'webhook']

  @Column({ name: 'notification_config', type: 'json', nullable: true })
  notificationConfig: any;

  @Column({ name: 'auto_resolve', type: 'boolean', default: true })
  autoResolve: boolean;

  @Column({ name: 'resolve_timeout', type: 'int', default: 300 })
  resolveTimeout: number; // 秒

  @Column({ name: 'last_evaluation', type: 'timestamp', nullable: true })
  lastEvaluation: Date;

  @Column({ name: 'last_triggered', type: 'timestamp', nullable: true })
  lastTriggered: Date;

  @Column({ name: 'trigger_count', type: 'int', default: 0 })
  triggerCount: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
