import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from './health.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get()
  @ApiOperation({ summary: '基础健康检查' })
  @ApiResponse({ status: 200, description: '服务健康' })
  @ApiResponse({ status: 503, description: '服务不健康' })
  async checkHealth() {
    return this.healthService.checkBasicHealth();
  }

  @Get('detailed')
  @ApiOperation({ summary: '详细健康检查' })
  @ApiResponse({ status: 200, description: '服务健康' })
  @ApiResponse({ status: 503, description: '服务不健康' })
  async checkDetailedHealth() {
    return this.healthService.checkDetailedHealth();
  }

  @Get('database')
  @ApiOperation({ summary: '数据库健康检查' })
  @ApiResponse({ status: 200, description: '数据库连接正常' })
  @ApiResponse({ status: 503, description: '数据库连接异常' })
  async checkDatabaseHealth() {
    return this.healthService.checkDatabaseHealth();
  }

  @Get('info')
  @ApiOperation({ summary: '获取服务信息' })
  @ApiResponse({ status: 200, description: '服务信息获取成功' })
  getServiceInfo() {
    return this.healthService.getServiceInfo();
  }

  @Get('memory')
  @ApiOperation({ summary: '获取内存使用情况' })
  @ApiResponse({ status: 200, description: '内存信息获取成功' })
  getMemoryUsage() {
    return this.healthService.getMemoryUsage();
  }

  @Get('metrics')
  @ApiOperation({ summary: '获取系统指标' })
  @ApiResponse({ status: 200, description: '系统指标获取成功' })
  getSystemMetrics() {
    return this.healthService.getSystemMetrics();
  }
}
