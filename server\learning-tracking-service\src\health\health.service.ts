/**
 * 健康检查服务
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(
    private configService: ConfigService,
    @InjectDataSource()
    private dataSource: DataSource,
  ) {}

  /**
   * 获取详细健康状态
   */
  async getDetailedHealthStatus() {
    const startTime = Date.now();
    
    try {
      const [
        databaseStatus,
        memoryStatus,
        systemStatus,
      ] = await Promise.all([
        this.checkDatabaseHealth(),
        this.checkMemoryHealth(),
        this.checkSystemHealth(),
      ]);

      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        responseTime: `${responseTime}ms`,
        service: {
          name: '学习记录跟踪服务',
          version: '1.0.0',
          environment: this.configService.get('NODE_ENV'),
          uptime: process.uptime(),
        },
        checks: {
          database: databaseStatus,
          memory: memoryStatus,
          system: systemStatus,
        },
      };
    } catch (error) {
      this.logger.error('健康检查失败:', error);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  /**
   * 检查就绪状态
   */
  async checkReadiness() {
    try {
      // 检查数据库连接
      await this.dataSource.query('SELECT 1');
      
      return {
        status: 'ready',
        timestamp: new Date().toISOString(),
        message: '服务已就绪',
      };
    } catch (error) {
      this.logger.error('就绪检查失败:', error);
      return {
        status: 'not_ready',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  /**
   * 检查存活状态
   */
  async checkLiveness() {
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      pid: process.pid,
    };
  }

  /**
   * 检查数据库健康状态
   */
  private async checkDatabaseHealth() {
    try {
      const startTime = Date.now();
      await this.dataSource.query('SELECT 1');
      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        responseTime: `${responseTime}ms`,
        connection: 'active',
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  /**
   * 检查内存健康状态
   */
  private checkMemoryHealth() {
    const memUsage = process.memoryUsage();
    const totalMemory = memUsage.heapTotal + memUsage.external;
    const usedMemory = memUsage.heapUsed;
    const memoryUsagePercent = (usedMemory / totalMemory) * 100;

    return {
      status: memoryUsagePercent < 90 ? 'healthy' : 'warning',
      usage: {
        heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
        external: `${Math.round(memUsage.external / 1024 / 1024)}MB`,
        rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
        usagePercent: `${memoryUsagePercent.toFixed(2)}%`,
      },
    };
  }

  /**
   * 检查系统健康状态
   */
  private checkSystemHealth() {
    return {
      status: 'healthy',
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      uptime: `${Math.floor(process.uptime())}s`,
      pid: process.pid,
      environment: this.configService.get('NODE_ENV'),
    };
  }
}
