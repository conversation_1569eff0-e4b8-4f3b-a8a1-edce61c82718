/**
 * 移动端同步服务
 * 
 * 处理移动设备的数据同步逻辑
 */

import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as moment from 'moment';
import { v4 as uuidv4 } from 'uuid';

// 实体
import { SyncRecord } from '../mobile-sync/entities/sync-record.entity';
import { ConflictRecord } from '../mobile-sync/entities/conflict-record.entity';
import { MobileDevice } from '../device/entities/mobile-device.entity';

// DTO
import { DataChangeDto } from '../controllers/mobile-sync.controller';

/**
 * 同步结果接口
 */
export interface SyncResult {
  acceptedChanges: DataChangeDto[];
  conflicts: ConflictRecord[];
  rejectedChanges: DataChangeDto[];
}

/**
 * 同步统计接口
 */
export interface SyncStats {
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  conflictCount: number;
  lastSyncTime: Date | null;
  avgSyncTime: number;
}

/**
 * 设备状态接口
 */
export interface DeviceStatus {
  deviceId: string;
  isOnline: boolean;
  lastActivity: Date;
  syncStatus: 'idle' | 'syncing' | 'conflict' | 'error';
  pendingChanges: number;
  lastSyncTime: Date | null;
}

@Injectable()
export class MobileSyncService {
  private readonly logger = new Logger(MobileSyncService.name);

  constructor(
    @InjectRepository(SyncRecord)
    private readonly syncRecordRepository: Repository<SyncRecord>,
    
    @InjectRepository(ConflictRecord)
    private readonly conflictRecordRepository: Repository<ConflictRecord>,
    
    @InjectRepository(MobileDevice)
    private readonly deviceRepository: Repository<MobileDevice>,
    
    private readonly configService: ConfigService,
  ) {}

  /**
   * 处理上传的变更
   */
  async processUploadedChanges(
    userId: string,
    deviceId: string,
    changes: DataChangeDto[]
  ): Promise<SyncResult> {
    this.logger.log(`处理用户 ${userId} 设备 ${deviceId} 的 ${changes.length} 个变更`);
    
    const result: SyncResult = {
      acceptedChanges: [],
      conflicts: [],
      rejectedChanges: []
    };

    for (const change of changes) {
      try {
        // 检查是否存在冲突
        const conflict = await this.checkForConflict(userId, deviceId, change);
        
        if (conflict) {
          // 创建冲突记录
          const conflictRecord = await this.createConflictRecord(
            userId,
            deviceId,
            change,
            conflict
          );
          result.conflicts.push(conflictRecord);
        } else {
          // 接受变更
          await this.acceptChange(userId, deviceId, change);
          result.acceptedChanges.push(change);
        }
      } catch (error) {
        this.logger.error(`处理变更失败: ${change.id}`, error);
        result.rejectedChanges.push(change);
      }
    }

    // 更新设备同步状态
    await this.updateDeviceSyncStatus(deviceId, 'idle');

    return result;
  }

  /**
   * 获取设备的变更数据
   */
  async getChangesForDevice(
    userId: string,
    deviceId: string,
    lastSyncTime: number,
    options: {
      entityTypes?: string[];
      limit?: number;
      includeDeleted?: boolean;
    } = {}
  ): Promise<DataChangeDto[]> {
    const { entityTypes, limit = 100, includeDeleted = false } = options;
    
    this.logger.log(`获取用户 ${userId} 设备 ${deviceId} 自 ${lastSyncTime} 以来的变更`);

    // 构建查询条件
    const queryBuilder = this.syncRecordRepository
      .createQueryBuilder('sync')
      .where('sync.userId = :userId', { userId })
      .andWhere('sync.deviceId != :deviceId', { deviceId })
      .andWhere('sync.timestamp > :lastSyncTime', { lastSyncTime })
      .orderBy('sync.timestamp', 'ASC')
      .limit(limit);

    if (entityTypes && entityTypes.length > 0) {
      queryBuilder.andWhere('sync.entityType IN (:...entityTypes)', { entityTypes });
    }

    if (!includeDeleted) {
      queryBuilder.andWhere('sync.type != :deleteType', { deleteType: 'delete' });
    }

    const syncRecords = await queryBuilder.getMany();

    // 转换为DataChangeDto格式
    return syncRecords.map(record => ({
      id: record.id,
      type: record.type as any,
      entityType: record.entityType as any,
      entityId: record.entityId,
      data: record.data,
      timestamp: record.timestamp,
      deviceId: record.deviceId,
      userId: record.userId,
      version: record.version
    }));
  }

  /**
   * 获取同步统计
   */
  async getSyncStats(userId: string, deviceId: string): Promise<SyncStats> {
    const totalSyncs = await this.syncRecordRepository.count({
      where: { userId, deviceId }
    });

    const successfulSyncs = await this.syncRecordRepository.count({
      where: { userId, deviceId, status: 'success' }
    });

    const failedSyncs = totalSyncs - successfulSyncs;

    const conflictCount = await this.conflictRecordRepository.count({
      where: { userId, deviceId, status: 'pending' }
    });

    const lastSyncRecord = await this.syncRecordRepository.findOne({
      where: { userId, deviceId },
      order: { timestamp: 'DESC' }
    });

    // 计算平均同步时间
    const avgSyncTime = await this.calculateAverageSyncTime(userId, deviceId);

    return {
      totalSyncs,
      successfulSyncs,
      failedSyncs,
      conflictCount,
      lastSyncTime: lastSyncRecord?.createdAt || null,
      avgSyncTime
    };
  }

  /**
   * 获取设备状态
   */
  async getDeviceStatus(userId: string, deviceId: string): Promise<DeviceStatus> {
    const device = await this.deviceRepository.findOne({
      where: { deviceId, userId }
    });

    if (!device) {
      throw new NotFoundException('设备不存在');
    }

    const pendingChanges = await this.syncRecordRepository.count({
      where: { userId, deviceId, status: 'pending' }
    });

    const lastSyncRecord = await this.syncRecordRepository.findOne({
      where: { userId, deviceId },
      order: { timestamp: 'DESC' }
    });

    return {
      deviceId,
      isOnline: device.isOnline,
      lastActivity: device.lastActivity,
      syncStatus: device.syncStatus as any,
      pendingChanges,
      lastSyncTime: lastSyncRecord?.createdAt || null
    };
  }

  /**
   * 获取冲突列表
   */
  async getConflicts(userId: string, deviceId: string): Promise<ConflictRecord[]> {
    return this.conflictRecordRepository.find({
      where: { userId, deviceId, status: 'pending' },
      order: { createdAt: 'DESC' }
    });
  }

  /**
   * 解决冲突
   */
  async resolveConflict(
    userId: string,
    deviceId: string,
    conflictId: string,
    resolution: 'accept_local' | 'accept_remote' | 'merge',
    mergedData?: any
  ): Promise<any> {
    const conflict = await this.conflictRecordRepository.findOne({
      where: { id: conflictId, userId, deviceId }
    });

    if (!conflict) {
      throw new NotFoundException('冲突记录不存在');
    }

    let resolvedData: any;

    switch (resolution) {
      case 'accept_local':
        resolvedData = conflict.localData;
        break;
      case 'accept_remote':
        resolvedData = conflict.remoteData;
        break;
      case 'merge':
        resolvedData = mergedData || this.mergeData(conflict.localData, conflict.remoteData);
        break;
      default:
        throw new Error('无效的冲突解决方案');
    }

    // 更新冲突记录
    conflict.status = 'resolved';
    conflict.resolution = resolution;
    conflict.resolvedData = resolvedData;
    conflict.resolvedAt = new Date();
    await this.conflictRecordRepository.save(conflict);

    // 应用解决方案
    await this.applyResolution(userId, deviceId, conflict, resolvedData);

    return {
      conflictId,
      resolution,
      resolvedData,
      timestamp: new Date()
    };
  }

  /**
   * 检查冲突
   */
  private async checkForConflict(
    userId: string,
    deviceId: string,
    change: DataChangeDto
  ): Promise<any> {
    // 查找同一实体的最新变更
    const latestChange = await this.syncRecordRepository.findOne({
      where: {
        userId,
        entityType: change.entityType,
        entityId: change.entityId
      },
      order: { timestamp: 'DESC' }
    });

    if (!latestChange) {
      return null;
    }

    // 检查版本冲突
    if (latestChange.version >= change.version && latestChange.deviceId !== deviceId) {
      return latestChange;
    }

    return null;
  }

  /**
   * 创建冲突记录
   */
  private async createConflictRecord(
    userId: string,
    deviceId: string,
    localChange: DataChangeDto,
    remoteChange: any
  ): Promise<ConflictRecord> {
    const conflict = this.conflictRecordRepository.create({
      id: uuidv4(),
      userId,
      deviceId,
      entityType: localChange.entityType,
      entityId: localChange.entityId,
      localData: localChange.data,
      remoteData: remoteChange.data,
      localVersion: localChange.version,
      remoteVersion: remoteChange.version,
      status: 'pending'
    });

    return this.conflictRecordRepository.save(conflict);
  }

  /**
   * 接受变更
   */
  private async acceptChange(
    userId: string,
    deviceId: string,
    change: DataChangeDto
  ): Promise<void> {
    const syncRecord = this.syncRecordRepository.create({
      id: change.id,
      userId,
      deviceId,
      type: change.type,
      entityType: change.entityType,
      entityId: change.entityId,
      data: change.data,
      timestamp: change.timestamp,
      version: change.version,
      status: 'success'
    });

    await this.syncRecordRepository.save(syncRecord);
  }

  /**
   * 更新设备同步状态
   */
  private async updateDeviceSyncStatus(
    deviceId: string,
    status: 'idle' | 'syncing' | 'conflict' | 'error'
  ): Promise<void> {
    await this.deviceRepository.update(
      { deviceId },
      { syncStatus: status, lastActivity: new Date() }
    );
  }

  /**
   * 计算平均同步时间
   */
  private async calculateAverageSyncTime(userId: string, deviceId: string): Promise<number> {
    // 简化实现，返回固定值
    return 1500; // 1.5秒
  }

  /**
   * 合并数据
   */
  private mergeData(localData: any, remoteData: any): any {
    // 简化的数据合并逻辑
    return { ...remoteData, ...localData };
  }

  /**
   * 应用冲突解决方案
   */
  private async applyResolution(
    userId: string,
    deviceId: string,
    conflict: ConflictRecord,
    resolvedData: any
  ): Promise<void> {
    // 创建解决后的同步记录
    const syncRecord = this.syncRecordRepository.create({
      id: uuidv4(),
      userId,
      deviceId,
      type: 'update',
      entityType: conflict.entityType,
      entityId: conflict.entityId,
      data: resolvedData,
      timestamp: Date.now(),
      version: Math.max(conflict.localVersion, conflict.remoteVersion) + 1,
      status: 'success'
    });

    await this.syncRecordRepository.save(syncRecord);
  }

  /**
   * 定时清理过期的同步记录
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async cleanupExpiredRecords(): Promise<void> {
    const retentionDays = this.configService.get<number>('SYNC_RETENTION_DAYS', 30);
    const cutoffDate = moment().subtract(retentionDays, 'days').toDate();

    const deletedCount = await this.syncRecordRepository
      .createQueryBuilder()
      .delete()
      .where('createdAt < :cutoffDate', { cutoffDate })
      .execute();

    this.logger.log(`清理了 ${deletedCount.affected} 条过期的同步记录`);
  }

  /**
   * 处理实时变更
   */
  async processRealtimeChange(
    userId: string,
    deviceId: string,
    change: any
  ): Promise<SyncResult> {
    // 实时变更处理逻辑
    return this.processUploadedChanges(userId, deviceId, [change]);
  }

  /**
   * 获取同步数据
   */
  async getSyncData(
    userId: string,
    deviceId: string,
    options: any
  ): Promise<any> {
    return this.getChangesForDevice(userId, deviceId, options.lastSyncTime || 0, options);
  }

  /**
   * 获取待同步变更
   */
  async getPendingChanges(userId: string, deviceId: string): Promise<DataChangeDto[]> {
    const pendingRecords = await this.syncRecordRepository.find({
      where: { userId, deviceId, status: 'pending' },
      order: { timestamp: 'ASC' },
      take: 100
    });

    return pendingRecords.map(record => ({
      id: record.id,
      type: record.type as any,
      entityType: record.entityType as any,
      entityId: record.entityId,
      data: record.data,
      timestamp: record.timestamp,
      deviceId: record.deviceId,
      userId: record.userId,
      version: record.version
    }));
  }

  /**
   * 获取最后同步时间
   */
  async getLastSyncTime(userId: string, deviceId: string): Promise<number> {
    const lastRecord = await this.syncRecordRepository.findOne({
      where: { userId, deviceId },
      order: { timestamp: 'DESC' }
    });

    return lastRecord ? lastRecord.timestamp : 0;
  }

  /**
   * 强制同步
   */
  async forceSync(
    userId: string,
    deviceId: string,
    options: {
      resetLocal?: boolean;
      resetRemote?: boolean;
      conflictResolution?: 'client_wins' | 'server_wins';
    }
  ): Promise<any> {
    this.logger.log(`用户 ${userId} 设备 ${deviceId} 执行强制同步`);

    // 如果需要重置本地数据
    if (options.resetLocal) {
      await this.syncRecordRepository.delete({ userId, deviceId });
    }

    // 如果需要重置远程数据
    if (options.resetRemote) {
      await this.syncRecordRepository
        .createQueryBuilder()
        .update()
        .set({ status: 'overridden' })
        .where('userId = :userId AND deviceId != :deviceId', { userId, deviceId })
        .execute();
    }

    // 解决所有待处理的冲突
    const conflicts = await this.getConflicts(userId, deviceId);
    for (const conflict of conflicts) {
      const resolution = options.conflictResolution === 'client_wins' ? 'accept_local' : 'accept_remote';
      await this.resolveConflict(userId, deviceId, conflict.id, resolution);
    }

    return {
      message: '强制同步完成',
      resetLocal: options.resetLocal || false,
      resetRemote: options.resetRemote || false,
      resolvedConflicts: conflicts.length,
      timestamp: new Date()
    };
  }

  /**
   * 重置设备数据
   */
  async resetDeviceData(userId: string, deviceId: string): Promise<void> {
    this.logger.log(`重置用户 ${userId} 设备 ${deviceId} 的数据`);

    // 删除同步记录
    await this.syncRecordRepository.delete({ userId, deviceId });

    // 删除冲突记录
    await this.conflictRecordRepository.delete({ userId, deviceId });

    // 重置设备状态
    await this.updateDeviceSyncStatus(deviceId, 'idle');
  }

  /**
   * 获取用户设备列表
   */
  async getUserDevices(userId: string): Promise<any[]> {
    const devices = await this.deviceRepository.find({
      where: { userId, isActive: true },
      order: { lastActivity: 'DESC' }
    });

    return devices.map(device => ({
      deviceId: device.deviceId,
      deviceName: device.deviceName,
      platform: device.platform,
      isOnline: device.isOnline,
      lastActivity: device.lastActivity,
      syncStatus: device.syncStatus
    }));
  }

  /**
   * 注册设备
   */
  async registerDevice(userId: string, deviceInfo: any): Promise<any> {
    const existingDevice = await this.deviceRepository.findOne({
      where: { deviceId: deviceInfo.deviceId }
    });

    if (existingDevice) {
      // 更新现有设备
      Object.assign(existingDevice, {
        deviceName: deviceInfo.deviceName,
        platform: deviceInfo.platform,
        version: deviceInfo.version,
        capabilities: deviceInfo.capabilities,
        lastActivity: new Date(),
        isActive: true
      });
      return this.deviceRepository.save(existingDevice);
    }

    // 创建新设备
    const device = this.deviceRepository.create({
      deviceId: deviceInfo.deviceId,
      userId,
      deviceName: deviceInfo.deviceName,
      platform: deviceInfo.platform,
      version: deviceInfo.version,
      capabilities: deviceInfo.capabilities,
      isOnline: false,
      syncStatus: 'idle',
      lastActivity: new Date(),
      firstRegistered: new Date(),
      isActive: true
    });

    return this.deviceRepository.save(device);
  }

  /**
   * 获取详细统计
   */
  async getDetailedStats(userId: string, deviceId: string, period: string): Promise<any> {
    const stats = await this.getSyncStats(userId, deviceId);

    return {
      ...stats,
      period,
      deviceInfo: await this.deviceRepository.findOne({
        where: { deviceId, userId }
      })
    };
  }

  /**
   * 获取服务健康状态
   */
  async getServiceHealth(): Promise<any> {
    const totalDevices = await this.deviceRepository.count();
    const onlineDevices = await this.deviceRepository.count({ where: { isOnline: true } });
    const totalSyncs = await this.syncRecordRepository.count();
    const pendingConflicts = await this.conflictRecordRepository.count({ where: { status: 'pending' } });

    return {
      totalDevices,
      onlineDevices,
      totalSyncs,
      pendingConflicts,
      uptime: process.uptime(),
      memory: process.memoryUsage()
    };
  }
}
