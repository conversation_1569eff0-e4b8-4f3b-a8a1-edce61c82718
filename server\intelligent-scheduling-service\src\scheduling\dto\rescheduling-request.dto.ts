import {
  IsString,
  IsEnum,
  IsObject,
  IsOptional,
  IsDate,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 干扰类型枚举
 */
export enum DisruptionType {
  MACHINE_BREAKDOWN = 'machine_breakdown',
  WORKER_ABSENCE = 'worker_absence',
  MATERIAL_SHORTAGE = 'material_shortage',
  URGENT_ORDER = 'urgent_order',
  QUALITY_ISSUE = 'quality_issue',
  DELAY = 'delay',
  CANCELLATION = 'cancellation',
  RESOURCE_CONFLICT = 'resource_conflict',
  EXTERNAL_EVENT = 'external_event',
}

/**
 * 干扰严重程度枚举
 */
export enum DisruptionSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * 重调度请求DTO
 */
export class ReschedulingRequestDto {
  @ApiProperty({ description: '干扰事件信息' })
  @IsObject()
  disruption: {
    id: string;
    type: DisruptionType;
    severity: DisruptionSeverity;
    description: string;
    affectedTasks: string[];
    affectedResources: string[];
    estimatedDuration: number;
    startTime: Date;
    endTime?: Date;
    metadata?: any;
  };

  @ApiPropertyOptional({ description: '重调度策略' })
  @IsOptional()
  @IsString()
  strategy?: 'local_adjustment' | 'partial_reschedule' | 'complete_reschedule' | 'auto';

  @ApiPropertyOptional({ description: '重调度配置' })
  @IsOptional()
  @IsObject()
  config?: {
    maxReschedulingTime?: number;
    preserveCompletedTasks?: boolean;
    minimizeDisruption?: boolean;
    allowDelays?: boolean;
    prioritizeUrgentTasks?: boolean;
  };

  @ApiPropertyOptional({ description: '扩展参数' })
  @IsOptional()
  @IsObject()
  metadata?: any;
}
