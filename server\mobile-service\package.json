{"name": "mobile-service", "version": "1.0.0", "description": "移动端服务 - 提供移动设备专用的数据同步和实时通信功能", "author": "DL Engine Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:generate": "typeorm migration:generate -d src/config/database.config.ts", "migration:run": "typeorm migration:run -d src/config/database.config.ts", "migration:revert": "typeorm migration:revert -d src/config/database.config.ts", "seed:run": "ts-node src/database/seeds/run-seeds.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@nestjs/websockets": "^10.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/schedule": "^3.0.0", "@nestjs/throttler": "^4.0.0", "@nestjs/terminus": "^10.0.0", "typeorm": "^0.3.17", "mysql2": "^3.6.0", "redis": "^4.6.7", "ioredis": "^5.3.2", "socket.io": "^4.7.2", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "bcrypt": "^5.1.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "swagger-ui-express": "^5.0.0", "compression": "^1.7.4", "helmet": "^7.0.0", "uuid": "^9.0.0", "moment": "^2.29.4", "lodash": "^4.17.21", "rxjs": "^7.8.1", "reflect-metadata": "^0.1.13"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/bcrypt": "^5.0.0", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/uuid": "^9.0.2", "@types/compression": "^1.7.2", "@types/lodash": "^4.14.195", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapping": {"^@shared/(.*)$": "<rootDir>/../shared/$1"}}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}