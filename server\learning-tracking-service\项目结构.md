# 学习记录跟踪服务项目结构

## 📁 项目目录结构

```
server/learning-tracking-service/
├── 📄 package.json                    # 项目依赖和脚本配置
├── 📄 tsconfig.json                   # TypeScript编译配置
├── 📄 nest-cli.json                   # NestJS CLI配置
├── 📄 .env.example                    # 环境变量配置模板
├── 📄 .gitignore                      # Git忽略文件配置
├── 📄 Dockerfile                      # Docker镜像构建配置
├── 📄 docker-compose.yml              # Docker Compose配置
├── 📄 Makefile                        # 常用命令快捷方式
├── 📄 README.md                       # 项目文档
├── 📄 修复总结.md                      # 修复过程总结
├── 📄 项目结构.md                      # 本文件
│
├── 📁 src/                            # 源代码目录
│   ├── 📄 main.ts                     # 应用启动入口
│   ├── 📄 app.module.ts               # 主应用模块
│   ├── 📄 app.controller.ts           # 应用控制器
│   ├── 📄 app.service.ts              # 应用服务
│   ├── 📄 app.controller.spec.ts      # 应用控制器测试
│   ├── 📄 learning-tracking.module.ts # 学习跟踪核心模块
│   ├── 📄 data-source.ts              # TypeORM数据源配置
│   │
│   ├── 📁 common/                     # 公共组件
│   │   ├── 📄 logger.config.ts       # 日志配置
│   │   ├── 📁 filters/                # 过滤器
│   │   │   └── 📄 global-exception.filter.ts  # 全局异常过滤器
│   │   └── 📁 interceptors/           # 拦截器
│   │       └── 📄 logging.interceptor.ts      # 日志拦截器
│   │
│   ├── 📁 controllers/                # 控制器
│   │   └── 📄 learning-tracking.controller.ts # 学习跟踪控制器
│   │
│   ├── 📁 entities/                   # 数据实体
│   │   ├── 📄 content.entity.ts       # 内容实体
│   │   ├── 📄 learner-profile.entity.ts       # 学习者画像实体
│   │   ├── 📄 learning-record.entity.ts       # 学习记录实体
│   │   └── 📄 recommendation.entity.ts        # 推荐实体
│   │
│   ├── 📁 guards/                     # 守卫
│   │   └── 📄 jwt-auth.guard.ts       # JWT认证守卫
│   │
│   ├── 📁 health/                     # 健康检查模块
│   │   ├── 📄 health.module.ts        # 健康检查模块
│   │   ├── 📄 health.controller.ts    # 健康检查控制器
│   │   ├── 📄 health.service.ts       # 健康检查服务
│   │   └── 📄 health.service.spec.ts  # 健康检查服务测试
│   │
│   ├── 📁 learninglocker/             # Learninglocker集成
│   │   └── 📄 learninglocker-client.service.ts
│   │
│   ├── 📁 migrations/                 # 数据库迁移
│   │   ├── 📄 001-create-learning-tracking-tables.sql
│   │   └── 📄 1703000000000-InitialMigration.ts
│   │
│   ├── 📁 processors/                 # 队列处理器
│   │   └── 📄 learning-sync.processor.ts
│   │
│   ├── 📁 profile/                    # 用户画像分析
│   │   └── 📄 learner-profile-analyzer.service.ts
│   │
│   ├── 📁 recommendation/             # 个性化推荐
│   │   ├── 📄 personalized-recommendation.service.ts
│   │   └── 📄 recommendation.interface.ts
│   │
│   ├── 📁 seeds/                      # 数据库种子
│   │   ├── 📄 initial-data.seed.ts    # 初始数据种子
│   │   └── 📄 run-seeds.ts            # 种子执行脚本
│   │
│   ├── 📁 sync/                       # 数据同步
│   │   └── 📄 learning-data-sync.service.ts
│   │
│   └── 📁 xapi/                       # xAPI相关
│       ├── 📄 xapi-client.service.ts  # xAPI客户端服务
│       ├── 📄 xapi-statement-builder.service.ts  # xAPI语句构建器
│       └── 📁 interfaces/             # 接口定义
│           └── 📄 xapi.interface.ts   # xAPI接口定义
│
├── 📁 test/                           # 测试目录
│   ├── 📄 app.e2e-spec.ts            # E2E测试
│   └── 📄 jest-e2e.json              # E2E测试配置
│
├── 📁 scripts/                       # 脚本目录
│   ├── 📄 start-dev.sh               # 开发启动脚本
│   ├── 📄 deploy.sh                  # 部署脚本
│   └── 📄 verify-setup.sh            # 设置验证脚本
│
└── 📁 k8s/                           # Kubernetes配置
    ├── 📄 deployment.yaml            # 部署配置
    └── 📄 configmap.yaml             # 配置映射
```

## 🏗️ 架构说明

### 核心模块
- **AppModule**: 主应用模块，整合所有功能模块
- **LearningTrackingModule**: 学习跟踪核心业务模块
- **HealthModule**: 健康检查模块

### 业务功能
- **xAPI数据采集**: 标准化学习数据收集
- **学习记录管理**: 完整的学习轨迹跟踪
- **用户画像分析**: 智能用户行为分析
- **个性化推荐**: 基于AI的内容推荐
- **数据同步**: 与外部系统的数据同步
- **Learninglocker集成**: LRS系统集成

### 技术组件
- **认证授权**: JWT认证守卫
- **异常处理**: 全局异常过滤器
- **日志记录**: 请求日志拦截器
- **健康检查**: 多层次健康监控
- **队列处理**: 异步任务处理
- **数据库**: TypeORM + MySQL
- **缓存**: Redis缓存和消息队列

## 🚀 快速开始

### 1. 环境准备
```bash
# 检查环境
./scripts/verify-setup.sh

# 安装依赖
npm install

# 配置环境
cp .env.example .env
```

### 2. 数据库设置
```bash
# 运行迁移
npm run migration:run

# 执行种子数据
npm run seed:run
```

### 3. 启动服务
```bash
# 开发模式
npm run start:dev

# 或使用脚本
./scripts/start-dev.sh

# 或使用Makefile
make start-dev
```

### 4. 验证服务
- 服务信息: http://localhost:3030
- API文档: http://localhost:3030/api/docs
- 健康检查: http://localhost:3030/api/v1/health

## 📊 功能特性

### API接口
- **REST API**: 完整的RESTful接口
- **微服务**: TCP Transport微服务通信
- **WebSocket**: 实时数据推送（规划中）
- **Swagger**: 自动生成API文档

### 数据管理
- **学习记录**: CRUD操作和查询
- **用户画像**: 智能分析和洞察
- **个性化推荐**: 算法驱动的推荐
- **内容管理**: 学习内容元数据管理

### 系统特性
- **高可用**: 健康检查和监控
- **可扩展**: 模块化架构设计
- **可维护**: 完整的测试覆盖
- **可部署**: Docker和K8s支持

## 🔧 开发指南

### 添加新功能
1. 在相应目录创建模块文件
2. 在主模块中注册新模块
3. 添加相应的测试文件
4. 更新API文档

### 数据库变更
1. 生成迁移: `npm run migration:generate`
2. 执行迁移: `npm run migration:run`
3. 更新种子数据（如需要）

### 测试
```bash
# 单元测试
npm run test

# E2E测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

### 部署
```bash
# Docker部署
make docker-compose-up

# Kubernetes部署
make k8s-deploy

# 生产部署
./scripts/deploy.sh
```

## 📝 注意事项

1. **环境配置**: 确保正确配置.env文件中的数据库和Redis连接
2. **依赖版本**: 保持依赖版本与package.json一致
3. **数据库**: 确保MySQL和Redis服务正常运行
4. **权限**: 确保脚本文件有执行权限
5. **端口**: 确保3030和3031端口未被占用

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License
