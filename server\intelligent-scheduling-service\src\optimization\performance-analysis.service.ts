import { Injectable, Logger } from '@nestjs/common';

/**
 * 性能分析服务
 */
@Injectable()
export class PerformanceAnalysisService {
  private readonly logger = new Logger(PerformanceAnalysisService.name);
  private performanceData: any[] = [];

  /**
   * 生成性能分析报告
   */
  async generatePerformanceReport(period: string): Promise<any> {
    try {
      this.logger.log(`生成性能分析报告: ${period}`);

      const reportData = await this.collectPerformanceData(period);
      const analysis = await this.analyzePerformanceData(reportData);
      const trends = await this.analyzeTrends(reportData);
      const recommendations = await this.generateRecommendations(analysis, trends);

      return {
        period,
        generatedAt: new Date(),
        summary: analysis.summary,
        metrics: analysis.metrics,
        trends,
        recommendations,
        charts: this.generateChartData(reportData),
      };
    } catch (error) {
      this.logger.error(`生成性能报告失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 收集性能数据
   */
  private async collectPerformanceData(period: string): Promise<any[]> {
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case 'day':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // 模拟性能数据收集
    const data = [];
    const dataPoints = period === 'day' ? 24 : period === 'week' ? 7 : 30;

    for (let i = 0; i < dataPoints; i++) {
      const timestamp = new Date(startDate.getTime() + (i * (now.getTime() - startDate.getTime()) / dataPoints));
      
      data.push({
        timestamp,
        optimizationCount: Math.floor(Math.random() * 20) + 5,
        averageExecutionTime: Math.random() * 5000 + 1000,
        successRate: Math.random() * 0.3 + 0.7,
        averageQuality: Math.random() * 0.3 + 0.7,
        resourceUtilization: Math.random() * 0.4 + 0.6,
        costEfficiency: Math.random() * 0.3 + 0.7,
        userSatisfaction: Math.random() * 0.2 + 0.8,
      });
    }

    return data;
  }

  /**
   * 分析性能数据
   */
  private async analyzePerformanceData(data: any[]): Promise<any> {
    const metrics = {
      totalOptimizations: data.reduce((sum, d) => sum + d.optimizationCount, 0),
      averageExecutionTime: data.reduce((sum, d) => sum + d.averageExecutionTime, 0) / data.length,
      overallSuccessRate: data.reduce((sum, d) => sum + d.successRate, 0) / data.length,
      averageQuality: data.reduce((sum, d) => sum + d.averageQuality, 0) / data.length,
      averageUtilization: data.reduce((sum, d) => sum + d.resourceUtilization, 0) / data.length,
      costEfficiency: data.reduce((sum, d) => sum + d.costEfficiency, 0) / data.length,
      userSatisfaction: data.reduce((sum, d) => sum + d.userSatisfaction, 0) / data.length,
    };

    const summary = {
      performance: this.categorizePerformance(metrics.averageQuality),
      efficiency: this.categorizeEfficiency(metrics.averageExecutionTime),
      reliability: this.categorizeReliability(metrics.overallSuccessRate),
      utilization: this.categorizeUtilization(metrics.averageUtilization),
    };

    return { metrics, summary };
  }

  /**
   * 分析趋势
   */
  private async analyzeTrends(data: any[]): Promise<any> {
    const trends = {};

    const metrics = ['averageExecutionTime', 'successRate', 'averageQuality', 'resourceUtilization'];

    for (const metric of metrics) {
      const values = data.map(d => d[metric]);
      const trend = this.calculateTrend(values);
      
      trends[metric] = {
        direction: trend > 0.05 ? 'increasing' : trend < -0.05 ? 'decreasing' : 'stable',
        magnitude: Math.abs(trend),
        significance: Math.abs(trend) > 0.1 ? 'significant' : 'minor',
      };
    }

    return trends;
  }

  /**
   * 计算趋势
   */
  private calculateTrend(values: number[]): number {
    if (values.length < 2) return 0;

    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = values.reduce((sum, val, index) => sum + index * val, 0);
    const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    return slope / (sumY / n); // 归一化斜率
  }

  /**
   * 生成建议
   */
  private async generateRecommendations(analysis: any, trends: any): Promise<any[]> {
    const recommendations = [];

    // 基于性能指标的建议
    if (analysis.metrics.averageQuality < 0.8) {
      recommendations.push({
        type: 'quality_improvement',
        priority: 'high',
        title: '提升优化质量',
        description: '当前优化质量偏低，建议调整算法参数或尝试其他算法',
        actions: [
          '增加遗传算法的种群大小',
          '调整模拟退火的冷却速率',
          '尝试混合算法策略',
        ],
      });
    }

    if (analysis.metrics.averageExecutionTime > 3000) {
      recommendations.push({
        type: 'performance_optimization',
        priority: 'medium',
        title: '优化执行速度',
        description: '优化执行时间较长，建议优化算法或硬件配置',
        actions: [
          '启用并行计算',
          '使用更快的启发式算法',
          '增加计算资源',
        ],
      });
    }

    if (analysis.metrics.averageUtilization < 0.7) {
      recommendations.push({
        type: 'utilization_improvement',
        priority: 'medium',
        title: '提升资源利用率',
        description: '资源利用率偏低，存在优化空间',
        actions: [
          '重新平衡工作负载',
          '优化资源分配策略',
          '减少空闲时间',
        ],
      });
    }

    // 基于趋势的建议
    if (trends.averageQuality?.direction === 'decreasing' && trends.averageQuality?.significance === 'significant') {
      recommendations.push({
        type: 'trend_alert',
        priority: 'high',
        title: '质量下降趋势警告',
        description: '优化质量呈下降趋势，需要立即关注',
        actions: [
          '检查算法配置',
          '分析数据质量',
          '重新校准参数',
        ],
      });
    }

    return recommendations;
  }

  /**
   * 生成图表数据
   */
  private generateChartData(data: any[]): any {
    return {
      timeSeriesCharts: {
        executionTime: {
          labels: data.map(d => d.timestamp.toISOString().split('T')[0]),
          data: data.map(d => d.averageExecutionTime),
          title: '平均执行时间趋势',
        },
        quality: {
          labels: data.map(d => d.timestamp.toISOString().split('T')[0]),
          data: data.map(d => d.averageQuality),
          title: '优化质量趋势',
        },
        utilization: {
          labels: data.map(d => d.timestamp.toISOString().split('T')[0]),
          data: data.map(d => d.resourceUtilization),
          title: '资源利用率趋势',
        },
      },
      distributionCharts: {
        successRate: {
          ranges: ['0-60%', '60-80%', '80-90%', '90-100%'],
          counts: [2, 5, 15, 8],
          title: '成功率分布',
        },
      },
    };
  }

  /**
   * 性能分类
   */
  private categorizePerformance(quality: number): string {
    if (quality >= 0.9) return 'excellent';
    if (quality >= 0.8) return 'good';
    if (quality >= 0.7) return 'fair';
    return 'poor';
  }

  /**
   * 效率分类
   */
  private categorizeEfficiency(executionTime: number): string {
    if (executionTime <= 1000) return 'excellent';
    if (executionTime <= 3000) return 'good';
    if (executionTime <= 5000) return 'fair';
    return 'poor';
  }

  /**
   * 可靠性分类
   */
  private categorizeReliability(successRate: number): string {
    if (successRate >= 0.95) return 'excellent';
    if (successRate >= 0.9) return 'good';
    if (successRate >= 0.8) return 'fair';
    return 'poor';
  }

  /**
   * 利用率分类
   */
  private categorizeUtilization(utilization: number): string {
    if (utilization >= 0.85) return 'excellent';
    if (utilization >= 0.75) return 'good';
    if (utilization >= 0.65) return 'fair';
    return 'poor';
  }
}
