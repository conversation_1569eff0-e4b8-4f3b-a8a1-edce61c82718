/**
 * 搜索DTO
 */
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsBoolean, IsObject, Min, Max } from 'class-validator';

export class SearchDto {
  @ApiProperty({ description: '搜索查询', example: '什么是高血压的症状？' })
  @IsString()
  query: string;

  @ApiProperty({ 
    description: '返回结果数量', 
    required: false, 
    minimum: 1, 
    maximum: 50, 
    default: 5 
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  topK?: number = 5;

  @ApiProperty({ 
    description: '相似度阈值', 
    required: false, 
    minimum: 0, 
    maximum: 1, 
    default: 0.7 
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  threshold?: number = 0.7;

  @ApiProperty({ 
    description: '过滤条件', 
    required: false,
    example: { category: '医疗设备', author: '某医院' }
  })
  @IsOptional()
  @IsObject()
  filter?: Record<string, any>;

  @ApiProperty({ 
    description: '是否启用重排序', 
    required: false, 
    default: false 
  })
  @IsOptional()
  @IsBoolean()
  rerank?: boolean = false;

  @ApiProperty({ 
    description: '是否启用查询扩展', 
    required: false, 
    default: false 
  })
  @IsOptional()
  @IsBoolean()
  expandQuery?: boolean = false;
}

export class HybridSearchDto extends SearchDto {
  @ApiProperty({ 
    description: '语义搜索权重', 
    required: false, 
    minimum: 0, 
    maximum: 1, 
    default: 0.7 
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  semanticWeight?: number = 0.7;

  @ApiProperty({ 
    description: '关键词搜索权重', 
    required: false, 
    minimum: 0, 
    maximum: 1, 
    default: 0.3 
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  keywordWeight?: number = 0.3;
}

export class SearchSuggestionsDto {
  @ApiProperty({ description: '部分查询词', example: '高血' })
  @IsString()
  partialQuery: string;

  @ApiProperty({ 
    description: '建议数量', 
    required: false, 
    minimum: 1, 
    maximum: 20, 
    default: 5 
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(20)
  limit?: number = 5;
}
