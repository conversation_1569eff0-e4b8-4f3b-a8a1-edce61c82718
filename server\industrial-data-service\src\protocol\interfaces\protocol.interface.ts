import { DeviceStatus } from '../../device-management/entities/device.entity';

export interface ProtocolConfig {
  deviceId: string;
  protocol: string;
  host: string;
  port: number;
  timeout?: number;
  retries?: number;
  [key: string]: any;
}

export interface ConnectionResult {
  success: boolean;
  deviceId: string;
  status: DeviceStatus;
  message?: string;
  connectionTime?: number;
  error?: string;
}

export interface ReadResult {
  success: boolean;
  deviceId: string;
  address: string;
  value: any;
  timestamp: Date;
  quality?: string;
  error?: string;
}

export interface WriteResult {
  success: boolean;
  deviceId: string;
  address: string;
  value: any;
  timestamp: Date;
  error?: string;
}

export interface ProtocolDriver {
  /**
   * 协议名称
   */
  readonly name: string;

  /**
   * 协议版本
   */
  readonly version: string;

  /**
   * 连接设备
   */
  connect(config: ProtocolConfig): Promise<ConnectionResult>;

  /**
   * 断开连接
   */
  disconnect(deviceId: string): Promise<boolean>;

  /**
   * 读取数据
   */
  read(deviceId: string, address: string): Promise<ReadResult>;

  /**
   * 批量读取数据
   */
  readMultiple(deviceId: string, addresses: string[]): Promise<ReadResult[]>;

  /**
   * 写入数据
   */
  write(deviceId: string, address: string, value: any): Promise<WriteResult>;

  /**
   * 批量写入数据
   */
  writeMultiple(deviceId: string, data: { address: string; value: any }[]): Promise<WriteResult[]>;

  /**
   * 检查连接状态
   */
  isConnected(deviceId: string): boolean;

  /**
   * 获取连接信息
   */
  getConnectionInfo(deviceId: string): any;
}
