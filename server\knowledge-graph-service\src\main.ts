import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { AppModule } from './app.module';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';

async function bootstrap() {
  const logger = new Logger('KnowledgeGraphService');
  
  try {
    // 创建应用实例
    const app = await NestFactory.create(AppModule, {
      logger: ['log', 'error', 'warn', 'debug', 'verbose'],
    });

    // 获取配置服务
    const configService = app.get(ConfigService);

    // 设置全局前缀
    app.setGlobalPrefix('api/v1');

    // 启用CORS
    app.enableCors({
      origin: configService.get<string>('CORS_ORIGIN', '*'),
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      credentials: true,
    });

    // 全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
        disableErrorMessages: configService.get<string>('NODE_ENV') === 'production',
      }),
    );

    // 全局异常过滤器
    app.useGlobalFilters(new GlobalExceptionFilter());

    // 全局拦截器
    app.useGlobalInterceptors(new LoggingInterceptor());

    // Swagger API文档
    if (configService.get<string>('NODE_ENV') !== 'production') {
      const config = new DocumentBuilder()
        .setTitle('工业知识图谱服务')
        .setDescription('智能推理引擎、专家知识系统API文档')
        .setVersion('1.0.0')
        .addTag('knowledge-graph', '知识图谱管理')
        .addTag('inference', '智能推理')
        .addTag('expert', '专家系统')
        .addTag('health', '健康检查')
        .addBearerAuth()
        .addServer(`http://localhost:${configService.get<number>('PORT', 3025)}`, '开发环境')
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
          persistAuthorization: true,
          tagsSorter: 'alpha',
          operationsSorter: 'alpha',
        },
      });
    }

    // 微服务配置（可选）
    if (configService.get<string>('REDIS_HOST')) {
      try {
        const microserviceOptions: MicroserviceOptions = {
          transport: Transport.REDIS,
          options: {
            host: configService.get<string>('REDIS_HOST', 'localhost'),
            port: configService.get<number>('REDIS_PORT', 6379),
            password: configService.get<string>('REDIS_PASSWORD'),
            retryAttempts: 3,
            retryDelay: 1000,
          },
        };

        app.connectMicroservice(microserviceOptions);
        await app.startAllMicroservices();
        logger.log('微服务已启动');
      } catch (error: any) {
        logger.warn(`微服务连接失败，将以HTTP模式运行: ${error.message}`);
      }
    } else {
      logger.log('Redis未配置，跳过微服务启动');
    }

    // 启动HTTP服务
    const port = configService.get<number>('PORT', 3025);
    const host = configService.get<string>('HOST', '0.0.0.0');

    await app.listen(port, host);

    logger.log(`🚀 工业知识图谱服务已启动`);
    logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
    logger.log(`📖 API文档地址: http://${host}:${port}/api/docs`);
    logger.log(`🔍 健康检查: http://${host}:${port}/api/v1/health`);
    logger.log(`🔧 环境: ${configService.get<string>('NODE_ENV', 'development')}`);

    // 优雅关闭处理
    process.on('SIGTERM', async () => {
      logger.log('收到SIGTERM信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('收到SIGINT信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

  } catch (error) {
    logger.error('启动失败:', error);
    process.exit(1);
  }
}

bootstrap();
