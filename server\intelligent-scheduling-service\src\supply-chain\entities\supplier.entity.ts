import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * 供应商类型枚举
 */
export enum SupplierType {
  RAW_MATERIAL = 'raw_material',
  COMPONENT = 'component',
  SERVICE = 'service',
  LOGISTICS = 'logistics',
  EQUIPMENT = 'equipment',
}

/**
 * 供应商状态枚举
 */
export enum SupplierStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  BLACKLISTED = 'blacklisted',
}

/**
 * 供应商实体
 */
@Entity('suppliers')
@Index(['type', 'status'])
@Index(['location'])
export class Supplier {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'supplier_id', length: 100, unique: true })
  supplierId: string;

  @Column({ length: 200 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: SupplierType,
  })
  type: SupplierType;

  @Column({
    type: 'enum',
    enum: SupplierStatus,
    default: SupplierStatus.ACTIVE,
  })
  status: SupplierStatus;

  @Column({ length: 200, nullable: true })
  location: string;

  @Column({ name: 'contact_person', length: 100, nullable: true })
  contactPerson: string;

  @Column({ length: 50, nullable: true })
  phone: string;

  @Column({ length: 100, nullable: true })
  email: string;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0, comment: '质量评级(0-1)' })
  qualityRating: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0, comment: '交付评级(0-1)' })
  deliveryRating: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0, comment: '成本评级(0-1)' })
  costRating: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0, comment: '综合评级(0-1)' })
  overallRating: number;

  @Column({ name: 'lead_time_days', type: 'int', default: 0, comment: '交付周期(天)' })
  leadTimeDays: number;

  @Column({ name: 'minimum_order_quantity', type: 'int', default: 1, comment: '最小订单量' })
  minimumOrderQuantity: number;

  @Column({ name: 'payment_terms', length: 100, nullable: true, comment: '付款条件' })
  paymentTerms: string;

  @Column({ type: 'json', nullable: true, comment: '供应能力' })
  capabilities: {
    category: string;
    capacity: number;
    unit: string;
    certifications: string[];
  }[];

  @Column({ type: 'json', nullable: true, comment: '认证信息' })
  certifications: {
    name: string;
    issuer: string;
    validFrom: Date;
    validTo: Date;
    status: string;
  }[];

  @Column({ name: 'risk_level', length: 20, default: 'medium', comment: '风险等级' })
  riskLevel: string;

  @Column({ name: 'collaboration_history', type: 'json', nullable: true, comment: '合作历史' })
  collaborationHistory: {
    year: number;
    orderCount: number;
    totalValue: number;
    qualityIssues: number;
    deliveryIssues: number;
  }[];

  @Column({ type: 'json', nullable: true, comment: '扩展属性' })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
