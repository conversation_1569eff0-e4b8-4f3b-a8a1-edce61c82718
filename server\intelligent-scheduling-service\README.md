# 智能调度服务 (Intelligent Scheduling Service)

智能调度和优化服务 - 生产计划、资源配置、供应链协同

## 功能特性

### 🎯 核心功能
- **智能生产调度**: 基于多种优化算法的生产任务调度
- **实时调度优化**: 响应干扰事件的动态重调度
- **资源优化配置**: 智能资源分配和瓶颈分析
- **供应链协同**: 供应链协同优化和风险评估
- **能耗智能管理**: 能耗模式分析和节能优化

### 🧠 优化算法
- 遗传算法 (Genetic Algorithm)
- 模拟退火 (Simulated Annealing)
- 粒子群优化 (Particle Swarm Optimization)
- 蚁群算法 (Ant Colony Optimization)
- 禁忌搜索 (Tabu Search)
- 线性规划 (Linear Programming)
- 约束规划 (Constraint Programming)
- 启发式算法 (Heuristic)

### 📊 优化目标
- 最小化完工时间 (Minimize Makespan)
- 最小化成本 (Minimize Cost)
- 最大化吞吐量 (Maximize Throughput)
- 最小化能耗 (Minimize Energy)
- 最大化利用率 (Maximize Utilization)
- 最小化延迟 (Minimize Tardiness)
- 负载均衡 (Balance Workload)

## 项目结构

```
src/
├── main.ts                     # 应用入口
├── app.module.ts              # 主模块
├── app.controller.ts          # 主控制器
├── app.service.ts             # 主服务
├── scheduling/                # 调度模块
│   ├── scheduling.module.ts
│   ├── scheduling.controller.ts
│   ├── intelligent-scheduler.service.ts
│   ├── scheduling-optimization.service.ts
│   ├── task-management.service.ts
│   ├── constraint-solver.service.ts
│   ├── entities/              # 实体定义
│   │   ├── production-task.entity.ts
│   │   ├── scheduling-solution.entity.ts
│   │   ├── scheduled-task.entity.ts
│   │   └── task-constraint.entity.ts
│   ├── dto/                   # 数据传输对象
│   │   ├── create-task.dto.ts
│   │   ├── update-task.dto.ts
│   │   ├── scheduling-request.dto.ts
│   │   ├── rescheduling-request.dto.ts
│   │   └── optimization-config.dto.ts
│   └── processors/            # 队列处理器
│       └── scheduling.processor.ts
├── resource/                  # 资源模块
│   └── resource.module.ts
├── optimization/              # 优化模块
│   └── optimization.module.ts
├── supply-chain/              # 供应链模块
│   └── supply-chain.module.ts
├── energy/                    # 能耗模块
│   └── energy.module.ts
├── monitoring/                # 监控模块
│   └── monitoring.module.ts
└── websocket/                 # WebSocket模块
    ├── websocket.module.ts
    ├── scheduling.gateway.ts
    ├── optimization.gateway.ts
    ├── websocket.service.ts
    └── notification.service.ts
```

## API 端点

### 调度管理
- `POST /api/v1/scheduling/generate` - 生成调度方案
- `POST /api/v1/scheduling/reschedule` - 实时重调度
- `GET /api/v1/scheduling/current` - 获取当前调度方案
- `GET /api/v1/scheduling/history` - 获取调度历史

### 任务管理
- `POST /api/v1/scheduling/tasks` - 创建任务
- `GET /api/v1/scheduling/tasks` - 获取任务列表
- `GET /api/v1/scheduling/tasks/:id` - 获取任务详情
- `PUT /api/v1/scheduling/tasks/:id` - 更新任务
- `DELETE /api/v1/scheduling/tasks/:id` - 删除任务

### 优化配置
- `POST /api/v1/scheduling/optimization/config` - 设置优化配置
- `GET /api/v1/scheduling/optimization/stats` - 获取优化统计

## WebSocket 事件

### 调度更新 (/scheduling)
- `subscribe-schedule-updates` - 订阅调度更新
- `schedule-updated` - 调度方案更新通知
- `system-alert` - 系统警告

### 优化进度 (/optimization)
- `subscribe-optimization-status` - 订阅优化状态
- `optimization-started` - 优化开始
- `optimization-completed` - 优化完成
- `optimization-failed` - 优化失败

## 环境配置

复制 `.env.example` 到 `.env` 并配置相应参数：

```bash
cp .env.example .env
```

### 主要配置项
- `NODE_ENV`: 运行环境
- `PORT`: HTTP服务端口 (默认: 3015)
- `DB_*`: 数据库配置
- `REDIS_*`: Redis配置
- `SCHEDULING_*`: 调度算法参数

## 安装和运行

```bash
# 安装依赖
npm install

# 开发模式
npm run start:dev

# 生产构建
npm run build

# 生产运行
npm run start
```

## 技术栈

- **框架**: NestJS
- **数据库**: MySQL + TypeORM
- **缓存**: Redis
- **队列**: Bull
- **WebSocket**: Socket.IO
- **文档**: Swagger
- **语言**: TypeScript

## 监控和日志

- 健康检查: `GET /api/v1/health`
- 系统信息: `GET /api/v1/info`
- 系统统计: `GET /api/v1/stats`
- API文档: `http://localhost:3015/api/docs`

## 许可证

MIT License
