# 人机协作增强服务 (Human-Machine Collaboration Service)

## 项目概述

人机协作增强服务是一个基于 NestJS 的微服务，提供 AR/VR 维护指导、智能助手、语音交互、手势识别等功能，旨在增强人机协作体验。

## 核心功能

### 🔧 AR/VR 维护指导
- 沉浸式维护指导场景
- 实时 3D 可视化指导
- 智能步骤验证
- 性能分析和优化建议

### 🤖 智能助手
- 自然语言理解
- 智能问答系统
- 个性化建议
- 知识库管理

### 🎤 语音交互
- 语音识别和合成
- 多语言支持
- 实时语音指令
- 语音反馈系统

### 👋 手势识别
- 实时手势捕捉
- 动作识别和分析
- 手势指令映射
- 多模态交互

### 🌐 实时协作
- WebSocket 实时通信
- 多用户协作会话
- 数据同步
- 状态管理

## 技术栈

- **框架**: NestJS 9.x
- **数据库**: MySQL + TypeORM
- **缓存**: Redis
- **实时通信**: Socket.IO
- **语言**: TypeScript
- **测试**: Jest
- **文档**: Swagger/OpenAPI

## 项目结构

```
src/
├── ai-assistant/           # AI智能助手模块
│   ├── ai-assistant.service.ts
│   ├── nlp.service.ts
│   └── knowledge-base.service.ts
├── collaboration/          # 协作模块
│   ├── collaboration.service.ts
│   └── ar-vr-guidance.service.ts
├── voice-interaction/      # 语音交互模块
│   ├── voice-interaction.service.ts
│   ├── speech-recognition.service.ts
│   ├── text-to-speech.service.ts
│   └── voice-command-processor.service.ts
├── gesture-recognition/    # 手势识别模块
│   ├── gesture-recognition.service.ts
│   ├── hand-tracking.service.ts
│   ├── gesture-pattern.service.ts
│   └── motion-analysis.service.ts
├── database/              # 数据库实体
│   └── entities/
├── gateway/               # WebSocket网关
│   └── collaboration.gateway.ts
├── controllers/           # REST API控制器
├── common/               # 公共模块
├── main.ts               # 应用入口
└── app.module.ts         # 主模块
```

## 安装和运行

### 环境要求

- Node.js >= 16.x
- MySQL >= 8.0
- Redis >= 6.0

### 安装依赖

```bash
npm install
```

### 环境配置

复制环境配置文件：

```bash
cp .env.example .env
```

配置环境变量：

```env
# 服务配置
PORT=3005
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=human_machine_collaboration

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# AI模型配置
AI_MODEL_PATH=/models
SPEECH_API_KEY=your-speech-api-key
```

### 数据库初始化

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE human_machine_collaboration;"

# 运行迁移（开发环境会自动同步）
npm run start:dev
```

### 启动服务

```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start

# 调试模式
npm run start:debug
```

## API 文档

启动服务后，访问 Swagger 文档：

```
http://localhost:3005/api/docs
```

## 主要 API 端点

### 系统接口
- `GET /api/v1/health` - 健康检查
- `GET /api/v1/info` - 服务信息
- `GET /api/v1/stats` - 系统统计

### AI 智能助手
- `POST /api/v1/ai/chat` - AI 对话
- `GET /api/v1/ai/conversations/:userId` - 对话历史
- `GET /api/v1/ai/statistics` - AI 统计

### 语音交互
- `POST /api/v1/voice/recognize` - 语音识别
- `POST /api/v1/voice/synthesize` - 语音合成
- `GET /api/v1/voice/languages` - 支持语言
- `GET /api/v1/voice/statistics` - 语音统计

### 手势识别
- `POST /api/v1/gesture/sessions` - 开始手势识别
- `POST /api/v1/gesture/sessions/:id/frames` - 处理视频帧
- `GET /api/v1/gesture/patterns` - 手势模式
- `GET /api/v1/gesture/statistics` - 手势统计

### AR/VR 指导
- `POST /api/v1/arvr/scenes` - 创建场景
- `POST /api/v1/arvr/sessions` - 启动会话
- `POST /api/v1/arvr/sessions/:id/interactions` - 处理交互
- `GET /api/v1/arvr/statistics` - AR/VR 统计

## WebSocket 事件

连接到 `/collaboration` 命名空间：

```javascript
const socket = io('http://localhost:3005/collaboration', {
  query: { userId: 'user123', sessionId: 'session123' }
});

// 监听事件
socket.on('connected', (data) => console.log('已连接:', data));
socket.on('ai-response', (data) => console.log('AI响应:', data));
socket.on('voice-response', (data) => console.log('语音响应:', data));
socket.on('gesture-result', (data) => console.log('手势结果:', data));

// 发送事件
socket.emit('ai-chat', { userId: 'user123', message: '你好' });
socket.emit('voice-input', { audioData: '...', userId: 'user123' });
socket.emit('gesture-frame', { sessionId: 'session123', frameData: '...' });
```

## 测试

```bash
# 运行单元测试
npm run test

# 运行测试并监听变化
npm run test:watch

# 运行测试覆盖率
npm run test:cov

# 运行 E2E 测试
npm run test:e2e
```

## 部署

### Docker 部署

```bash
# 构建镜像
docker build -t human-machine-collaboration-service .

# 运行容器
docker run -p 3005:3005 human-machine-collaboration-service
```

### 使用 Docker Compose

```bash
docker-compose up -d
```

## 开发指南

### 添加新功能模块

1. 创建模块目录和文件
2. 定义服务接口
3. 实现业务逻辑
4. 添加数据库实体（如需要）
5. 创建 API 控制器
6. 编写测试用例
7. 更新文档

### 代码规范

- 使用 TypeScript 严格模式
- 遵循 NestJS 最佳实践
- 使用 ESLint 和 Prettier
- 编写单元测试和集成测试
- 添加适当的日志记录

## 监控和日志

### 日志配置

日志文件位置：`./logs/`

日志级别：
- `error` - 错误信息
- `warn` - 警告信息
- `info` - 一般信息
- `debug` - 调试信息

### 性能监控

- 内存使用监控
- CPU 使用监控
- 响应时间统计
- 错误率统计

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查数据库连接
   - 确认端口未被占用
   - 验证环境变量配置

2. **语音识别不工作**
   - 检查语音 API 密钥
   - 确认音频格式支持
   - 验证网络连接

3. **手势识别精度低**
   - 调整置信度阈值
   - 检查摄像头质量
   - 优化光照条件

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

- 项目维护者：DL Engine Team
- 邮箱：<EMAIL>
- 文档：https://docs.dlengine.com

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现 AR/VR 维护指导功能
- 添加智能助手模块
- 支持语音交互
- 集成手势识别
- 提供实时协作功能
