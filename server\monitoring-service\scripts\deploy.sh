#!/bin/bash

# 监控服务部署脚本

set -e

echo "🚀 开始部署监控服务..."

# 设置变量
SERVICE_NAME="monitoring-service"
IMAGE_NAME="monitoring-service"
CONTAINER_NAME="monitoring-service"
PORT=3003

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down || true

# 清理旧镜像
echo "🧹 清理旧镜像..."
docker rmi $IMAGE_NAME:latest || true

# 构建新镜像
echo "🔨 构建Docker镜像..."
docker build -t $IMAGE_NAME:latest .

# 启动服务
echo "🎯 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
if curl -f http://localhost:$PORT/api/v1/health > /dev/null 2>&1; then
    echo "✅ 监控服务部署成功！"
    echo "🌐 服务地址: http://localhost:$PORT"
    echo "📚 API文档: http://localhost:$PORT/api/docs"
    echo "📊 Prometheus指标: http://localhost:$PORT/metrics"
else
    echo "❌ 服务启动失败，请检查日志"
    docker-compose logs $CONTAINER_NAME
    exit 1
fi

echo "🎉 部署完成！"
