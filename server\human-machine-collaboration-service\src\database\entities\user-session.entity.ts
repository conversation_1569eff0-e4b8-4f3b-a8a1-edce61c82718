import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { ARVRScene } from './arvr-scene.entity';
import { InteractionLog } from './interaction-log.entity';

/**
 * 用户会话实体
 * 记录用户的AR/VR指导会话信息
 */
@Entity('user_sessions')
@Index(['userId', 'status'])
@Index(['sceneId', 'startTime'])
export class UserSession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 50 })
  userId: string;

  @Column({ type: 'uuid' })
  sceneId: string;

  @Column({ type: 'datetime' })
  startTime: Date;

  @Column({ type: 'datetime', nullable: true })
  endTime: Date;

  @Column({ type: 'int', default: 0 })
  currentStep: number;

  @Column({ type: 'json', nullable: true })
  completedSteps: string[];

  @Column({ type: 'json', nullable: true })
  performance: {
    totalTime: number;
    stepTimes: number[];
    errorCount: number;
    helpRequests: number;
    efficiency: number;
    accuracy: number;
    completionRate: number;
  };

  @Column({
    type: 'enum',
    enum: ['active', 'paused', 'completed', 'abandoned'],
    default: 'active',
  })
  status: string;

  @Column({ type: 'json', nullable: true })
  deviceInfo: any;

  @Column({ type: 'json', nullable: true })
  userPreferences: any;

  @Column({ type: 'json', nullable: true })
  sessionMetadata: any;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => ARVRScene, (scene) => scene.sessions)
  @JoinColumn({ name: 'sceneId' })
  scene: ARVRScene;

  @OneToMany(() => InteractionLog, (log) => log.session)
  interactions: InteractionLog[];
}
