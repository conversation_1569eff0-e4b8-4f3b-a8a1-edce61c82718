import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InventoryController } from './inventory.controller';
import { InventoryService } from './inventory.service';
import { Inventory, InventoryTransaction } from './entities/inventory.entity';

/**
 * 库存管理模块
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([Inventory, InventoryTransaction]),
  ],
  controllers: [InventoryController],
  providers: [InventoryService],
  exports: [InventoryService],
})
export class InventoryModule {}
