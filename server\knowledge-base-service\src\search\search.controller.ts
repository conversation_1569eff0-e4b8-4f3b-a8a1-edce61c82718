/**
 * 搜索控制器
 */
import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { SearchService } from './search.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { SearchDto, HybridSearchDto } from './dto/search.dto';

@ApiTags('search')
@Controller('search')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SearchController {
  constructor(private readonly searchService: SearchService) {}

  @Post('knowledge-base/:knowledgeBaseId')
  @ApiOperation({ summary: '在知识库中进行语义搜索' })
  @ApiParam({ name: 'knowledgeBaseId', description: '知识库ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '搜索成功',
    schema: {
      type: 'object',
      properties: {
        query: { type: 'string' },
        results: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              content: { type: 'string' },
              score: { type: 'number' },
              relevanceScore: { type: 'number' },
              snippet: { type: 'string' },
              highlights: { type: 'array', items: { type: 'string' } },
              metadata: { type: 'object' },
            },
          },
        },
        totalResults: { type: 'number' },
        searchTime: { type: 'number' },
      },
    },
  })
  async search(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @Body() searchDto: SearchDto,
    @CurrentUser() user: any,
  ) {
    const startTime = Date.now();
    
    const results = await this.searchService.search(knowledgeBaseId, searchDto.query, {
      topK: searchDto.topK,
      threshold: searchDto.threshold,
      filter: searchDto.filter,
      rerank: searchDto.rerank,
      expandQuery: searchDto.expandQuery,
    });

    const searchTime = Date.now() - startTime;

    return {
      query: searchDto.query,
      results,
      totalResults: results.length,
      searchTime,
    };
  }

  @Post('knowledge-base/:knowledgeBaseId/hybrid')
  @ApiOperation({ summary: '在知识库中进行混合搜索（语义+关键词）' })
  @ApiParam({ name: 'knowledgeBaseId', description: '知识库ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '搜索成功',
  })
  async hybridSearch(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @Body() searchDto: HybridSearchDto,
    @CurrentUser() user: any,
  ) {
    const startTime = Date.now();
    
    const results = await this.searchService.hybridSearch(knowledgeBaseId, searchDto.query, {
      topK: searchDto.topK,
      threshold: searchDto.threshold,
      filter: searchDto.filter,
      rerank: searchDto.rerank,
      expandQuery: searchDto.expandQuery,
    });

    const searchTime = Date.now() - startTime;

    return {
      query: searchDto.query,
      results,
      totalResults: results.length,
      searchTime,
      searchType: 'hybrid',
    };
  }

  @Get('knowledge-base/:knowledgeBaseId/suggestions')
  @ApiOperation({ summary: '获取搜索建议' })
  @ApiParam({ name: 'knowledgeBaseId', description: '知识库ID' })
  @ApiQuery({ name: 'q', description: '部分查询词' })
  @ApiQuery({ name: 'limit', required: false, description: '建议数量', example: 5 })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        suggestions: {
          type: 'array',
          items: { type: 'string' },
        },
      },
    },
  })
  async getSearchSuggestions(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @Query('q') partialQuery: string,
    @Query('limit') limit: number = 5,
  ) {
    const suggestions = await this.searchService.getSearchSuggestions(
      knowledgeBaseId,
      partialQuery,
      limit,
    );

    return { suggestions };
  }

  @Get('knowledge-base/:knowledgeBaseId/statistics')
  @ApiOperation({ summary: '获取搜索统计信息' })
  @ApiParam({ name: 'knowledgeBaseId', description: '知识库ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        totalVectors: { type: 'number' },
        collectionInfo: { type: 'object' },
      },
    },
  })
  async getSearchStatistics(@Param('knowledgeBaseId') knowledgeBaseId: string) {
    return this.searchService.getSearchStatistics(knowledgeBaseId);
  }
}
