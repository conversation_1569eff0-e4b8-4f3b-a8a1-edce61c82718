import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { KnowledgeGraphService } from './knowledge-graph.service';

@WebSocketGateway({
  namespace: '/knowledge',
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
  },
})
export class KnowledgeGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(KnowledgeGateway.name);
  private connectedClients = new Map<string, Socket>();

  constructor(
    private readonly knowledgeGraphService: KnowledgeGraphService,
  ) {}

  handleConnection(client: Socket) {
    this.connectedClients.set(client.id, client);
    this.logger.log(`客户端连接: ${client.id}`);
    
    // 发送连接确认
    client.emit('connected', {
      message: '知识图谱服务连接成功',
      clientId: client.id,
      timestamp: new Date().toISOString(),
    });
  }

  handleDisconnect(client: Socket) {
    this.connectedClients.delete(client.id);
    this.logger.log(`客户端断开连接: ${client.id}`);
  }

  @SubscribeMessage('query_knowledge')
  async handleQueryKnowledge(
    @MessageBody() data: { query: string; reasoning?: boolean },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      this.logger.log(`收到知识查询请求: ${data.query}`);
      
      // 发送查询开始通知
      client.emit('query_started', {
        message: '开始处理查询请求',
        query: data.query,
        timestamp: new Date().toISOString(),
      });

      // 执行查询
      const result = await this.knowledgeGraphService.intelligentQuery(
        data.query,
        data.reasoning,
      );

      // 发送查询结果
      client.emit('query_result', {
        success: true,
        data: result,
        message: '查询完成',
        timestamp: new Date().toISOString(),
      });

    } catch (error: any) {
      this.logger.error('查询处理失败', error);
      client.emit('query_error', {
        success: false,
        message: '查询处理失败',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  @SubscribeMessage('get_statistics')
  async handleGetStatistics(@ConnectedSocket() client: Socket) {
    try {
      const statistics = await this.knowledgeGraphService.getStatistics();
      
      client.emit('statistics_result', {
        success: true,
        data: statistics,
        message: '统计信息获取成功',
        timestamp: new Date().toISOString(),
      });

    } catch (error: any) {
      this.logger.error('获取统计信息失败', error);
      client.emit('statistics_error', {
        success: false,
        message: '获取统计信息失败',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  @SubscribeMessage('perform_inference')
  async handlePerformInference(
    @MessageBody() data: { facts: any[] },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      this.logger.log(`收到推理请求: ${data.facts.length} 个事实`);
      
      // 发送推理开始通知
      client.emit('inference_started', {
        message: '开始执行知识推理',
        factsCount: data.facts.length,
        timestamp: new Date().toISOString(),
      });

      // 执行推理
      const result = await this.knowledgeGraphService.performKnowledgeInference(data.facts);

      // 发送推理结果
      client.emit('inference_result', {
        success: true,
        data: result,
        message: '推理完成',
        timestamp: new Date().toISOString(),
      });

    } catch (error: any) {
      this.logger.error('推理处理失败', error);
      client.emit('inference_error', {
        success: false,
        message: '推理处理失败',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * 广播知识图谱更新事件
   */
  broadcastKnowledgeUpdate(event: string, data: any) {
    this.server.emit('knowledge_updated', {
      event,
      data,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 广播推理结果
   */
  broadcastInferenceResult(result: any) {
    this.server.emit('inference_broadcast', {
      result,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 获取连接的客户端数量
   */
  getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }
}
