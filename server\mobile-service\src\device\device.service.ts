/**
 * 设备管理服务
 * 
 * 处理移动设备的管理逻辑
 */

import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

// 实体
import { MobileDevice } from './entities/mobile-device.entity';
import { DeviceSession } from './entities/device-session.entity';

// DTO
import { DeviceRegistrationDto, DeviceUpdateDto, DeviceQueryDto } from './device.controller';

/**
 * 设备列表结果接口
 */
export interface DeviceListResult {
  devices: MobileDevice[];
  total: number;
  limit: number;
  offset: number;
}

/**
 * 设备统计接口
 */
export interface DeviceStats {
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  successRate: number;
  avgSyncTime: number;
  lastSyncTime: Date | null;
  totalSessions: number;
  activeSessions: number;
  totalUptime: number;
  registrationDate: Date;
}

@Injectable()
export class DeviceService {
  private readonly logger = new Logger(DeviceService.name);

  constructor(
    @InjectRepository(MobileDevice)
    private readonly deviceRepository: Repository<MobileDevice>,
    
    @InjectRepository(DeviceSession)
    private readonly sessionRepository: Repository<DeviceSession>,
  ) {}

  /**
   * 注册设备
   */
  async registerDevice(userId: string, registrationDto: DeviceRegistrationDto): Promise<MobileDevice> {
    this.logger.log(`注册设备: ${registrationDto.deviceId} for user: ${userId}`);

    // 检查设备是否已存在
    const existingDevice = await this.deviceRepository.findOne({
      where: { deviceId: registrationDto.deviceId }
    });

    if (existingDevice) {
      if (existingDevice.userId !== userId) {
        throw new ConflictException('设备已被其他用户注册');
      }
      
      // 更新现有设备信息
      Object.assign(existingDevice, {
        deviceName: registrationDto.deviceName,
        platform: registrationDto.platform,
        version: registrationDto.version,
        capabilities: registrationDto.capabilities,
        deviceInfo: registrationDto.deviceInfo,
        settings: registrationDto.settings,
        isActive: true,
        lastActivity: new Date(),
      });

      return this.deviceRepository.save(existingDevice);
    }

    // 创建新设备
    const device = this.deviceRepository.create({
      deviceId: registrationDto.deviceId,
      userId,
      deviceName: registrationDto.deviceName,
      platform: registrationDto.platform,
      version: registrationDto.version,
      capabilities: registrationDto.capabilities,
      deviceInfo: registrationDto.deviceInfo,
      settings: registrationDto.settings || {
        autoSync: true,
        syncInterval: 30000,
        wifiOnly: false,
        backgroundSync: true,
      },
      isOnline: false,
      syncStatus: 'idle',
      lastActivity: new Date(),
      firstRegistered: new Date(),
      isActive: true,
    });

    return this.deviceRepository.save(device);
  }

  /**
   * 获取用户设备列表
   */
  async getUserDevices(userId: string, queryDto: DeviceQueryDto): Promise<DeviceListResult> {
    const { platform, isOnline, isActive, limit = 50, offset = 0 } = queryDto;

    const queryBuilder = this.deviceRepository
      .createQueryBuilder('device')
      .where('device.userId = :userId', { userId });

    if (platform) {
      queryBuilder.andWhere('device.platform = :platform', { platform });
    }

    if (typeof isOnline === 'boolean') {
      queryBuilder.andWhere('device.isOnline = :isOnline', { isOnline });
    }

    if (typeof isActive === 'boolean') {
      queryBuilder.andWhere('device.isActive = :isActive', { isActive });
    }

    const [devices, total] = await queryBuilder
      .orderBy('device.lastActivity', 'DESC')
      .limit(limit)
      .offset(offset)
      .getManyAndCount();

    return {
      devices,
      total,
      limit,
      offset,
    };
  }

  /**
   * 获取设备详情
   */
  async getDeviceDetails(userId: string, deviceId: string): Promise<MobileDevice> {
    const device = await this.deviceRepository.findOne({
      where: { deviceId, userId }
    });

    if (!device) {
      throw new NotFoundException('设备不存在');
    }

    return device;
  }

  /**
   * 更新设备信息
   */
  async updateDevice(
    userId: string,
    deviceId: string,
    updateDto: DeviceUpdateDto
  ): Promise<MobileDevice> {
    const device = await this.getDeviceDetails(userId, deviceId);

    Object.assign(device, {
      ...updateDto,
      lastActivity: new Date(),
    });

    return this.deviceRepository.save(device);
  }

  /**
   * 删除设备
   */
  async deleteDevice(userId: string, deviceId: string): Promise<void> {
    const device = await this.getDeviceDetails(userId, deviceId);

    // 软删除：设置为非活跃状态
    device.isActive = false;
    device.isOnline = false;
    await this.deviceRepository.save(device);

    // 关闭所有活跃会话
    await this.sessionRepository.update(
      { deviceId, isActive: true },
      { isActive: false, disconnectedAt: new Date(), disconnectReason: 'device_deleted' }
    );
  }

  /**
   * 设置设备在线状态
   */
  async setDeviceOnlineStatus(userId: string, deviceId: string, isOnline: boolean): Promise<void> {
    const device = await this.getDeviceDetails(userId, deviceId);

    device.isOnline = isOnline;
    device.lastActivity = new Date();

    await this.deviceRepository.save(device);
  }

  /**
   * 获取设备统计信息
   */
  async getDeviceStats(userId: string, deviceId: string): Promise<DeviceStats> {
    const device = await this.getDeviceDetails(userId, deviceId);

    // 获取会话统计
    const totalSessions = await this.sessionRepository.count({
      where: { deviceId }
    });

    const activeSessions = await this.sessionRepository.count({
      where: { deviceId, isActive: true }
    });

    // 计算成功率
    const successRate = device.totalSyncs > 0 
      ? (device.successfulSyncs / device.totalSyncs) * 100 
      : 0;

    // 计算运行时间（简化实现）
    const totalUptime = device.firstRegistered 
      ? Date.now() - device.firstRegistered.getTime()
      : 0;

    return {
      totalSyncs: device.totalSyncs,
      successfulSyncs: device.successfulSyncs,
      failedSyncs: device.failedSyncs,
      successRate: Math.round(successRate * 100) / 100,
      avgSyncTime: 1500, // 简化实现
      lastSyncTime: device.lastSyncTime,
      totalSessions,
      activeSessions,
      totalUptime,
      registrationDate: device.firstRegistered || device.createdAt,
    };
  }

  /**
   * 创建设备会话
   */
  async createDeviceSession(
    userId: string,
    deviceId: string,
    socketId: string,
    connectionInfo?: any
  ): Promise<DeviceSession> {
    // 关闭该设备的其他活跃会话
    await this.sessionRepository.update(
      { deviceId, isActive: true },
      { isActive: false, disconnectedAt: new Date(), disconnectReason: 'new_session' }
    );

    const session = this.sessionRepository.create({
      sessionId: uuidv4(),
      userId,
      deviceId,
      socketId,
      connectionInfo,
      isActive: true,
      connectedAt: new Date(),
      lastActivity: new Date(),
    });

    // 更新设备在线状态
    await this.setDeviceOnlineStatus(userId, deviceId, true);

    return this.sessionRepository.save(session);
  }

  /**
   * 关闭设备会话
   */
  async closeDeviceSession(
    socketId: string,
    reason: string = 'client_disconnect'
  ): Promise<void> {
    const session = await this.sessionRepository.findOne({
      where: { socketId, isActive: true }
    });

    if (session) {
      session.isActive = false;
      session.disconnectedAt = new Date();
      session.disconnectReason = reason;
      await this.sessionRepository.save(session);

      // 检查设备是否还有其他活跃会话
      const activeSessionCount = await this.sessionRepository.count({
        where: { deviceId: session.deviceId, isActive: true }
      });

      // 如果没有其他活跃会话，设置设备为离线
      if (activeSessionCount === 0) {
        await this.deviceRepository.update(
          { deviceId: session.deviceId },
          { isOnline: false, lastActivity: new Date() }
        );
      }
    }
  }

  /**
   * 更新会话活动时间
   */
  async updateSessionActivity(socketId: string): Promise<void> {
    await this.sessionRepository.update(
      { socketId, isActive: true },
      { lastActivity: new Date() }
    );
  }

  /**
   * 获取用户在线设备
   */
  async getUserOnlineDevices(userId: string): Promise<MobileDevice[]> {
    return this.deviceRepository.find({
      where: { userId, isOnline: true, isActive: true },
      order: { lastActivity: 'DESC' }
    });
  }
}
