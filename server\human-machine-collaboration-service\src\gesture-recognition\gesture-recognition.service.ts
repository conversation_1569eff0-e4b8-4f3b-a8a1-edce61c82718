import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HandTrackingService } from './hand-tracking.service';
import { GesturePatternService } from './gesture-pattern.service';
import { MotionAnalysisService } from './motion-analysis.service';

/**
 * 手势识别服务
 * 统一管理手势识别、动作捕捉和模式匹配
 */
@Injectable()
export class GestureRecognitionService {
  private readonly logger = new Logger(GestureRecognitionService.name);
  private isInitialized = false;
  private recognitionSession: Map<string, any> = new Map();

  constructor(
    private readonly handTrackingService: HandTrackingService,
    private readonly gesturePatternService: GesturePatternService,
    private readonly motionAnalysisService: MotionAnalysisService,
    private readonly configService: ConfigService,
  ) {
    this.initializeService();
  }

  /**
   * 初始化手势识别服务
   */
  private async initializeService(): Promise<void> {
    try {
      // 初始化手部追踪
      await this.handTrackingService.initialize();
      
      // 加载手势模式
      await this.gesturePatternService.loadPatterns();
      
      // 初始化运动分析
      await this.motionAnalysisService.initialize();
      
      this.isInitialized = true;
      this.logger.log('手势识别服务初始化完成');
    } catch (error) {
      this.logger.error('初始化手势识别服务失败:', error);
    }
  }

  /**
   * 开始手势识别会话
   * @param userId 用户ID
   * @param sessionId 会话ID
   * @param options 识别选项
   * @returns 会话信息
   */
  async startRecognitionSession(
    userId: string,
    sessionId: string,
    options?: any,
  ): Promise<any> {
    try {
      if (!this.isInitialized) {
        throw new Error('手势识别服务未初始化');
      }

      const session = {
        userId,
        sessionId,
        startTime: new Date(),
        isActive: true,
        recognizedGestures: [],
        currentFrame: 0,
        options: {
          confidenceThreshold: options?.confidenceThreshold || 0.8,
          trackingMode: options?.trackingMode || 'hands',
          enableMotionAnalysis: options?.enableMotionAnalysis !== false,
          bufferSize: options?.bufferSize || 10,
          ...options,
        },
      };

      this.recognitionSession.set(sessionId, session);

      this.logger.log(`手势识别会话已启动: ${sessionId} - 用户: ${userId}`);
      return {
        success: true,
        sessionId,
        message: '手势识别会话已启动',
        options: session.options,
      };

    } catch (error) {
      this.logger.error('启动手势识别会话失败:', error);
      throw error;
    }
  }

  /**
   * 处理视频帧
   * @param sessionId 会话ID
   * @param frameData 帧数据
   * @returns 识别结果
   */
  async processFrame(sessionId: string, frameData: any): Promise<any> {
    try {
      const session = this.recognitionSession.get(sessionId);
      if (!session || !session.isActive) {
        throw new Error(`会话不存在或已结束: ${sessionId}`);
      }

      const startTime = Date.now();

      // 1. 手部追踪
      const handTrackingResult = await this.handTrackingService.trackHands(frameData);

      if (!handTrackingResult.success || handTrackingResult.hands.length === 0) {
        return {
          success: false,
          message: '未检测到手部',
          frameNumber: session.currentFrame++,
          processingTime: Date.now() - startTime,
        };
      }

      // 2. 手势识别
      const gestureResults = [];
      for (const hand of handTrackingResult.hands) {
        const gestureResult = await this.recognizeGesture(hand, session.options);
        if (gestureResult.success) {
          gestureResults.push(gestureResult);
        }
      }

      // 3. 运动分析（如果启用）
      let motionAnalysis = null;
      if (session.options.enableMotionAnalysis) {
        motionAnalysis = await this.motionAnalysisService.analyzeMotion(
          handTrackingResult.hands,
          session.sessionId,
        );
      }

      // 4. 更新会话状态
      session.currentFrame++;
      if (gestureResults.length > 0) {
        session.recognizedGestures.push(...gestureResults);
      }

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        frameNumber: session.currentFrame - 1,
        hands: handTrackingResult.hands,
        gestures: gestureResults,
        motionAnalysis,
        processingTime,
        sessionStats: {
          totalFrames: session.currentFrame,
          totalGestures: session.recognizedGestures.length,
          sessionDuration: Date.now() - session.startTime.getTime(),
        },
      };

    } catch (error) {
      this.logger.error('处理视频帧失败:', error);
      throw error;
    }
  }

  /**
   * 识别手势
   * @param handData 手部数据
   * @param options 识别选项
   * @returns 识别结果
   */
  private async recognizeGesture(handData: any, options: any): Promise<any> {
    try {
      // 获取手势模式
      const patterns = await this.gesturePatternService.getActivePatterns();

      let bestMatch = null;
      let maxConfidence = 0;

      // 遍历所有模式进行匹配
      for (const pattern of patterns) {
        const confidence = await this.calculateGestureConfidence(handData, pattern);
        
        if (confidence > maxConfidence && confidence >= options.confidenceThreshold) {
          maxConfidence = confidence;
          bestMatch = pattern;
        }
      }

      if (bestMatch) {
        // 更新模式使用统计
        await this.gesturePatternService.updateUsageStats(bestMatch.id, maxConfidence);

        return {
          success: true,
          gesture: {
            id: bestMatch.id,
            name: bestMatch.patternName,
            type: bestMatch.gestureType,
            confidence: maxConfidence,
            action: bestMatch.associatedAction,
            parameters: bestMatch.actionParameters,
          },
          handData,
          timestamp: new Date(),
        };
      }

      return {
        success: false,
        message: '未识别到已知手势',
        confidence: maxConfidence,
      };

    } catch (error) {
      this.logger.error('识别手势失败:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 计算手势置信度
   * @param handData 手部数据
   * @param pattern 手势模式
   * @returns 置信度
   */
  private async calculateGestureConfidence(handData: any, pattern: any): Promise<number> {
    try {
      let confidence = 0;

      switch (pattern.gestureType) {
        case 'static':
          confidence = this.calculateStaticGestureConfidence(handData, pattern);
          break;
        case 'dynamic':
          confidence = this.calculateDynamicGestureConfidence(handData, pattern);
          break;
        case 'sequence':
          confidence = this.calculateSequenceGestureConfidence(handData, pattern);
          break;
        default:
          confidence = 0;
      }

      return Math.max(0, Math.min(1, confidence));

    } catch (error) {
      this.logger.error('计算手势置信度失败:', error);
      return 0;
    }
  }

  /**
   * 计算静态手势置信度
   */
  private calculateStaticGestureConfidence(handData: any, pattern: any): number {
    // 简化的静态手势匹配算法
    const landmarks = handData.landmarks;
    const patternKeyPoints = pattern.keyPoints;

    if (!landmarks || !patternKeyPoints) return 0;

    let totalDistance = 0;
    let validPoints = 0;

    for (let i = 0; i < Math.min(landmarks.length, patternKeyPoints.length); i++) {
      const landmark = landmarks[i];
      const keyPoint = patternKeyPoints[i];

      if (landmark && keyPoint) {
        const distance = Math.sqrt(
          Math.pow(landmark.x - keyPoint.x, 2) +
          Math.pow(landmark.y - keyPoint.y, 2) +
          Math.pow(landmark.z - keyPoint.z, 2)
        );
        totalDistance += distance;
        validPoints++;
      }
    }

    if (validPoints === 0) return 0;

    const averageDistance = totalDistance / validPoints;
    const maxDistance = 0.1; // 最大允许距离
    
    return Math.max(0, 1 - averageDistance / maxDistance);
  }

  /**
   * 计算动态手势置信度
   */
  private calculateDynamicGestureConfidence(handData: any, pattern: any): number {
    // 简化的动态手势匹配算法
    // 实际实现中需要考虑时间序列和运动轨迹
    return 0.7; // 模拟置信度
  }

  /**
   * 计算序列手势置信度
   */
  private calculateSequenceGestureConfidence(handData: any, pattern: any): number {
    // 简化的序列手势匹配算法
    // 实际实现中需要维护手势序列状态
    return 0.6; // 模拟置信度
  }

  /**
   * 停止手势识别会话
   * @param sessionId 会话ID
   * @returns 会话报告
   */
  async stopRecognitionSession(sessionId: string): Promise<any> {
    try {
      const session = this.recognitionSession.get(sessionId);
      if (!session) {
        throw new Error(`会话不存在: ${sessionId}`);
      }

      session.isActive = false;
      session.endTime = new Date();

      // 生成会话报告
      const report = {
        sessionId,
        userId: session.userId,
        duration: session.endTime.getTime() - session.startTime.getTime(),
        totalFrames: session.currentFrame,
        totalGestures: session.recognizedGestures.length,
        recognizedGestures: session.recognizedGestures,
        averageProcessingTime: this.calculateAverageProcessingTime(session),
        gestureFrequency: this.calculateGestureFrequency(session),
      };

      // 清理会话数据
      this.recognitionSession.delete(sessionId);

      this.logger.log(`手势识别会话已结束: ${sessionId}`);
      return report;

    } catch (error) {
      this.logger.error('停止手势识别会话失败:', error);
      throw error;
    }
  }

  /**
   * 获取支持的手势列表
   */
  async getSupportedGestures(): Promise<any[]> {
    try {
      return await this.gesturePatternService.getAllPatterns();
    } catch (error) {
      this.logger.error('获取支持手势列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取手势识别统计
   */
  async getRecognitionStats(): Promise<any> {
    try {
      const activeSessions = Array.from(this.recognitionSession.values()).filter(s => s.isActive);
      const patternStats = await this.gesturePatternService.getUsageStatistics();

      return {
        activeSessions: activeSessions.length,
        totalPatterns: patternStats.totalPatterns,
        mostUsedGestures: patternStats.mostUsed,
        averageAccuracy: patternStats.averageAccuracy,
        systemPerformance: {
          memoryUsage: process.memoryUsage(),
          uptime: process.uptime(),
        },
      };

    } catch (error) {
      this.logger.error('获取手势识别统计失败:', error);
      throw error;
    }
  }

  /**
   * 计算平均处理时间
   */
  private calculateAverageProcessingTime(session: any): number {
    // 简化实现，实际中需要记录每帧的处理时间
    return 50; // 模拟50ms平均处理时间
  }

  /**
   * 计算手势频率
   */
  private calculateGestureFrequency(session: any): any {
    const frequency: { [key: string]: number } = {};
    
    session.recognizedGestures.forEach((gesture: any) => {
      const name = gesture.gesture.name;
      frequency[name] = (frequency[name] || 0) + 1;
    });

    return frequency;
  }
}
