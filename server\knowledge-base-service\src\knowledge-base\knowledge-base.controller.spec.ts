/**
 * 知识库控制器单元测试
 */
import { Test, TestingModule } from '@nestjs/testing';
import { KnowledgeBaseController } from './knowledge-base.controller';
import { KnowledgeBaseService } from './knowledge-base.service';
import { CreateKnowledgeBaseDto, SearchKnowledgeBaseDto } from './dto/create-knowledge-base.dto';
import { KnowledgeBaseStatus } from './entities/knowledge-base.entity';

describe('KnowledgeBaseController', () => {
  let controller: KnowledgeBaseController;
  let service: KnowledgeBaseService;

  const mockKnowledgeBaseService = {
    create: jest.fn(),
    findByOwner: jest.fn(),
    findByScene: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    search: jest.fn(),
    getStatistics: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [KnowledgeBaseController],
      providers: [
        {
          provide: KnowledgeBaseService,
          useValue: mockKnowledgeBaseService,
        },
      ],
    }).compile();

    controller = module.get<KnowledgeBaseController>(KnowledgeBaseController);
    service = module.get<KnowledgeBaseService>(KnowledgeBaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a knowledge base', async () => {
      const createDto: CreateKnowledgeBaseDto = {
        name: '测试知识库',
        description: '这是一个测试知识库',
      };
      const user = { id: 'user_123' };
      const mockResult = {
        id: 'kb_123',
        ...createDto,
        ownerId: user.id,
        status: KnowledgeBaseStatus.ACTIVE,
      };

      mockKnowledgeBaseService.create.mockResolvedValue(mockResult);

      const result = await controller.create(user, createDto);

      expect(service.create).toHaveBeenCalledWith(user.id, createDto);
      expect(result).toEqual(mockResult);
    });
  });

  describe('findAll', () => {
    it('should return paginated knowledge bases', async () => {
      const user = { id: 'user_123' };
      const page = 1;
      const limit = 10;
      const mockResult = {
        data: [
          { id: 'kb_1', name: '知识库1' },
          { id: 'kb_2', name: '知识库2' },
        ],
        total: 2,
        page,
        limit,
      };

      mockKnowledgeBaseService.findByOwner.mockResolvedValue(mockResult);

      const result = await controller.findAll(user, page, limit);

      expect(service.findByOwner).toHaveBeenCalledWith(user.id, page, limit);
      expect(result).toEqual(mockResult);
    });
  });

  describe('findByScene', () => {
    it('should return knowledge bases by scene', async () => {
      const sceneId = 'scene_123';
      const mockResult = [
        { id: 'kb_1', name: '知识库1', sceneId },
        { id: 'kb_2', name: '知识库2', sceneId },
      ];

      mockKnowledgeBaseService.findByScene.mockResolvedValue(mockResult);

      const result = await controller.findByScene(sceneId);

      expect(service.findByScene).toHaveBeenCalledWith(sceneId);
      expect(result).toEqual(mockResult);
    });
  });

  describe('findOne', () => {
    it('should return a knowledge base', async () => {
      const user = { id: 'user_123' };
      const id = 'kb_123';
      const mockResult = {
        id,
        name: '测试知识库',
        ownerId: user.id,
      };

      mockKnowledgeBaseService.findOne.mockResolvedValue(mockResult);

      const result = await controller.findOne(user, id);

      expect(service.findOne).toHaveBeenCalledWith(id, user.id);
      expect(result).toEqual(mockResult);
    });
  });

  describe('search', () => {
    it('should perform search in knowledge base', async () => {
      const user = { id: 'user_123' };
      const id = 'kb_123';
      const searchDto: SearchKnowledgeBaseDto = {
        query: '测试查询',
        topK: 5,
        threshold: 0.7,
      };
      const mockResult = {
        query: searchDto.query,
        results: [
          { id: 'result_1', content: '搜索结果1', score: 0.9 },
        ],
        knowledgeBase: {
          id,
          name: '测试知识库',
        },
      };

      mockKnowledgeBaseService.search.mockResolvedValue(mockResult);

      const result = await controller.search(user, id, searchDto);

      expect(service.search).toHaveBeenCalledWith(id, searchDto, user.id);
      expect(result).toEqual(mockResult);
    });
  });

  describe('getStatistics', () => {
    it('should return knowledge base statistics', async () => {
      const user = { id: 'user_123' };
      const id = 'kb_123';
      const mockResult = {
        id,
        name: '测试知识库',
        statistics: {
          documentCount: 5,
          totalSize: 1024000,
          vectorCount: 100,
        },
      };

      mockKnowledgeBaseService.getStatistics.mockResolvedValue(mockResult);

      const result = await controller.getStatistics(user, id);

      expect(service.getStatistics).toHaveBeenCalledWith(id, user.id);
      expect(result).toEqual(mockResult);
    });
  });
});
