import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * 协同类型枚举
 */
export enum CollaborationType {
  DEMAND_PLANNING = 'demand_planning',
  INVENTORY_OPTIMIZATION = 'inventory_optimization',
  CAPACITY_SHARING = 'capacity_sharing',
  RISK_MITIGATION = 'risk_mitigation',
  SUSTAINABILITY = 'sustainability',
  INNOVATION = 'innovation',
}

/**
 * 计划状态枚举
 */
export enum PlanStatus {
  DRAFT = 'draft',
  PROPOSED = 'proposed',
  APPROVED = 'approved',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

/**
 * 协同计划实体
 */
@Entity('collaboration_plans')
@Index(['type', 'status'])
@Index(['startDate', 'endDate'])
export class CollaborationPlan {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'plan_id', length: 100, unique: true })
  planId: string;

  @Column({ length: 200 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: CollaborationType,
  })
  type: CollaborationType;

  @Column({
    type: 'enum',
    enum: PlanStatus,
    default: PlanStatus.DRAFT,
  })
  status: PlanStatus;

  @Column({ name: 'start_date', type: 'datetime' })
  startDate: Date;

  @Column({ name: 'end_date', type: 'datetime' })
  endDate: Date;

  @Column({ name: 'participating_nodes', type: 'json', comment: '参与节点' })
  participatingNodes: {
    nodeId: string;
    nodeName: string;
    role: string;
    responsibilities: string[];
    commitments: any;
  }[];

  @Column({ type: 'json', comment: '协同目标' })
  objectives: {
    objectiveType: string;
    description: string;
    targetValue: number;
    currentValue: number;
    unit: string;
    priority: string;
  }[];

  @Column({ name: 'collaboration_activities', type: 'json', comment: '协同活动' })
  collaborationActivities: {
    activityId: string;
    activityName: string;
    description: string;
    responsibleNodes: string[];
    startDate: Date;
    endDate: Date;
    status: string;
    deliverables: string[];
  }[];

  @Column({ name: 'resource_sharing', type: 'json', nullable: true, comment: '资源共享' })
  resourceSharing: {
    resourceType: string;
    sharingNode: string;
    receivingNodes: string[];
    quantity: number;
    unit: string;
    sharingPeriod: {
      start: Date;
      end: Date;
    };
    terms: any;
  }[];

  @Column({ name: 'information_sharing', type: 'json', nullable: true, comment: '信息共享' })
  informationSharing: {
    informationType: string;
    dataSource: string;
    recipients: string[];
    frequency: string;
    format: string;
    securityLevel: string;
  }[];

  @Column({ name: 'performance_metrics', type: 'json', nullable: true, comment: '绩效指标' })
  performanceMetrics: {
    metricName: string;
    targetValue: number;
    currentValue: number;
    unit: string;
    measurementFrequency: string;
    responsibleNode: string;
  }[];

  @Column({ name: 'risk_management', type: 'json', nullable: true, comment: '风险管理' })
  riskManagement: {
    riskType: string;
    probability: number;
    impact: number;
    mitigationStrategy: string;
    responsibleNodes: string[];
    contingencyPlan: string;
  }[];

  @Column({ name: 'governance_structure', type: 'json', nullable: true, comment: '治理结构' })
  governanceStructure: {
    governanceLevel: string;
    decisionMakers: string[];
    meetingFrequency: string;
    escalationProcess: string;
    conflictResolution: string;
  };

  @Column({ name: 'financial_arrangements', type: 'json', nullable: true, comment: '财务安排' })
  financialArrangements: {
    costSharing: any;
    revenueSharing: any;
    investmentCommitments: any;
    paymentTerms: string;
  };

  @Column({ name: 'success_criteria', type: 'json', nullable: true, comment: '成功标准' })
  successCriteria: {
    criterion: string;
    measurementMethod: string;
    targetValue: number;
    weight: number;
  }[];

  @Column({ type: 'json', nullable: true, comment: '扩展属性' })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
