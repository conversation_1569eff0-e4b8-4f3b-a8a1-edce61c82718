import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * 知识库服务
 * 提供维护、故障排除、培训等知识内容的搜索和管理
 */
@Injectable()
export class KnowledgeBaseService {
  private readonly logger = new Logger(KnowledgeBaseService.name);
  private knowledgeBase: Map<string, any> = new Map();

  constructor(private readonly configService: ConfigService) {
    this.initializeKnowledgeBase();
  }

  /**
   * 初始化知识库
   */
  private initializeKnowledgeBase(): void {
    try {
      // 维护知识
      this.loadMaintenanceKnowledge();
      
      // 故障排除知识
      this.loadTroubleshootingKnowledge();
      
      // 培训知识
      this.loadTrainingKnowledge();
      
      // 常见问题
      this.loadFAQKnowledge();
      
      this.logger.log(`知识库初始化完成，共加载 ${this.knowledgeBase.size} 条知识`);
    } catch (error) {
      this.logger.error('初始化知识库失败:', error);
    }
  }

  /**
   * 搜索维护知识
   */
  async searchMaintenance(params: {
    equipment?: string;
    procedure?: string;
    query: string;
  }): Promise<any[]> {
    try {
      const results: any[] = [];
      const { equipment, procedure, query } = params;
      
      for (const [key, knowledge] of this.knowledgeBase) {
        if (knowledge.type === 'maintenance') {
          let score = 0;
          
          // 设备匹配
          if (equipment && knowledge.equipment?.includes(equipment)) {
            score += 0.4;
          }
          
          // 程序匹配
          if (procedure && knowledge.procedure?.includes(procedure)) {
            score += 0.3;
          }
          
          // 文本相似度
          const textScore = this.calculateTextSimilarity(query, knowledge.content);
          score += textScore * 0.3;
          
          if (score > 0.3) {
            results.push({
              ...knowledge,
              score,
              id: key,
            });
          }
        }
      }
      
      return results.sort((a, b) => b.score - a.score).slice(0, 5);
    } catch (error) {
      this.logger.error('搜索维护知识失败:', error);
      return [];
    }
  }

  /**
   * 搜索故障排除知识
   */
  async searchTroubleshooting(params: {
    symptom?: string;
    equipment?: string;
    query: string;
  }): Promise<any[]> {
    try {
      const results: any[] = [];
      const { symptom, equipment, query } = params;
      
      for (const [key, knowledge] of this.knowledgeBase) {
        if (knowledge.type === 'troubleshooting') {
          let score = 0;
          
          // 症状匹配
          if (symptom && knowledge.symptoms?.includes(symptom)) {
            score += 0.5;
          }
          
          // 设备匹配
          if (equipment && knowledge.equipment?.includes(equipment)) {
            score += 0.3;
          }
          
          // 文本相似度
          const textScore = this.calculateTextSimilarity(query, knowledge.description);
          score += textScore * 0.2;
          
          if (score > 0.3) {
            results.push({
              ...knowledge,
              score,
              id: key,
            });
          }
        }
      }
      
      return results.sort((a, b) => b.score - a.score).slice(0, 5);
    } catch (error) {
      this.logger.error('搜索故障排除知识失败:', error);
      return [];
    }
  }

  /**
   * 搜索培训知识
   */
  async searchTraining(params: {
    skill?: string;
    level?: string;
    query: string;
  }): Promise<any[]> {
    try {
      const results: any[] = [];
      const { skill, level, query } = params;
      
      for (const [key, knowledge] of this.knowledgeBase) {
        if (knowledge.type === 'training') {
          let score = 0;
          
          // 技能匹配
          if (skill && knowledge.skills?.includes(skill)) {
            score += 0.4;
          }
          
          // 级别匹配
          if (level && knowledge.level === level) {
            score += 0.3;
          }
          
          // 文本相似度
          const textScore = this.calculateTextSimilarity(query, knowledge.description);
          score += textScore * 0.3;
          
          if (score > 0.3) {
            results.push({
              ...knowledge,
              score,
              id: key,
            });
          }
        }
      }
      
      return results.sort((a, b) => b.score - a.score).slice(0, 5);
    } catch (error) {
      this.logger.error('搜索培训知识失败:', error);
      return [];
    }
  }

  /**
   * 搜索常见问题
   */
  async searchFAQ(query: string): Promise<any[]> {
    try {
      const results: any[] = [];
      
      for (const [key, knowledge] of this.knowledgeBase) {
        if (knowledge.type === 'faq') {
          const questionScore = this.calculateTextSimilarity(query, knowledge.question);
          const keywordScore = this.calculateKeywordMatch(query, knowledge.keywords || []);
          const score = questionScore * 0.7 + keywordScore * 0.3;
          
          if (score > 0.3) {
            results.push({
              ...knowledge,
              score,
              id: key,
            });
          }
        }
      }
      
      return results.sort((a, b) => b.score - a.score).slice(0, 3);
    } catch (error) {
      this.logger.error('搜索FAQ失败:', error);
      return [];
    }
  }

  /**
   * 加载维护知识
   */
  private loadMaintenanceKnowledge(): void {
    const maintenanceData = [
      {
        type: 'maintenance',
        equipment: ['CNC机床', '数控机床'],
        procedure: ['日常维护', '保养'],
        title: 'CNC机床日常维护',
        content: '1. 检查冷却液液位\n2. 清洁工作台面\n3. 检查刀具磨损\n4. 润滑导轨\n5. 检查电气连接',
        frequency: '每日',
        duration: '30分钟',
        tools: ['清洁布', '润滑油', '检查表'],
        safety: ['佩戴防护眼镜', '确保设备停机'],
      },
      {
        type: 'maintenance',
        equipment: ['机器人', '工业机器人'],
        procedure: ['定期检查', '保养'],
        title: '工业机器人维护',
        content: '1. 检查关节润滑\n2. 校准位置精度\n3. 检查电缆磨损\n4. 清洁传感器\n5. 更新软件',
        frequency: '每周',
        duration: '60分钟',
        tools: ['校准工具', '清洁剂', '润滑脂'],
        safety: ['断电操作', '锁定安全开关'],
      },
    ];

    maintenanceData.forEach((item, index) => {
      this.knowledgeBase.set(`maintenance_${index}`, item);
    });
  }

  /**
   * 加载故障排除知识
   */
  private loadTroubleshootingKnowledge(): void {
    const troubleshootingData = [
      {
        type: 'troubleshooting',
        equipment: ['CNC机床'],
        symptoms: ['设备故障', '不工作'],
        title: 'CNC机床不启动',
        description: 'CNC机床无法正常启动的故障排除',
        steps: [
          { step: 1, description: '检查电源连接是否正常' },
          { step: 2, description: '确认急停按钮是否释放' },
          { step: 3, description: '检查安全门是否关闭' },
          { step: 4, description: '查看控制面板错误代码' },
          { step: 5, description: '联系技术支持' },
        ],
        estimatedTime: '15分钟',
        difficulty: 'easy',
      },
      {
        type: 'troubleshooting',
        equipment: ['机器人'],
        symptoms: ['异常噪音', '噪音'],
        title: '机器人异常噪音',
        description: '机器人运行时产生异常噪音的排除方法',
        steps: [
          { step: 1, description: '停止机器人运行' },
          { step: 2, description: '检查关节润滑状态' },
          { step: 3, description: '检查传动部件磨损' },
          { step: 4, description: '调整运行参数' },
          { step: 5, description: '重新校准' },
        ],
        estimatedTime: '30分钟',
        difficulty: 'medium',
      },
    ];

    troubleshootingData.forEach((item, index) => {
      this.knowledgeBase.set(`troubleshooting_${index}`, item);
    });
  }

  /**
   * 加载培训知识
   */
  private loadTrainingKnowledge(): void {
    const trainingData = [
      {
        type: 'training',
        title: 'CNC机床基础操作',
        description: '学习CNC机床的基本操作方法',
        skills: ['设备操作', 'CNC编程'],
        level: 'beginner',
        duration: '2小时',
        modules: [
          '机床结构认识',
          '控制面板操作',
          '程序输入方法',
          '安全操作规程',
        ],
        prerequisites: ['机械基础知识'],
        certification: true,
      },
      {
        type: 'training',
        title: '工业机器人编程',
        description: '掌握工业机器人的编程技能',
        skills: ['编程', '机器人操作'],
        level: 'intermediate',
        duration: '4小时',
        modules: [
          '编程语言基础',
          '路径规划',
          '传感器集成',
          '调试技巧',
        ],
        prerequisites: ['机器人基础操作'],
        certification: true,
      },
    ];

    trainingData.forEach((item, index) => {
      this.knowledgeBase.set(`training_${index}`, item);
    });
  }

  /**
   * 加载FAQ知识
   */
  private loadFAQKnowledge(): void {
    const faqData = [
      {
        type: 'faq',
        question: '如何开始AR/VR培训？',
        answer: '您可以通过以下步骤开始AR/VR培训：\n1. 选择培训场景\n2. 佩戴VR设备\n3. 按照语音提示操作\n4. 完成培训任务',
        keywords: ['AR', 'VR', '培训', '开始'],
        category: '使用指南',
      },
      {
        type: 'faq',
        question: '设备维护频率是多少？',
        answer: '不同设备的维护频率不同：\n- CNC机床：每日检查，每周深度保养\n- 工业机器人：每周检查，每月深度保养\n- 传送带：每日检查，每两周保养',
        keywords: ['维护', '频率', '保养'],
        category: '维护指南',
      },
    ];

    faqData.forEach((item, index) => {
      this.knowledgeBase.set(`faq_${index}`, item);
    });
  }

  /**
   * 计算文本相似度
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    // 简单的文本相似度计算
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);
    
    const intersection = words1.filter(word => words2.includes(word));
    const union = [...new Set([...words1, ...words2])];
    
    return intersection.length / union.length;
  }

  /**
   * 计算关键词匹配度
   */
  private calculateKeywordMatch(query: string, keywords: string[]): number {
    const queryWords = query.toLowerCase().split(/\s+/);
    const matchCount = keywords.filter(keyword => 
      queryWords.some(word => word.includes(keyword.toLowerCase()))
    ).length;
    
    return keywords.length > 0 ? matchCount / keywords.length : 0;
  }
}
