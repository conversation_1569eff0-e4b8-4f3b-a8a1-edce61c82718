-- 监控服务数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS monitoring CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE monitoring;

-- 创建指标表
CREATE TABLE IF NOT EXISTS metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(255) NOT NULL COMMENT '指标名称',
    metric_type ENUM('counter', 'gauge', 'histogram', 'summary') NOT NULL COMMENT '指标类型',
    value DECIMAL(20,6) NOT NULL COMMENT '指标值',
    labels JSON COMMENT '标签',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_metric_name (metric_name),
    INDEX idx_timestamp (timestamp),
    INDEX idx_metric_name_timestamp (metric_name, timestamp)
) ENGINE=InnoDB COMMENT='指标数据表';

-- 创建系统指标表
CREATE TABLE IF NOT EXISTS system_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    cpu_usage DECIMAL(5,2) NOT NULL COMMENT 'CPU使用率(%)',
    memory_usage DECIMAL(5,2) NOT NULL COMMENT '内存使用率(%)',
    disk_usage DECIMAL(5,2) NOT NULL COMMENT '磁盘使用率(%)',
    network_usage DECIMAL(5,2) NOT NULL COMMENT '网络使用率(%)',
    load_average DECIMAL(10,2) COMMENT '系统负载',
    process_count INT COMMENT '进程数量',
    thread_count INT COMMENT '线程数量',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_timestamp (timestamp)
) ENGINE=InnoDB COMMENT='系统指标表';

-- 创建服务指标表
CREATE TABLE IF NOT EXISTS service_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    service_name VARCHAR(255) NOT NULL COMMENT '服务名称',
    status ENUM('healthy', 'unhealthy', 'unknown') NOT NULL COMMENT '服务状态',
    response_time DECIMAL(10,2) COMMENT '响应时间(ms)',
    error_rate DECIMAL(5,2) COMMENT '错误率(%)',
    throughput DECIMAL(10,2) COMMENT '吞吐量(req/s)',
    cpu_usage DECIMAL(5,2) COMMENT 'CPU使用率(%)',
    memory_usage DECIMAL(5,2) COMMENT '内存使用率(%)',
    active_connections INT COMMENT '活跃连接数',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_service_name (service_name),
    INDEX idx_timestamp (timestamp),
    INDEX idx_service_name_timestamp (service_name, timestamp)
) ENGINE=InnoDB COMMENT='服务指标表';

-- 创建告警规则表
CREATE TABLE IF NOT EXISTS alert_rules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT '规则名称',
    description TEXT COMMENT '规则描述',
    condition TEXT NOT NULL COMMENT '告警条件',
    severity ENUM('info', 'warning', 'critical') NOT NULL COMMENT '告警级别',
    duration VARCHAR(50) DEFAULT '5m' COMMENT '持续时间',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    notification_channels JSON COMMENT '通知渠道',
    labels JSON COMMENT '标签',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_name (name),
    INDEX idx_enabled (enabled)
) ENGINE=InnoDB COMMENT='告警规则表';

-- 创建告警记录表
CREATE TABLE IF NOT EXISTS alerts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    rule_id BIGINT NOT NULL COMMENT '规则ID',
    alert_name VARCHAR(255) NOT NULL COMMENT '告警名称',
    severity ENUM('info', 'warning', 'critical') NOT NULL COMMENT '告警级别',
    status ENUM('firing', 'resolved') NOT NULL COMMENT '告警状态',
    message TEXT COMMENT '告警消息',
    labels JSON COMMENT '标签',
    annotations JSON COMMENT '注释',
    starts_at TIMESTAMP NOT NULL COMMENT '开始时间',
    ends_at TIMESTAMP NULL COMMENT '结束时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (rule_id) REFERENCES alert_rules(id) ON DELETE CASCADE,
    INDEX idx_rule_id (rule_id),
    INDEX idx_status (status),
    INDEX idx_starts_at (starts_at)
) ENGINE=InnoDB COMMENT='告警记录表';

-- 创建健康检查表
CREATE TABLE IF NOT EXISTS health_checks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    service_name VARCHAR(255) NOT NULL COMMENT '服务名称',
    endpoint VARCHAR(500) NOT NULL COMMENT '检查端点',
    method VARCHAR(10) DEFAULT 'GET' COMMENT 'HTTP方法',
    timeout_seconds INT DEFAULT 10 COMMENT '超时时间(秒)',
    interval_seconds INT DEFAULT 30 COMMENT '检查间隔(秒)',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_service_endpoint (service_name, endpoint),
    INDEX idx_enabled (enabled)
) ENGINE=InnoDB COMMENT='健康检查配置表';

-- 创建健康检查历史表
CREATE TABLE IF NOT EXISTS health_check_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    health_check_id BIGINT NOT NULL COMMENT '健康检查ID',
    status ENUM('success', 'failure', 'timeout') NOT NULL COMMENT '检查状态',
    response_time DECIMAL(10,2) COMMENT '响应时间(ms)',
    status_code INT COMMENT 'HTTP状态码',
    error_message TEXT COMMENT '错误消息',
    checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '检查时间',
    FOREIGN KEY (health_check_id) REFERENCES health_checks(id) ON DELETE CASCADE,
    INDEX idx_health_check_id (health_check_id),
    INDEX idx_checked_at (checked_at),
    INDEX idx_status (status)
) ENGINE=InnoDB COMMENT='健康检查历史表';

-- 创建日志表
CREATE TABLE IF NOT EXISTS logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    service_name VARCHAR(255) NOT NULL COMMENT '服务名称',
    level ENUM('debug', 'info', 'warn', 'error', 'fatal') NOT NULL COMMENT '日志级别',
    message TEXT NOT NULL COMMENT '日志消息',
    context JSON COMMENT '上下文信息',
    trace_id VARCHAR(255) COMMENT '追踪ID',
    user_id VARCHAR(255) COMMENT '用户ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    INDEX idx_service_name (service_name),
    INDEX idx_level (level),
    INDEX idx_timestamp (timestamp),
    INDEX idx_trace_id (trace_id),
    INDEX idx_service_level_timestamp (service_name, level, timestamp)
) ENGINE=InnoDB COMMENT='日志表';

-- 创建日志查询表
CREATE TABLE IF NOT EXISTS log_queries (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    query_name VARCHAR(255) NOT NULL COMMENT '查询名称',
    query_text TEXT NOT NULL COMMENT '查询语句',
    description TEXT COMMENT '查询描述',
    created_by VARCHAR(255) COMMENT '创建者',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_created_by (created_by)
) ENGINE=InnoDB COMMENT='日志查询表';

-- 创建通知渠道表
CREATE TABLE IF NOT EXISTS notification_channels (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT '渠道名称',
    type ENUM('email', 'dingtalk', 'wechat', 'slack', 'webhook') NOT NULL COMMENT '渠道类型',
    config JSON NOT NULL COMMENT '渠道配置',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_name (name),
    INDEX idx_type (type),
    INDEX idx_enabled (enabled)
) ENGINE=InnoDB COMMENT='通知渠道表';

-- 创建通知历史表
CREATE TABLE IF NOT EXISTS notification_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    channel_id BIGINT NOT NULL COMMENT '渠道ID',
    alert_id BIGINT COMMENT '告警ID',
    recipient VARCHAR(500) NOT NULL COMMENT '接收者',
    subject VARCHAR(500) COMMENT '主题',
    content TEXT NOT NULL COMMENT '内容',
    status ENUM('pending', 'sent', 'failed') NOT NULL COMMENT '发送状态',
    error_message TEXT COMMENT '错误消息',
    sent_at TIMESTAMP NULL COMMENT '发送时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (channel_id) REFERENCES notification_channels(id) ON DELETE CASCADE,
    FOREIGN KEY (alert_id) REFERENCES alerts(id) ON DELETE SET NULL,
    INDEX idx_channel_id (channel_id),
    INDEX idx_alert_id (alert_id),
    INDEX idx_status (status),
    INDEX idx_sent_at (sent_at)
) ENGINE=InnoDB COMMENT='通知历史表';

-- 插入默认数据
INSERT INTO notification_channels (name, type, config, enabled) VALUES
('默认邮件通知', 'email', '{"host": "smtp.gmail.com", "port": 587, "secure": false, "from": "<EMAIL>"}', TRUE),
('默认钉钉通知', 'dingtalk', '{"webhook": "", "secret": ""}', FALSE),
('默认企业微信通知', 'wechat', '{"webhook": "", "key": ""}', FALSE);

INSERT INTO alert_rules (name, description, condition, severity, duration, enabled, notification_channels) VALUES
('CPU使用率过高', '当CPU使用率超过80%时触发告警', 'cpu_usage > 80', 'warning', '5m', TRUE, '["默认邮件通知"]'),
('内存使用率过高', '当内存使用率超过85%时触发告警', 'memory_usage > 85', 'warning', '5m', TRUE, '["默认邮件通知"]'),
('磁盘使用率过高', '当磁盘使用率超过90%时触发告警', 'disk_usage > 90', 'critical', '2m', TRUE, '["默认邮件通知"]'),
('服务响应时间过长', '当服务响应时间超过2秒时触发告警', 'response_time > 2000', 'warning', '3m', TRUE, '["默认邮件通知"]'),
('服务错误率过高', '当服务错误率超过5%时触发告警', 'error_rate > 5', 'warning', '3m', TRUE, '["默认邮件通知"]');

INSERT INTO health_checks (service_name, endpoint, method, timeout_seconds, interval_seconds, enabled) VALUES
('user-service', 'http://user-service:3001/api/v1/health', 'GET', 10, 30, TRUE),
('ecosystem-service', 'http://ecosystem-service:3002/api/v1/health', 'GET', 10, 30, TRUE),
('emotion-service', 'http://emotion-service:3004/api/v1/health', 'GET', 10, 30, TRUE),
('mes-service', 'http://mes-service:3005/api/v1/health', 'GET', 10, 30, TRUE),
('mobile-service', 'http://mobile-service:3006/api/v1/health', 'GET', 10, 30, TRUE);
