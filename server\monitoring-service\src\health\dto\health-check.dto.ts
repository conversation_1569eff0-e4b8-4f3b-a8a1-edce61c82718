import { IsString, IsUrl, IsOptional, IsInt, <PERSON>, <PERSON>, IsBoolean, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
  HEAD = 'HEAD',
  OPTIONS = 'OPTIONS',
}

/**
 * 创建健康检查DTO
 */
export class CreateHealthCheckDto {
  @ApiProperty({ description: '服务名称', example: 'user-service' })
  @IsString({ message: '服务名称必须是字符串' })
  serviceName: string;

  @ApiProperty({ description: '检查端点', example: 'http://user-service:3001/api/v1/health' })
  @IsUrl({}, { message: '检查端点必须是有效的URL' })
  endpoint: string;

  @ApiProperty({ 
    description: 'HTTP方法', 
    enum: HttpMethod, 
    default: HttpMethod.GET,
    example: HttpMethod.GET 
  })
  @IsOptional()
  @IsEnum(HttpMethod, { message: 'HTTP方法必须是有效值' })
  method?: HttpMethod = HttpMethod.GET;

  @ApiProperty({ description: '超时时间(秒)', minimum: 1, maximum: 300, default: 10, example: 10 })
  @IsOptional()
  @IsInt({ message: '超时时间必须是整数' })
  @Min(1, { message: '超时时间最少为1秒' })
  @Max(300, { message: '超时时间最多为300秒' })
  timeoutSeconds?: number = 10;

  @ApiProperty({ description: '检查间隔(秒)', minimum: 10, maximum: 3600, default: 30, example: 30 })
  @IsOptional()
  @IsInt({ message: '检查间隔必须是整数' })
  @Min(10, { message: '检查间隔最少为10秒' })
  @Max(3600, { message: '检查间隔最多为3600秒' })
  intervalSeconds?: number = 30;

  @ApiProperty({ description: '是否启用', default: true, example: true })
  @IsOptional()
  @IsBoolean({ message: '启用状态必须是布尔值' })
  enabled?: boolean = true;
}

/**
 * 更新健康检查DTO
 */
export class UpdateHealthCheckDto {
  @ApiProperty({ description: '服务名称', example: 'user-service', required: false })
  @IsOptional()
  @IsString({ message: '服务名称必须是字符串' })
  serviceName?: string;

  @ApiProperty({ description: '检查端点', example: 'http://user-service:3001/api/v1/health', required: false })
  @IsOptional()
  @IsUrl({}, { message: '检查端点必须是有效的URL' })
  endpoint?: string;

  @ApiProperty({ 
    description: 'HTTP方法', 
    enum: HttpMethod, 
    required: false,
    example: HttpMethod.GET 
  })
  @IsOptional()
  @IsEnum(HttpMethod, { message: 'HTTP方法必须是有效值' })
  method?: HttpMethod;

  @ApiProperty({ description: '超时时间(秒)', minimum: 1, maximum: 300, required: false, example: 10 })
  @IsOptional()
  @IsInt({ message: '超时时间必须是整数' })
  @Min(1, { message: '超时时间最少为1秒' })
  @Max(300, { message: '超时时间最多为300秒' })
  timeoutSeconds?: number;

  @ApiProperty({ description: '检查间隔(秒)', minimum: 10, maximum: 3600, required: false, example: 30 })
  @IsOptional()
  @IsInt({ message: '检查间隔必须是整数' })
  @Min(10, { message: '检查间隔最少为10秒' })
  @Max(3600, { message: '检查间隔最多为3600秒' })
  intervalSeconds?: number;

  @ApiProperty({ description: '是否启用', required: false, example: true })
  @IsOptional()
  @IsBoolean({ message: '启用状态必须是布尔值' })
  enabled?: boolean;
}

/**
 * 健康检查查询DTO
 */
export class HealthCheckQueryDto {
  @ApiProperty({ description: '服务名称', required: false, example: 'user-service' })
  @IsOptional()
  @IsString({ message: '服务名称必须是字符串' })
  serviceName?: string;

  @ApiProperty({ description: '是否启用', required: false, example: true })
  @IsOptional()
  @IsBoolean({ message: '启用状态必须是布尔值' })
  enabled?: boolean;
}
