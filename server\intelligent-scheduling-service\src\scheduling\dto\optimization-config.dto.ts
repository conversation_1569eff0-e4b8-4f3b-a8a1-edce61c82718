import {
  IsN<PERSON>ber,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsObject,
  Min,
  Max,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OptimizationAlgorithm, SchedulingObjective } from '../entities/scheduling-solution.entity';

/**
 * 优化配置DTO
 */
export class OptimizationConfigDto {
  @ApiPropertyOptional({ 
    description: '默认优化算法',
    enum: OptimizationAlgorithm,
    example: OptimizationAlgorithm.GENETIC_ALGORITHM
  })
  @IsOptional()
  @IsEnum(OptimizationAlgorithm)
  defaultAlgorithm?: OptimizationAlgorithm;

  @ApiPropertyOptional({ 
    description: '规划时间范围(小时)',
    minimum: 1,
    maximum: 8760,
    example: 168
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(8760)
  planningHorizon?: number;

  @ApiPropertyOptional({ 
    description: '重规划间隔(小时)',
    minimum: 1,
    maximum: 168,
    example: 4
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(168)
  replanningInterval?: number;

  @ApiPropertyOptional({ 
    description: '优化时间限制(秒)',
    minimum: 10,
    maximum: 3600,
    example: 300
  })
  @IsOptional()
  @IsNumber()
  @Min(10)
  @Max(3600)
  optimizationTimeLimit?: number;

  @ApiPropertyOptional({ 
    description: '遗传算法种群大小',
    minimum: 10,
    maximum: 1000,
    example: 100
  })
  @IsOptional()
  @IsNumber()
  @Min(10)
  @Max(1000)
  populationSize?: number;

  @ApiPropertyOptional({ 
    description: '最大迭代次数',
    minimum: 10,
    maximum: 10000,
    example: 1000
  })
  @IsOptional()
  @IsNumber()
  @Min(10)
  @Max(10000)
  maxIterations?: number;

  @ApiPropertyOptional({ 
    description: '收敛阈值',
    minimum: 0.0001,
    maximum: 0.1,
    example: 0.001
  })
  @IsOptional()
  @IsNumber()
  @Min(0.0001)
  @Max(0.1)
  convergenceThreshold?: number;

  @ApiPropertyOptional({ 
    description: '交叉概率',
    minimum: 0.1,
    maximum: 1.0,
    example: 0.8
  })
  @IsOptional()
  @IsNumber()
  @Min(0.1)
  @Max(1.0)
  crossoverRate?: number;

  @ApiPropertyOptional({ 
    description: '变异概率',
    minimum: 0.001,
    maximum: 0.5,
    example: 0.1
  })
  @IsOptional()
  @IsNumber()
  @Min(0.001)
  @Max(0.5)
  mutationRate?: number;

  @ApiPropertyOptional({ 
    description: '精英保留比例',
    minimum: 0.01,
    maximum: 0.5,
    example: 0.1
  })
  @IsOptional()
  @IsNumber()
  @Min(0.01)
  @Max(0.5)
  elitismRate?: number;

  @ApiPropertyOptional({ 
    description: '是否启用并行计算',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  enableParallelProcessing?: boolean;

  @ApiPropertyOptional({ 
    description: '并行线程数',
    minimum: 1,
    maximum: 16,
    example: 4
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(16)
  parallelThreads?: number;

  @ApiPropertyOptional({ 
    description: '是否启用缓存',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  enableCaching?: boolean;

  @ApiPropertyOptional({ 
    description: '目标权重配置',
    example: {
      minimize_makespan: 0.4,
      minimize_cost: 0.3,
      maximize_utilization: 0.3
    }
  })
  @IsOptional()
  @IsObject()
  objectiveWeights?: Record<SchedulingObjective, number>;

  @ApiPropertyOptional({ description: '扩展配置参数' })
  @IsOptional()
  @IsObject()
  metadata?: any;
}
