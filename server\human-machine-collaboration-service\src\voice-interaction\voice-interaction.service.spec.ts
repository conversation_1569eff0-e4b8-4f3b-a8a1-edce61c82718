import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
import { VoiceInteractionService } from './voice-interaction.service';
import { SpeechRecognitionService } from './speech-recognition.service';
import { TextToSpeechService } from './text-to-speech.service';
import { VoiceCommandProcessor } from './voice-command-processor.service';
import { VoiceCommand } from '../database/entities/voice-command.entity';

describe('VoiceInteractionService', () => {
  let service: VoiceInteractionService;
  let voiceCommandRepository: Repository<VoiceCommand>;
  let speechRecognitionService: SpeechRecognitionService;
  let textToSpeechService: TextToSpeechService;
  let voiceCommandProcessor: VoiceCommandProcessor;

  const mockVoiceCommandRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockSpeechRecognitionService = {
    recognize: jest.fn(),
    getSupportedLanguages: jest.fn(),
  };

  const mockTextToSpeechService = {
    synthesize: jest.fn(),
    getSupportedLanguages: jest.fn(),
  };

  const mockVoiceCommandProcessor = {
    processCommand: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn((key: string, defaultValue?: any) => {
      const config = {
        TTS_VOICE: 'zh-CN-XiaoxiaoNeural',
      };
      return config[key] || defaultValue;
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VoiceInteractionService,
        {
          provide: getRepositoryToken(VoiceCommand),
          useValue: mockVoiceCommandRepository,
        },
        {
          provide: SpeechRecognitionService,
          useValue: mockSpeechRecognitionService,
        },
        {
          provide: TextToSpeechService,
          useValue: mockTextToSpeechService,
        },
        {
          provide: VoiceCommandProcessor,
          useValue: mockVoiceCommandProcessor,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<VoiceInteractionService>(VoiceInteractionService);
    voiceCommandRepository = module.get<Repository<VoiceCommand>>(getRepositoryToken(VoiceCommand));
    speechRecognitionService = module.get<SpeechRecognitionService>(SpeechRecognitionService);
    textToSpeechService = module.get<TextToSpeechService>(TextToSpeechService);
    voiceCommandProcessor = module.get<VoiceCommandProcessor>(VoiceCommandProcessor);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('processVoiceInput', () => {
    it('should process voice input successfully', async () => {
      const audioData = Buffer.from('mock audio data');
      const userId = 'user123';
      const sessionId = 'session123';

      // Mock speech recognition
      const recognitionResult = {
        success: true,
        text: '下一步',
        confidence: 0.95,
        language: 'zh-CN',
        metadata: {
          duration: 2.5,
          sampleRate: 16000,
          channels: 1,
          format: 'wav',
        },
      };
      mockSpeechRecognitionService.recognize.mockResolvedValue(recognitionResult);

      // Mock command processing
      const commandResult = {
        success: true,
        type: 'navigation',
        command: 'next',
        parameters: {},
        response: '正在进入下一步...',
        suggestions: ['暂停', '重复说明', '跳过'],
      };
      mockVoiceCommandProcessor.processCommand.mockResolvedValue(commandResult);

      // Mock TTS
      const ttsResult = {
        success: true,
        audioData: Buffer.from('mock tts audio'),
      };
      mockTextToSpeechService.synthesize.mockResolvedValue(ttsResult);

      // Mock voice command save
      const mockVoiceCommand = {
        id: 'cmd123',
        userId,
        sessionId,
        recognizedText: '下一步',
        success: true,
      };
      mockVoiceCommandRepository.create.mockReturnValue(mockVoiceCommand);
      mockVoiceCommandRepository.save.mockResolvedValue(mockVoiceCommand);

      const result = await service.processVoiceInput(audioData, userId, sessionId);

      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('id', 'cmd123');
      expect(result).toHaveProperty('recognizedText', '下一步');
      expect(result).toHaveProperty('confidence', 0.95);
      expect(result).toHaveProperty('command', 'next');
      expect(result).toHaveProperty('response', '正在进入下一步...');
      expect(result).toHaveProperty('audioResponse');
      expect(result).toHaveProperty('processingTime');
      expect(result).toHaveProperty('suggestions');

      expect(mockSpeechRecognitionService.recognize).toHaveBeenCalledWith(audioData);
      expect(mockVoiceCommandProcessor.processCommand).toHaveBeenCalledWith('下一步', userId, sessionId);
      expect(mockTextToSpeechService.synthesize).toHaveBeenCalled();
      expect(mockVoiceCommandRepository.save).toHaveBeenCalled();
    });

    it('should handle speech recognition failure', async () => {
      const audioData = Buffer.from('mock audio data');
      const userId = 'user123';

      const recognitionResult = {
        success: false,
        error: '语音识别失败',
      };
      mockSpeechRecognitionService.recognize.mockResolvedValue(recognitionResult);

      const result = await service.processVoiceInput(audioData, userId);

      expect(result).toHaveProperty('success', false);
      expect(result).toHaveProperty('error', '语音识别失败');
      expect(result).toHaveProperty('details', '语音识别失败');
    });
  });

  describe('textToSpeech', () => {
    it('should convert text to speech', async () => {
      const text = '你好，欢迎使用语音助手';
      const options = {
        language: 'zh-CN',
        voice: 'zh-CN-XiaoxiaoNeural',
        speed: 1.0,
      };

      const ttsResult = {
        success: true,
        audioData: Buffer.from('mock audio'),
        duration: 3.5,
      };
      mockTextToSpeechService.synthesize.mockResolvedValue(ttsResult);

      const result = await service.textToSpeech(text, options);

      expect(result).toEqual(ttsResult);
      expect(mockTextToSpeechService.synthesize).toHaveBeenCalledWith(text, {
        language: 'zh-CN',
        voice: 'zh-CN-XiaoxiaoNeural',
        speed: 1.0,
        pitch: 1.0,
        volume: 1.0,
      });
    });
  });

  describe('speechToText', () => {
    it('should convert speech to text', async () => {
      const audioData = Buffer.from('mock audio');
      const options = {
        language: 'zh-CN',
        enablePunctuation: true,
      };

      const recognitionResult = {
        success: true,
        text: '这是一个测试',
        confidence: 0.92,
      };
      mockSpeechRecognitionService.recognize.mockResolvedValue(recognitionResult);

      const result = await service.speechToText(audioData, options);

      expect(result).toEqual(recognitionResult);
      expect(mockSpeechRecognitionService.recognize).toHaveBeenCalledWith(audioData, {
        language: 'zh-CN',
        enablePunctuation: true,
        enableWordTimeOffsets: false,
      });
    });
  });

  describe('getSupportedLanguages', () => {
    it('should return supported languages', async () => {
      const speechLanguages = [
        { code: 'zh-CN', name: '中文（简体）' },
        { code: 'en-US', name: 'English (US)' },
      ];
      const ttsLanguages = [
        { code: 'zh-CN', name: '中文（简体）' },
        { code: 'en-US', name: 'English (US)' },
      ];

      mockSpeechRecognitionService.getSupportedLanguages.mockResolvedValue(speechLanguages);
      mockTextToSpeechService.getSupportedLanguages.mockResolvedValue(ttsLanguages);

      const result = await service.getSupportedLanguages();

      expect(result).toHaveProperty('speechRecognition', speechLanguages);
      expect(result).toHaveProperty('textToSpeech', ttsLanguages);
      expect(result).toHaveProperty('common');
      expect(result.common).toHaveLength(2);
    });
  });

  describe('getVoiceCommandHistory', () => {
    it('should return voice command history', async () => {
      const userId = 'user123';
      const limit = 20;

      const mockHistory = [
        {
          id: 'cmd1',
          userId,
          recognizedText: '下一步',
          success: true,
          createdAt: new Date(),
        },
        {
          id: 'cmd2',
          userId,
          recognizedText: '暂停',
          success: true,
          createdAt: new Date(),
        },
      ];

      mockVoiceCommandRepository.find.mockResolvedValue(mockHistory);

      const result = await service.getVoiceCommandHistory(userId, limit);

      expect(result).toEqual(mockHistory);
      expect(mockVoiceCommandRepository.find).toHaveBeenCalledWith({
        where: { userId },
        order: { createdAt: 'DESC' },
        take: limit,
      });
    });
  });

  describe('getVoiceInteractionStats', () => {
    it('should return voice interaction statistics', async () => {
      const userId = 'user123';

      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(50),
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ successRate: 0.95, avgConfidence: 0.88, avgProcessingTime: 1200 }),
        getRawMany: jest.fn().mockResolvedValue([
          { type: 'navigation', count: 20 },
          { type: 'control', count: 15 },
          { type: 'query', count: 10 },
        ]),
      };

      mockVoiceCommandRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getVoiceInteractionStats(userId);

      expect(result).toHaveProperty('totalCommands', 50);
      expect(result).toHaveProperty('successRate', 0.95);
      expect(result).toHaveProperty('averageConfidence', 0.88);
      expect(result).toHaveProperty('averageProcessingTime', 1200);
      expect(result).toHaveProperty('commandTypeDistribution');
      expect(result.commandTypeDistribution).toHaveLength(3);
    });
  });
});
