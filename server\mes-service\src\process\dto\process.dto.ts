import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { IsString, IsNumber, IsEnum, IsOptional, IsDateString, IsArray, IsObject, IsBoolean, Min, Max, Length, IsUUID } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ProcessRouteStatus, OperationType } from '../entities/process-route.entity';

/**
 * 创建工艺路线DTO
 */
export class CreateProcessRouteDto {
  @ApiProperty({ description: '工艺路线编码', example: 'PR202406300001' })
  @IsString()
  @Length(1, 50)
  routeCode: string;

  @ApiProperty({ description: '工艺路线名称', example: '智能手机外壳加工工艺' })
  @IsString()
  @Length(1, 200)
  routeName: string;

  @ApiProperty({ description: '产品编码', example: 'PROD001' })
  @IsString()
  @Length(1, 100)
  productCode: string;

  @ApiProperty({ description: '产品名称', example: '智能手机外壳' })
  @IsString()
  @Length(1, 200)
  productName: string;

  @ApiPropertyOptional({ description: '版本号', example: 1, minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  version?: number;

  @ApiPropertyOptional({ description: '状态', enum: ProcessRouteStatus, example: ProcessRouteStatus.DRAFT })
  @IsOptional()
  @IsEnum(ProcessRouteStatus)
  status?: ProcessRouteStatus;

  @ApiPropertyOptional({ description: '工艺路线描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '标准工时(分钟)', example: 120, minimum: 1 })
  @IsNumber()
  @Min(1)
  standardTime: number;

  @ApiPropertyOptional({ description: '准备时间(分钟)', example: 30, minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  setupTime?: number;

  @ApiProperty({ description: '生效日期', example: '2024-07-01' })
  @IsDateString()
  effectiveDate: string;

  @ApiPropertyOptional({ description: '失效日期', example: '2024-12-31' })
  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @ApiProperty({ description: '创建人', example: 'admin' })
  @IsString()
  @Length(1, 100)
  createdBy: string;
}

/**
 * 更新工艺路线DTO
 */
export class UpdateProcessRouteDto extends PartialType(CreateProcessRouteDto) {
  @ApiPropertyOptional({ description: '更新人' })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  updatedBy?: string;
}

/**
 * 工艺路线查询DTO
 */
export class ProcessRouteQueryDto {
  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: '工艺路线编码' })
  @IsOptional()
  @IsString()
  routeCode?: string;

  @ApiPropertyOptional({ description: '工艺路线名称' })
  @IsOptional()
  @IsString()
  routeName?: string;

  @ApiPropertyOptional({ description: '产品编码' })
  @IsOptional()
  @IsString()
  productCode?: string;

  @ApiPropertyOptional({ description: '状态', enum: ProcessRouteStatus })
  @IsOptional()
  @IsEnum(ProcessRouteStatus)
  status?: ProcessRouteStatus;

  @ApiPropertyOptional({ description: '排序字段', example: 'createdAt' })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', example: 'DESC', enum: ['ASC', 'DESC'] })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * 创建工序DTO
 */
export class CreateOperationDto {
  @ApiProperty({ description: '工序编码', example: 'OP001' })
  @IsString()
  @Length(1, 50)
  operationCode: string;

  @ApiProperty({ description: '工序名称', example: '铣削加工' })
  @IsString()
  @Length(1, 200)
  operationName: string;

  @ApiProperty({ description: '工序序号', example: 10, minimum: 1 })
  @IsNumber()
  @Min(1)
  sequenceNumber: number;

  @ApiProperty({ description: '工序类型', enum: OperationType, example: OperationType.MACHINING })
  @IsEnum(OperationType)
  operationType: OperationType;

  @ApiPropertyOptional({ description: '工作中心ID' })
  @IsOptional()
  @IsUUID()
  workCenterId?: string;

  @ApiPropertyOptional({ description: '工作中心名称', example: '数控加工中心1' })
  @IsOptional()
  @IsString()
  @Length(1, 200)
  workCenterName?: string;

  @ApiPropertyOptional({ description: '设备ID' })
  @IsOptional()
  @IsUUID()
  equipmentId?: string;

  @ApiPropertyOptional({ description: '设备名称', example: 'CNC-001' })
  @IsOptional()
  @IsString()
  @Length(1, 200)
  equipmentName?: string;

  @ApiProperty({ description: '标准工时(分钟)', example: 60, minimum: 1 })
  @IsNumber()
  @Min(1)
  standardTime: number;

  @ApiPropertyOptional({ description: '准备时间(分钟)', example: 15, minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  setupTime?: number;

  @ApiPropertyOptional({ description: '等待时间(分钟)', example: 5, minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  waitTime?: number;

  @ApiPropertyOptional({ description: '移动时间(分钟)', example: 3, minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  moveTime?: number;

  @ApiPropertyOptional({ description: '工序描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '工艺参数', example: { speed: 1200, feed: 0.2 } })
  @IsOptional()
  @IsObject()
  processParameters?: any;

  @ApiPropertyOptional({ description: '质量检查点', example: [{ name: '尺寸检查', tolerance: 0.01 }] })
  @IsOptional()
  @IsArray()
  qualityCheckpoints?: any[];

  @ApiPropertyOptional({ description: '所需技能', example: ['数控操作', '质量检验'] })
  @IsOptional()
  @IsArray()
  requiredSkills?: string[];

  @ApiPropertyOptional({ description: '工具清单', example: [{ name: '铣刀', specification: 'Φ10' }] })
  @IsOptional()
  @IsArray()
  toolList?: any[];

  @ApiPropertyOptional({ description: '物料清单', example: [{ code: 'MAT001', quantity: 1 }] })
  @IsOptional()
  @IsArray()
  materialList?: any[];

  @ApiPropertyOptional({ description: '是否关键工序', example: false })
  @IsOptional()
  @IsBoolean()
  isCritical?: boolean;

  @ApiPropertyOptional({ description: '是否可并行', example: false })
  @IsOptional()
  @IsBoolean()
  isParallel?: boolean;

  @ApiPropertyOptional({ description: '前置工序', example: ['OP001', 'OP002'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  predecessorOperations?: string[];

  @ApiPropertyOptional({ description: '后续工序', example: ['OP004', 'OP005'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  successorOperations?: string[];
}

/**
 * 更新工序DTO
 */
export class UpdateOperationDto extends PartialType(CreateOperationDto) {}

/**
 * 复制工艺路线DTO
 */
export class CopyProcessRouteDto {
  @ApiProperty({ description: '新工艺路线编码', example: 'PR202406300002' })
  @IsString()
  @Length(1, 50)
  newRouteCode: string;

  @ApiProperty({ description: '新工艺路线名称', example: '智能手机外壳加工工艺V2' })
  @IsString()
  @Length(1, 200)
  newRouteName: string;
}

/**
 * 工序查询DTO
 */
export class OperationQueryDto {
  @ApiPropertyOptional({ description: '工艺路线ID' })
  @IsOptional()
  @IsUUID()
  processRouteId?: string;

  @ApiPropertyOptional({ description: '工序类型', enum: OperationType })
  @IsOptional()
  @IsEnum(OperationType)
  operationType?: OperationType;

  @ApiPropertyOptional({ description: '工作中心ID' })
  @IsOptional()
  @IsUUID()
  workCenterId?: string;

  @ApiPropertyOptional({ description: '是否关键工序' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  isCritical?: boolean;
}
