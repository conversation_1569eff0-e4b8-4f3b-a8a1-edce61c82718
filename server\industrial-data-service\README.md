# 工业数据采集服务 (Industrial Data Service)

## 项目简介

工业数据采集服务是一个专为工业4.0环境设计的微服务，提供完整的工业设备数据采集、存储、分析和告警功能。支持多种工业通信协议，包括Modbus、OPC UA、MQTT等。

## 主要功能

### 🔧 设备管理
- 设备注册和配置管理
- 设备状态监控
- 设备连接测试
- 批量设备操作

### 📡 协议支持
- **Modbus TCP/RTU**: 工业标准通信协议
- **OPC UA**: 现代工业通信标准
- **MQTT**: 物联网消息传输协议
- **可扩展**: 支持自定义协议驱动

### 📊 数据采集
- 实时数据采集
- 定时任务调度
- 数据质量管理
- 采集状态监控

### 💾 数据存储
- 时序数据存储
- 数据压缩和归档
- 历史数据查询
- 数据统计分析

### 📈 数据分析
- 实时数据分析
- 统计分析
- 趋势分析
- 设备效率分析
- 能耗分析

### 🚨 告警系统
- 灵活的告警规则配置
- 多种告警条件支持
- 多渠道通知（邮件、短信、Webhook）
- 告警确认和解决

### 🔄 实时通信
- WebSocket实时数据推送
- 设备状态变化通知
- 告警实时推送
- 系统消息广播

## 技术栈

- **框架**: NestJS + TypeScript
- **数据库**: MySQL + Redis
- **时序数据库**: InfluxDB (可选)
- **实时通信**: Socket.IO
- **容器化**: Docker + Docker Compose
- **监控**: Grafana (可选)

## 快速开始

### 环境要求

- Node.js >= 18.0.0
- MySQL >= 8.0
- Redis >= 6.0
- Docker (可选)

### 安装依赖

```bash
npm install
```

### 环境配置

复制环境变量示例文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库连接等信息。

### 数据库初始化

确保MySQL和Redis服务已启动，然后运行：

```bash
# 数据库迁移会自动创建表结构
npm run start:dev
```

### 启动服务

```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start
```

服务将在 `http://localhost:3007` 启动。

## Docker 部署

### 使用 Docker Compose

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 单独构建镜像

```bash
# 构建镜像
docker build -t industrial-data-service .

# 运行容器
docker run -d \
  --name industrial-data-service \
  -p 3007:3007 \
  -e DB_HOST=your-mysql-host \
  -e REDIS_HOST=your-redis-host \
  industrial-data-service
```

## API 文档

### 设备管理 API

```bash
# 创建设备
POST /api/v1/devices

# 获取设备列表
GET /api/v1/devices

# 获取设备详情
GET /api/v1/devices/:id

# 更新设备
PATCH /api/v1/devices/:id

# 删除设备
DELETE /api/v1/devices/:id

# 测试设备连接
POST /api/v1/devices/:id/test-connection
```

### 协议管理 API

```bash
# 获取支持的协议
GET /api/v1/protocols/supported

# 测试协议连接
POST /api/v1/protocols/test-connection

# 连接设备
POST /api/v1/protocols/connect

# 读取设备数据
POST /api/v1/protocols/:deviceId/read
```

### 数据存储 API

```bash
# 存储数据
POST /api/v1/storage/store

# 查询数据
GET /api/v1/storage/query

# 获取最新数据
GET /api/v1/storage/latest/:deviceId
```

### 告警管理 API

```bash
# 创建告警规则
POST /api/v1/alerts/rules

# 获取告警规则
GET /api/v1/alerts/rules

# 获取活跃告警
GET /api/v1/alerts/active

# 确认告警
POST /api/v1/alerts/instances/:id/acknowledge
```

### 数据分析 API

```bash
# 统计分析
POST /api/v1/analytics/statistical

# 趋势分析
POST /api/v1/analytics/trend

# 实时分析
GET /api/v1/analytics/realtime/:deviceId/:tagName
```

## WebSocket 连接

连接到实时数据推送：

```javascript
const socket = io('http://localhost:3007/industrial-data');

// 订阅设备数据
socket.emit('subscribe-realtime-data', {
  deviceIds: ['device-1', 'device-2'],
  tagNames: ['temperature', 'pressure']
});

// 接收实时数据
socket.on('realtime-data', (data) => {
  console.log('实时数据:', data);
});

// 订阅告警
socket.emit('subscribe-alerts', {});

// 接收告警
socket.on('new-alert', (alert) => {
  console.log('新告警:', alert);
});
```

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| NODE_ENV | 运行环境 | development |
| PORT | 服务端口 | 3007 |
| DB_HOST | MySQL主机 | localhost |
| DB_PORT | MySQL端口 | 3306 |
| DB_USERNAME | MySQL用户名 | root |
| DB_PASSWORD | MySQL密码 | - |
| DB_DATABASE | 数据库名 | industrial_data |
| REDIS_HOST | Redis主机 | localhost |
| REDIS_PORT | Redis端口 | 6379 |

### 协议配置

支持的工业协议配置：

```json
{
  "modbus": {
    "enabled": true,
    "timeout": 5000,
    "retries": 3
  },
  "opcua": {
    "enabled": true,
    "securityMode": "None",
    "securityPolicy": "None"
  },
  "mqtt": {
    "enabled": true,
    "keepalive": 60,
    "qos": 1
  }
}
```

## 监控和日志

### 健康检查

```bash
# 检查服务状态
GET /api/v1/health

# 检查数据库连接
GET /api/v1/health/database

# 检查Redis连接
GET /api/v1/health/redis
```

### 日志级别

- `error`: 错误信息
- `warn`: 警告信息
- `info`: 一般信息
- `debug`: 调试信息

### Grafana 监控

如果启用了Grafana，可以通过 `http://localhost:3000` 访问监控面板。

默认登录信息：
- 用户名: admin
- 密码: industrial123

## 开发指南

### 项目结构

```
src/
├── main.ts                 # 应用入口
├── app.module.ts           # 根模块
├── device-management/      # 设备管理模块
├── protocol/              # 协议管理模块
├── data-collection/       # 数据采集模块
├── storage/               # 数据存储模块
├── analytics/             # 数据分析模块
├── alert/                 # 告警模块
└── websocket/             # WebSocket模块
```

### 添加新协议

1. 在 `src/protocol/drivers/` 目录下创建新的协议驱动
2. 实现 `ProtocolDriver` 接口
3. 在 `ProtocolService` 中注册新协议

### 自定义告警规则

可以通过扩展 `AlertCondition` 枚举来添加新的告警条件类型。

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库连接配置
   - 确认数据库用户权限

2. **Redis连接失败**
   - 检查Redis服务是否启动
   - 验证Redis连接配置

3. **设备连接超时**
   - 检查设备网络连接
   - 验证协议配置参数
   - 检查防火墙设置

### 日志查看

```bash
# 查看应用日志
tail -f logs/application.log

# 查看错误日志
tail -f logs/error.log

# Docker环境查看日志
docker-compose logs -f industrial-data-service
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。
