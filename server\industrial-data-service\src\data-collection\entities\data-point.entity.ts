import { <PERSON>tity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { Device } from '../../device-management/entities/device.entity';

/**
 * 工业数据点实体
 */
@Entity('data_points')
@Index(['deviceId', 'tagId', 'timestamp'])
@Index(['timestamp'])
@Index(['deviceId'])
export class DeviceDataPoint {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'device_id', type: 'uuid' })
  deviceId: string;

  @Column({ name: 'tag_id', length: 100 })
  tagId: string;

  @Column({ name: 'tag_name', length: 200, nullable: true })
  tagName: string;

  @Column({ type: 'timestamp', precision: 3 })
  timestamp: Date;

  @Column({ type: 'json' })
  value: any;

  @Column({ name: 'data_type', length: 50 })
  dataType: string;

  @Column({ length: 50, default: 'good' })
  quality: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @Column({ length: 50, nullable: true })
  unit: string;

  @Column({ name: 'source_timestamp', type: 'timestamp', precision: 3, nullable: true })
  sourceTimestamp: Date;

  @Column({ name: 'collection_method', length: 50, default: 'polling' })
  collectionMethod: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => Device)
  @JoinColumn({ name: 'device_id' })
  device: Device;
}

// 导出别名以保持向后兼容
export { DeviceDataPoint as DataPoint };
