/**
 * 增强的WebSocket网关
 * 专为支持100+并发用户优化的WebSocket通信网关
 */
import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Injectable, Logger, UseGuards } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EnhancedConnectionPool } from '../connection/EnhancedConnectionPool';

// 消息类型枚举
enum MessageType {
  // 用户操作
  USER_JOIN = 'user_join',
  USER_LEAVE = 'user_leave',
  USER_CURSOR = 'user_cursor',
  USER_SELECTION = 'user_selection',
  
  // 对象操作
  OBJECT_CREATE = 'object_create',
  OBJECT_UPDATE = 'object_update',
  OBJECT_DELETE = 'object_delete',
  OBJECT_TRANSFORM = 'object_transform',
  
  // 场景操作
  SCENE_LOAD = 'scene_load',
  SCENE_SAVE = 'scene_save',
  SCENE_SYNC = 'scene_sync',
  
  // 协作操作
  COLLABORATION_LOCK = 'collaboration_lock',
  COLLABORATION_UNLOCK = 'collaboration_unlock',
  COLLABORATION_EDIT = 'collaboration_edit',
  
  // 系统消息
  HEARTBEAT = 'heartbeat',
  ERROR = 'error',
  BATCH_UPDATE = 'batch_update',
  PERFORMANCE_STATS = 'performance_stats',
}

// 用户会话信息
interface UserSession {
  userId: string;
  socketId: string;
  roomId: string;
  joinedAt: Date;
  lastActivity: Date;
  messageCount: number;
  bytesTransferred: number;
  latency: number;
  userAgent?: string;
  ipAddress?: string;
}

// 房间信息
interface RoomInfo {
  id: string;
  name: string;
  users: Map<string, UserSession>;
  createdAt: Date;
  lastActivity: Date;
  messageCount: number;
  maxUsers: number;
  isPrivate: boolean;
}

// 消息统计
interface MessageStats {
  totalMessages: number;
  messagesPerSecond: number;
  averageLatency: number;
  errorRate: number;
  activeConnections: number;
  totalRooms: number;
  totalUsers: number;
}

/**
 * 增强的WebSocket网关类
 */
@WebSocketGateway({
  cors: {
    origin: '*',
    credentials: true,
  },
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000,
  maxHttpBufferSize: 1e6, // 1MB
  allowEIO3: true,
})
@Injectable()
export class EnhancedWebSocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(EnhancedWebSocketGateway.name);
  
  // 用户会话管理
  private userSessions: Map<string, UserSession> = new Map();
  private socketToUser: Map<string, string> = new Map();
  
  // 房间管理
  private rooms: Map<string, RoomInfo> = new Map();
  
  // 消息统计
  private messageStats: MessageStats = {
    totalMessages: 0,
    messagesPerSecond: 0,
    averageLatency: 0,
    errorRate: 0,
    activeConnections: 0,
    totalRooms: 0,
    totalUsers: 0,
  };
  
  // 批处理队列
  private batchQueue: Map<string, any[]> = new Map();
  private batchTimer: Map<string, NodeJS.Timeout> = new Map();
  
  // 配置
  private readonly maxUsersPerRoom: number;
  private readonly batchTimeout: number;
  private readonly maxBatchSize: number;
  private readonly enableCompression: boolean;
  private readonly enableBatching: boolean;
  
  // 定时器
  private statsTimer?: NodeJS.Timeout;
  private cleanupTimer?: NodeJS.Timeout;
  
  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly connectionPool: EnhancedConnectionPool,
  ) {
    // 读取配置
    this.maxUsersPerRoom = this.configService.get<number>('MAX_USERS_PER_ROOM', 100);
    this.batchTimeout = this.configService.get<number>('BATCH_TIMEOUT', 50);
    this.maxBatchSize = this.configService.get<number>('MAX_BATCH_SIZE', 20);
    this.enableCompression = this.configService.get<boolean>('ENABLE_COMPRESSION', true);
    this.enableBatching = this.configService.get<boolean>('ENABLE_BATCHING', true);
  }

  /**
   * 网关初始化
   */
  afterInit(server: Server): void {
    this.logger.log('增强WebSocket网关初始化完成');
    
    // 配置Socket.IO服务器
    this.configureServer(server);
    
    // 启动统计监控
    this.startStatsMonitoring();
    
    // 启动清理任务
    this.startCleanupTasks();
  }

  /**
   * 配置Socket.IO服务器
   */
  private configureServer(server: Server): void {
    // 启用压缩
    if (this.enableCompression) {
      // 注意：Socket.IO v4+ 的压缩配置方式可能不同
      // 这里使用类型断言来避免编译错误
      (server.engine as any).compression = true;
      (server.engine as any).perMessageDeflate = {
        threshold: 1024,
        concurrencyLimit: 10,
        memLevel: 7,
      };
    }
    
    // 设置适配器（如果使用Redis）
    const redisAdapter = this.configService.get<boolean>('USE_REDIS_ADAPTER', false);
    if (redisAdapter) {
      // 这里可以配置Redis适配器
      // const { createAdapter } = require('@socket.io/redis-adapter');
      // const pubClient = new Redis(redisConfig);
      // const subClient = pubClient.duplicate();
      // server.adapter(createAdapter(pubClient, subClient));
    }
  }

  /**
   * 处理客户端连接
   */
  async handleConnection(client: Socket): Promise<void> {
    try {
      const userId = this.extractUserId(client);
      const roomId = this.extractRoomId(client);
      
      if (!userId || !roomId) {
        this.logger.warn(`连接被拒绝: 缺少用户ID或房间ID`);
        client.disconnect(true);
        return;
      }
      
      // 检查房间用户数限制
      const room = this.rooms.get(roomId);
      if (room && room.users.size >= this.maxUsersPerRoom) {
        this.logger.warn(`房间 ${roomId} 已满，拒绝用户 ${userId} 连接`);
        client.emit('error', { message: '房间已满' });
        client.disconnect(true);
        return;
      }
      
      // 创建用户会话
      const session: UserSession = {
        userId,
        socketId: client.id,
        roomId,
        joinedAt: new Date(),
        lastActivity: new Date(),
        messageCount: 0,
        bytesTransferred: 0,
        latency: 0,
        userAgent: client.handshake.headers['user-agent'],
        ipAddress: client.handshake.address,
      };
      
      // 存储会话信息
      this.userSessions.set(userId, session);
      this.socketToUser.set(client.id, userId);
      
      // 加入房间
      await this.joinRoom(client, roomId, session);
      
      // 设置客户端事件监听器
      this.setupClientListeners(client);
      
      // 更新统计信息
      this.updateStats();
      
      this.logger.log(`用户 ${userId} 连接到房间 ${roomId}`);
      
    } catch (error) {
      this.logger.error('处理连接失败:', error);
      client.disconnect(true);
    }
  }

  /**
   * 处理客户端断开连接
   */
  async handleDisconnect(client: Socket): Promise<void> {
    try {
      const userId = this.socketToUser.get(client.id);
      
      if (userId) {
        const session = this.userSessions.get(userId);
        
        if (session) {
          // 离开房间
          await this.leaveRoom(client, session.roomId, session);
          
          // 清理会话信息
          this.userSessions.delete(userId);
          this.socketToUser.delete(client.id);
          
          // 更新统计信息
          this.updateStats();
          
          this.logger.log(`用户 ${userId} 断开连接`);
        }
      }
      
    } catch (error) {
      this.logger.error('处理断开连接失败:', error);
    }
  }

  /**
   * 加入房间
   */
  private async joinRoom(client: Socket, roomId: string, session: UserSession): Promise<void> {
    // 获取或创建房间
    let room = this.rooms.get(roomId);
    if (!room) {
      room = {
        id: roomId,
        name: `Room ${roomId}`,
        users: new Map(),
        createdAt: new Date(),
        lastActivity: new Date(),
        messageCount: 0,
        maxUsers: this.maxUsersPerRoom,
        isPrivate: false,
      };
      this.rooms.set(roomId, room);
    }
    
    // 添加用户到房间
    room.users.set(session.userId, session);
    room.lastActivity = new Date();
    
    // Socket.IO房间操作
    await client.join(roomId);
    
    // 通知房间内其他用户
    client.to(roomId).emit(MessageType.USER_JOIN, {
      userId: session.userId,
      joinedAt: session.joinedAt,
      userCount: room.users.size,
    });
    
    // 发送房间信息给新用户
    client.emit('room_info', {
      roomId,
      userCount: room.users.size,
      users: Array.from(room.users.keys()),
    });
  }

  /**
   * 离开房间
   */
  private async leaveRoom(client: Socket, roomId: string, session: UserSession): Promise<void> {
    const room = this.rooms.get(roomId);
    if (!room) return;
    
    // 从房间移除用户
    room.users.delete(session.userId);
    room.lastActivity = new Date();
    
    // Socket.IO房间操作
    await client.leave(roomId);
    
    // 通知房间内其他用户
    client.to(roomId).emit(MessageType.USER_LEAVE, {
      userId: session.userId,
      userCount: room.users.size,
    });
    
    // 如果房间为空，删除房间
    if (room.users.size === 0) {
      this.rooms.delete(roomId);
    }
  }

  /**
   * 设置客户端事件监听器
   */
  private setupClientListeners(client: Socket): void {
    // 心跳处理
    client.on('heartbeat', (data) => {
      const userId = this.socketToUser.get(client.id);
      if (userId) {
        const session = this.userSessions.get(userId);
        if (session) {
          session.lastActivity = new Date();
          session.latency = Date.now() - data.timestamp;
        }
      }
      client.emit('heartbeat_response', { timestamp: Date.now() });
    });
    
    // 错误处理
    client.on('error', (error) => {
      this.logger.error(`客户端 ${client.id} 错误:`, error);
    });
  }

  /**
   * 处理对象更新消息
   */
  @SubscribeMessage(MessageType.OBJECT_UPDATE)
  async handleObjectUpdate(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: any,
  ): Promise<void> {
    const userId = this.socketToUser.get(client.id);
    if (!userId) return;
    
    const session = this.userSessions.get(userId);
    if (!session) return;
    
    // 更新会话统计
    session.messageCount++;
    session.lastActivity = new Date();
    
    // 验证数据
    if (!this.validateObjectUpdate(data)) {
      client.emit('error', { message: '无效的对象更新数据' });
      return;
    }
    
    // 处理批处理
    if (this.enableBatching) {
      this.addToBatch(session.roomId, MessageType.OBJECT_UPDATE, data, userId);
    } else {
      // 直接广播
      this.broadcastToRoom(session.roomId, MessageType.OBJECT_UPDATE, data, userId);
    }
    
    // 发出事件
    this.eventEmitter.emit('object.updated', {
      userId,
      roomId: session.roomId,
      objectId: data.objectId,
      changes: data.changes,
    });
  }

  /**
   * 处理用户光标移动
   */
  @SubscribeMessage(MessageType.USER_CURSOR)
  async handleUserCursor(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: any,
  ): Promise<void> {
    const userId = this.socketToUser.get(client.id);
    if (!userId) return;
    
    const session = this.userSessions.get(userId);
    if (!session) return;
    
    session.lastActivity = new Date();
    
    // 光标消息使用批处理
    if (this.enableBatching) {
      this.addToBatch(session.roomId, MessageType.USER_CURSOR, data, userId);
    } else {
      this.broadcastToRoom(session.roomId, MessageType.USER_CURSOR, data, userId);
    }
  }

  /**
   * 添加到批处理队列
   */
  private addToBatch(roomId: string, type: MessageType, data: any, userId: string): void {
    const batchKey = `${roomId}:${type}`;
    
    if (!this.batchQueue.has(batchKey)) {
      this.batchQueue.set(batchKey, []);
    }
    
    const batch = this.batchQueue.get(batchKey)!;
    batch.push({ type, data, userId, timestamp: Date.now() });
    
    // 检查是否需要立即发送
    if (batch.length >= this.maxBatchSize) {
      this.flushBatch(roomId, type);
    } else if (!this.batchTimer.has(batchKey)) {
      // 设置批处理定时器
      const timer = setTimeout(() => {
        this.flushBatch(roomId, type);
      }, this.batchTimeout);
      
      this.batchTimer.set(batchKey, timer);
    }
  }

  /**
   * 刷新批处理队列
   */
  private flushBatch(roomId: string, type: MessageType): void {
    const batchKey = `${roomId}:${type}`;
    const batch = this.batchQueue.get(batchKey);
    
    if (!batch || batch.length === 0) return;
    
    // 清除定时器
    const timer = this.batchTimer.get(batchKey);
    if (timer) {
      clearTimeout(timer);
      this.batchTimer.delete(batchKey);
    }
    
    // 发送批处理消息
    this.server.to(roomId).emit(MessageType.BATCH_UPDATE, {
      type,
      messages: batch,
      count: batch.length,
    });
    
    // 清空批处理队列
    this.batchQueue.set(batchKey, []);
    
    // 更新统计
    this.messageStats.totalMessages += batch.length;
  }

  /**
   * 广播消息到房间
   */
  private broadcastToRoom(roomId: string, type: MessageType, data: any, excludeUserId?: string): void {
    const message = {
      type,
      data,
      timestamp: Date.now(),
      userId: excludeUserId,
    };
    
    if (excludeUserId) {
      const session = this.userSessions.get(excludeUserId);
      if (session) {
        this.server.to(roomId).except(session.socketId).emit(type, message);
      }
    } else {
      this.server.to(roomId).emit(type, message);
    }
    
    this.messageStats.totalMessages++;
  }

  /**
   * 验证对象更新数据
   */
  private validateObjectUpdate(data: any): boolean {
    return data && 
           typeof data.objectId === 'string' && 
           data.changes && 
           typeof data.changes === 'object';
  }

  /**
   * 提取用户ID
   */
  private extractUserId(client: Socket): string | null {
    return client.handshake.auth?.userId || 
           client.handshake.query?.userId as string || 
           null;
  }

  /**
   * 提取房间ID
   */
  private extractRoomId(client: Socket): string | null {
    return client.handshake.auth?.roomId || 
           client.handshake.query?.roomId as string || 
           null;
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    this.messageStats.activeConnections = this.userSessions.size;
    this.messageStats.totalRooms = this.rooms.size;
    this.messageStats.totalUsers = this.userSessions.size;
    
    // 计算平均延迟
    let totalLatency = 0;
    let latencyCount = 0;
    
    for (const session of this.userSessions.values()) {
      if (session.latency > 0) {
        totalLatency += session.latency;
        latencyCount++;
      }
    }
    
    this.messageStats.averageLatency = latencyCount > 0 ? totalLatency / latencyCount : 0;
  }

  /**
   * 启动统计监控
   */
  private startStatsMonitoring(): void {
    this.statsTimer = setInterval(() => {
      this.updateStats();
      
      // 广播性能统计
      this.server.emit(MessageType.PERFORMANCE_STATS, this.messageStats);
      
      this.logger.debug('WebSocket统计:', this.messageStats);
    }, 30000); // 每30秒更新一次
  }

  /**
   * 启动清理任务
   */
  private startCleanupTasks(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupInactiveSessions();
      this.cleanupEmptyRooms();
    }, 60000); // 每分钟清理一次
  }

  /**
   * 清理不活跃的会话
   */
  private cleanupInactiveSessions(): void {
    const now = Date.now();
    const inactiveThreshold = 5 * 60 * 1000; // 5分钟
    
    for (const [userId, session] of this.userSessions) {
      if (now - session.lastActivity.getTime() > inactiveThreshold) {
        const client = this.server.sockets.sockets.get(session.socketId);
        if (client) {
          client.disconnect(true);
        }
      }
    }
  }

  /**
   * 清理空房间
   */
  private cleanupEmptyRooms(): void {
    for (const [roomId, room] of this.rooms) {
      if (room.users.size === 0) {
        this.rooms.delete(roomId);
      }
    }
  }

  /**
   * 获取统计信息
   */
  public getStats(): MessageStats {
    this.updateStats();
    return { ...this.messageStats };
  }

  /**
   * 获取房间信息
   */
  public getRoomInfo(roomId: string): RoomInfo | undefined {
    return this.rooms.get(roomId);
  }

  /**
   * 获取所有房间
   */
  public getAllRooms(): RoomInfo[] {
    return Array.from(this.rooms.values());
  }
}
