/**
 * 日志拦截器
 * 记录请求和响应日志
 */
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const now = Date.now();
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const { method, url, ip, headers } = request;

    // 记录请求日志
    this.logger.log(
      `请求开始: ${method} ${url} - IP: ${ip} - User-Agent: ${headers['user-agent']}`,
    );

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - now;
        const { statusCode } = response;
        
        this.logger.log(
          `请求完成: ${method} ${url} - ${statusCode} - ${duration}ms`,
        );
      }),
    );
  }
}
