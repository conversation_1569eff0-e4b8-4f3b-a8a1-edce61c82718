#!/bin/bash

# 学习记录跟踪服务设置验证脚本

echo "🔍 验证学习记录跟踪服务设置..."

# 检查Node.js版本
echo "📦 检查Node.js版本..."
NODE_VERSION=$(node -v 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ Node.js版本: $NODE_VERSION"
else
    echo "❌ Node.js未安装"
    exit 1
fi

# 检查npm版本
echo "📦 检查npm版本..."
NPM_VERSION=$(npm -v 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ npm版本: $NPM_VERSION"
else
    echo "❌ npm未安装"
    exit 1
fi

# 检查package.json
echo "📄 检查package.json..."
if [ -f "package.json" ]; then
    echo "✅ package.json存在"
else
    echo "❌ package.json不存在"
    exit 1
fi

# 检查环境配置文件
echo "⚙️ 检查环境配置..."
if [ -f ".env" ]; then
    echo "✅ .env文件存在"
elif [ -f ".env.example" ]; then
    echo "⚠️ 只有.env.example文件，请复制为.env并配置"
    echo "   运行: cp .env.example .env"
else
    echo "❌ 环境配置文件不存在"
    exit 1
fi

# 检查核心文件
echo "📁 检查核心文件..."
CORE_FILES=("src/main.ts" "src/app.module.ts" "src/app.controller.ts" "src/app.service.ts")
for file in "${CORE_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 不存在"
        exit 1
    fi
done

# 检查依赖是否安装
echo "📦 检查依赖安装..."
if [ -d "node_modules" ]; then
    echo "✅ node_modules存在"
else
    echo "⚠️ node_modules不存在，需要运行: npm install"
fi

# 检查TypeScript配置
echo "🔧 检查TypeScript配置..."
if [ -f "tsconfig.json" ]; then
    echo "✅ tsconfig.json存在"
else
    echo "❌ tsconfig.json不存在"
    exit 1
fi

# 检查NestJS配置
echo "🏗️ 检查NestJS配置..."
if [ -f "nest-cli.json" ]; then
    echo "✅ nest-cli.json存在"
else
    echo "❌ nest-cli.json不存在"
    exit 1
fi

# 检查健康检查模块
echo "🏥 检查健康检查模块..."
if [ -d "src/health" ]; then
    echo "✅ 健康检查模块存在"
else
    echo "❌ 健康检查模块不存在"
    exit 1
fi

# 检查测试文件
echo "🧪 检查测试文件..."
if [ -d "test" ] && [ -f "src/app.controller.spec.ts" ]; then
    echo "✅ 测试文件存在"
else
    echo "⚠️ 测试文件不完整"
fi

# 检查部署配置
echo "🚀 检查部署配置..."
if [ -f "Dockerfile" ] && [ -f "docker-compose.yml" ]; then
    echo "✅ Docker配置存在"
else
    echo "⚠️ Docker配置不完整"
fi

if [ -d "k8s" ]; then
    echo "✅ Kubernetes配置存在"
else
    echo "⚠️ Kubernetes配置不存在"
fi

echo ""
echo "🎉 设置验证完成！"
echo ""
echo "📋 下一步操作："
echo "1. 如果node_modules不存在，运行: npm install"
echo "2. 如果.env不存在，运行: cp .env.example .env 并配置"
echo "3. 配置数据库和Redis连接"
echo "4. 运行迁移: npm run migration:run"
echo "5. 执行种子数据: npm run seed:run"
echo "6. 启动开发服务器: npm run start:dev"
echo ""
echo "📖 更多信息请查看 README.md"
