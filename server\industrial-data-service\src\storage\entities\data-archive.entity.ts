import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

export enum ArchiveStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export enum CompressionType {
  NONE = 'none',
  GZIP = 'gzip',
  LZ4 = 'lz4',
  SNAPPY = 'snappy'
}

@Entity('data_archives')
@Index(['status'])
@Index(['archiveDate'])
export class DataArchive {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 200 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'date' })
  archiveDate: Date;

  @Column({ type: 'timestamp' })
  startTime: Date;

  @Column({ type: 'timestamp' })
  endTime: Date;

  @Column({
    type: 'enum',
    enum: ArchiveStatus,
    default: ArchiveStatus.PENDING
  })
  status: ArchiveStatus;

  @Column({
    type: 'enum',
    enum: CompressionType,
    default: CompressionType.GZIP
  })
  compressionType: CompressionType;

  @Column({ type: 'bigint', default: 0 })
  originalSize: number;

  @Column({ type: 'bigint', default: 0 })
  compressedSize: number;

  @Column({ type: 'int', default: 0 })
  recordCount: number;

  @Column({ length: 500, nullable: true })
  filePath: string;

  @Column({ length: 100, nullable: true })
  checksum: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @Column({ type: 'timestamp', nullable: true })
  startedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  completedAt: Date;

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
