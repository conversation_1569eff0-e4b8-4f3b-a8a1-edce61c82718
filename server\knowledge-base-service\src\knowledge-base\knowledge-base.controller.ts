import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { KnowledgeBaseService } from './knowledge-base.service';
import { CreateKnowledgeBaseDto, UpdateKnowledgeBaseDto, SearchKnowledgeBaseDto } from './dto/create-knowledge-base.dto';
import { KnowledgeBase } from './entities/knowledge-base.entity';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { CurrentUser } from '../common/decorators/current-user.decorator';

@ApiTags('knowledge-base')
@Controller('knowledge-base')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class KnowledgeBaseController {
  constructor(private readonly knowledgeBaseService: KnowledgeBaseService) {}

  @Post()
  @ApiOperation({ summary: '创建知识库' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '知识库创建成功',
    type: KnowledgeBase,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误',
  })
  async create(
    @CurrentUser() user: any,
    @Body() createKnowledgeBaseDto: CreateKnowledgeBaseDto,
  ): Promise<KnowledgeBase> {
    return this.knowledgeBaseService.create(user.id, createKnowledgeBaseDto);
  }

  @Get()
  @ApiOperation({ summary: '获取用户的知识库列表' })
  @ApiQuery({ name: 'page', required: false, description: '页码', example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量', example: 10 })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        data: { type: 'array', items: { $ref: '#/components/schemas/KnowledgeBase' } },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
      },
    },
  })
  async findAll(
    @CurrentUser() user: any,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    return this.knowledgeBaseService.findByOwner(user.id, page, limit);
  }

  @Get('scene/:sceneId')
  @ApiOperation({ summary: '根据场景ID获取知识库' })
  @ApiParam({ name: 'sceneId', description: '场景ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    type: [KnowledgeBase],
  })
  async findByScene(@Param('sceneId') sceneId: string): Promise<KnowledgeBase[]> {
    return this.knowledgeBaseService.findByScene(sceneId);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取知识库详情' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    type: KnowledgeBase,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '知识库不存在',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: '没有权限访问',
  })
  async findOne(@CurrentUser() user: any, @Param('id') id: string): Promise<KnowledgeBase> {
    return this.knowledgeBaseService.findOne(id, user.id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新知识库' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '更新成功',
    type: KnowledgeBase,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '知识库不存在',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: '没有权限修改',
  })
  async update(
    @CurrentUser() user: any,
    @Param('id') id: string,
    @Body() updateKnowledgeBaseDto: UpdateKnowledgeBaseDto,
  ): Promise<KnowledgeBase> {
    return this.knowledgeBaseService.update(id, user.id, updateKnowledgeBaseDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除知识库' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: '删除成功',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '知识库不存在',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: '没有权限删除',
  })
  async remove(@CurrentUser() user: any, @Param('id') id: string): Promise<void> {
    return this.knowledgeBaseService.remove(id, user.id);
  }

  @Post(':id/search')
  @ApiOperation({ summary: '搜索知识库' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '搜索成功',
    schema: {
      type: 'object',
      properties: {
        query: { type: 'string' },
        results: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              content: { type: 'string' },
              score: { type: 'number' },
              metadata: { type: 'object' },
            },
          },
        },
        knowledgeBase: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            description: { type: 'string' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '知识库不存在',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '知识库不可用',
  })
  async search(
    @CurrentUser() user: any,
    @Param('id') id: string,
    @Body() searchDto: SearchKnowledgeBaseDto,
  ) {
    return this.knowledgeBaseService.search(id, searchDto, user.id);
  }

  @Get(':id/statistics')
  @ApiOperation({ summary: '获取知识库统计信息' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        statistics: {
          type: 'object',
          properties: {
            documentCount: { type: 'number' },
            totalSize: { type: 'number' },
            vectorCount: { type: 'number' },
            lastUpdated: { type: 'string' },
          },
        },
        status: { type: 'string' },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' },
      },
    },
  })
  async getStatistics(@CurrentUser() user: any, @Param('id') id: string) {
    return this.knowledgeBaseService.getStatistics(id, user.id);
  }
}
