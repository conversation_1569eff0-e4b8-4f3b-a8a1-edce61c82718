import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
  namespace: '/industrial-data',
})
export class IndustrialWebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(IndustrialWebSocketGateway.name);
  private connectedClients = new Map<string, Socket>();

  handleConnection(client: Socket) {
    this.connectedClients.set(client.id, client);
    this.logger.log(`客户端连接: ${client.id}, 总连接数: ${this.connectedClients.size}`);
    
    // 发送连接成功消息
    client.emit('connected', {
      message: '连接成功',
      clientId: client.id,
      timestamp: new Date()
    });
  }

  handleDisconnect(client: Socket) {
    this.connectedClients.delete(client.id);
    this.logger.log(`客户端断开: ${client.id}, 总连接数: ${this.connectedClients.size}`);
  }

  /**
   * 订阅实时数据
   */
  @SubscribeMessage('subscribe-realtime-data')
  handleSubscribeRealtimeData(
    @MessageBody() data: { deviceIds?: string[]; tagNames?: string[] },
    @ConnectedSocket() client: Socket,
  ) {
    const { deviceIds, tagNames } = data;
    
    if (deviceIds) {
      deviceIds.forEach(deviceId => {
        client.join(`device-${deviceId}`);
      });
    }
    
    if (tagNames) {
      tagNames.forEach(tagName => {
        client.join(`tag-${tagName}`);
      });
    }

    this.logger.log(`客户端 ${client.id} 订阅实时数据: 设备${deviceIds?.length || 0}个, 标签${tagNames?.length || 0}个`);
    
    return {
      event: 'subscription-confirmed',
      data: { deviceIds, tagNames, message: '订阅成功' }
    };
  }

  /**
   * 取消订阅实时数据
   */
  @SubscribeMessage('unsubscribe-realtime-data')
  handleUnsubscribeRealtimeData(
    @MessageBody() data: { deviceIds?: string[]; tagNames?: string[] },
    @ConnectedSocket() client: Socket,
  ) {
    const { deviceIds, tagNames } = data;
    
    if (deviceIds) {
      deviceIds.forEach(deviceId => {
        client.leave(`device-${deviceId}`);
      });
    }
    
    if (tagNames) {
      tagNames.forEach(tagName => {
        client.leave(`tag-${tagName}`);
      });
    }

    this.logger.log(`客户端 ${client.id} 取消订阅实时数据`);
    
    return {
      event: 'unsubscription-confirmed',
      data: { deviceIds, tagNames, message: '取消订阅成功' }
    };
  }

  /**
   * 订阅告警
   */
  @SubscribeMessage('subscribe-alerts')
  handleSubscribeAlerts(
    @MessageBody() data: { deviceIds?: string[]; severity?: string[] },
    @ConnectedSocket() client: Socket,
  ) {
    client.join('alerts');
    
    const { deviceIds, severity } = data;
    if (deviceIds) {
      deviceIds.forEach(deviceId => {
        client.join(`alerts-device-${deviceId}`);
      });
    }

    this.logger.log(`客户端 ${client.id} 订阅告警`);
    
    return {
      event: 'alert-subscription-confirmed',
      data: { message: '告警订阅成功' }
    };
  }

  /**
   * 获取连接状态
   */
  @SubscribeMessage('get-connection-status')
  handleGetConnectionStatus(@ConnectedSocket() client: Socket) {
    return {
      event: 'connection-status',
      data: {
        clientId: client.id,
        connected: true,
        connectedAt: new Date(),
        totalClients: this.connectedClients.size
      }
    };
  }

  /**
   * 广播实时数据
   */
  broadcastRealtimeData(deviceId: string, tagName: string, data: any) {
    this.server.to(`device-${deviceId}`).emit('realtime-data', {
      deviceId,
      tagName,
      data,
      timestamp: new Date()
    });

    this.server.to(`tag-${tagName}`).emit('realtime-data', {
      deviceId,
      tagName,
      data,
      timestamp: new Date()
    });
  }

  /**
   * 广播设备状态变化
   */
  broadcastDeviceStatus(deviceId: string, status: any) {
    this.server.to(`device-${deviceId}`).emit('device-status-change', {
      deviceId,
      status,
      timestamp: new Date()
    });

    // 同时广播给所有客户端
    this.server.emit('device-status-update', {
      deviceId,
      status,
      timestamp: new Date()
    });
  }

  /**
   * 广播告警
   */
  broadcastAlert(alert: any) {
    // 广播给所有订阅告警的客户端
    this.server.to('alerts').emit('new-alert', alert);
    
    // 广播给订阅特定设备告警的客户端
    if (alert.deviceId) {
      this.server.to(`alerts-device-${alert.deviceId}`).emit('device-alert', alert);
    }
  }

  /**
   * 广播系统消息
   */
  broadcastSystemMessage(message: any) {
    this.server.emit('system-message', {
      ...message,
      timestamp: new Date()
    });
  }

  /**
   * 获取连接统计
   */
  getConnectionStats() {
    return {
      totalConnections: this.connectedClients.size,
      connectedClients: Array.from(this.connectedClients.keys())
    };
  }

  /**
   * 向特定客户端发送消息
   */
  sendToClient(clientId: string, event: string, data: any) {
    const client = this.connectedClients.get(clientId);
    if (client) {
      client.emit(event, data);
      return true;
    }
    return false;
  }

  /**
   * 广播给所有客户端
   */
  broadcastToAll(event: string, data: any) {
    this.server.emit(event, data);
  }
}
