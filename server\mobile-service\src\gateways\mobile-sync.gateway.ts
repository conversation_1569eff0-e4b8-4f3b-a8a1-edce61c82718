/**
 * 移动端同步WebSocket网关
 * 
 * 提供移动端实时数据同步的WebSocket服务
 */

import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  MessageBody,
  ConnectedSocket,
  WsException
} from '@nestjs/websockets';
import { Logger, UseGuards } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { MobileSyncService } from '../services/mobile-sync.service';

/**
 * WebSocket消息类型
 */
export enum MessageType {
  CHANGE = 'change',
  CONFLICT = 'conflict',
  SYNC_REQUEST = 'sync_request',
  SYNC_RESPONSE = 'sync_response',
  STATUS_UPDATE = 'status_update',
  HEARTBEAT = 'heartbeat',
  ERROR = 'error'
}

/**
 * 客户端信息接口
 */
interface ClientInfo {
  userId: string;
  deviceId: string;
  platform: string;
  version: string;
  lastActivity: number;
  isAuthenticated: boolean;
}

/**
 * WebSocket消息接口
 */
interface WebSocketMessage {
  type: MessageType;
  data: any;
  timestamp: number;
  messageId?: string;
}

/**
 * 移动端同步WebSocket网关
 */
@WebSocketGateway({
  namespace: '/mobile/sync',
  cors: {
    origin: '*',
    credentials: true
  },
  transports: ['websocket', 'polling']
})
export class MobileSyncGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(MobileSyncGateway.name);
  private clients = new Map<string, ClientInfo>();
  private deviceToSocket = new Map<string, string>();
  private heartbeatInterval?: NodeJS.Timeout;

  constructor(
    private readonly jwtService: JwtService,
    private readonly mobileSyncService: MobileSyncService
  ) {}

  /**
   * 网关初始化
   */
  afterInit(server: Server) {
    this.logger.log('移动端同步WebSocket网关已初始化');
    this.startHeartbeat();
  }

  /**
   * 客户端连接
   */
  async handleConnection(client: Socket) {
    try {
      this.logger.log(`客户端连接: ${client.id}`);
      
      // 验证连接
      const clientInfo = await this.authenticateClient(client);
      if (!clientInfo) {
        client.disconnect();
        return;
      }
      
      // 存储客户端信息
      this.clients.set(client.id, clientInfo);
      this.deviceToSocket.set(clientInfo.deviceId, client.id);
      
      // 加入用户房间
      client.join(`user:${clientInfo.userId}`);
      client.join(`device:${clientInfo.deviceId}`);
      
      // 发送连接确认
      client.emit('connected', {
        type: MessageType.STATUS_UPDATE,
        data: {
          status: 'connected',
          deviceId: clientInfo.deviceId,
          timestamp: Date.now()
        }
      });
      
      // 检查是否有待同步的数据
      await this.checkPendingSync(client, clientInfo);
      
      this.logger.log(`用户 ${clientInfo.userId} 设备 ${clientInfo.deviceId} 已连接`);
      
    } catch (error) {
      this.logger.error('处理客户端连接失败:', error);
      client.disconnect();
    }
  }

  /**
   * 客户端断开连接
   */
  handleDisconnect(client: Socket) {
    const clientInfo = this.clients.get(client.id);
    
    if (clientInfo) {
      this.deviceToSocket.delete(clientInfo.deviceId);
      this.clients.delete(client.id);
      
      this.logger.log(`用户 ${clientInfo.userId} 设备 ${clientInfo.deviceId} 已断开连接`);
    } else {
      this.logger.log(`客户端 ${client.id} 已断开连接`);
    }
  }

  /**
   * 处理数据变更消息
   */
  @SubscribeMessage('change')
  async handleChange(
    @MessageBody() message: WebSocketMessage,
    @ConnectedSocket() client: Socket
  ) {
    try {
      const clientInfo = this.clients.get(client.id);
      if (!clientInfo || !clientInfo.isAuthenticated) {
        throw new WsException('未认证的客户端');
      }
      
      this.updateClientActivity(client.id);
      
      // 处理数据变更
      const result = await this.mobileSyncService.processRealtimeChange(
        clientInfo.userId,
        clientInfo.deviceId,
        message.data
      );
      
      // 发送处理结果
      client.emit('change_result', {
        type: MessageType.SYNC_RESPONSE,
        data: result,
        timestamp: Date.now(),
        messageId: message.messageId
      });
      
      // 如果有冲突，通知客户端
      if (result.conflicts && result.conflicts.length > 0) {
        client.emit('conflict', {
          type: MessageType.CONFLICT,
          data: result.conflicts,
          timestamp: Date.now()
        });
      }
      
      // 广播变更到其他设备
      await this.broadcastChangeToOtherDevices(
        clientInfo.userId,
        clientInfo.deviceId,
        message.data
      );
      
    } catch (error) {
      this.logger.error('处理数据变更失败:', error);
      client.emit('error', {
        type: MessageType.ERROR,
        data: {
          message: error.message,
          code: 'CHANGE_PROCESSING_ERROR'
        },
        timestamp: Date.now(),
        messageId: message.messageId
      });
    }
  }

  /**
   * 处理同步请求
   */
  @SubscribeMessage('sync_request')
  async handleSyncRequest(
    @MessageBody() message: WebSocketMessage,
    @ConnectedSocket() client: Socket
  ) {
    try {
      const clientInfo = this.clients.get(client.id);
      if (!clientInfo || !clientInfo.isAuthenticated) {
        throw new WsException('未认证的客户端');
      }
      
      this.updateClientActivity(client.id);
      
      // 获取同步数据
      const syncData = await this.mobileSyncService.getSyncData(
        clientInfo.userId,
        clientInfo.deviceId,
        message.data
      );
      
      // 发送同步响应
      client.emit('sync_response', {
        type: MessageType.SYNC_RESPONSE,
        data: syncData,
        timestamp: Date.now(),
        messageId: message.messageId
      });
      
    } catch (error) {
      this.logger.error('处理同步请求失败:', error);
      client.emit('error', {
        type: MessageType.ERROR,
        data: {
          message: error.message,
          code: 'SYNC_REQUEST_ERROR'
        },
        timestamp: Date.now(),
        messageId: message.messageId
      });
    }
  }

  /**
   * 处理心跳消息
   */
  @SubscribeMessage('heartbeat')
  handleHeartbeat(
    @MessageBody() message: WebSocketMessage,
    @ConnectedSocket() client: Socket
  ) {
    const clientInfo = this.clients.get(client.id);
    if (clientInfo) {
      this.updateClientActivity(client.id);
      
      // 发送心跳响应
      client.emit('heartbeat_response', {
        type: MessageType.HEARTBEAT,
        data: {
          timestamp: Date.now(),
          status: 'alive'
        },
        timestamp: Date.now(),
        messageId: message.messageId
      });
    }
  }

  /**
   * 处理冲突解决
   */
  @SubscribeMessage('resolve_conflict')
  async handleConflictResolution(
    @MessageBody() message: WebSocketMessage,
    @ConnectedSocket() client: Socket
  ) {
    try {
      const clientInfo = this.clients.get(client.id);
      if (!clientInfo || !clientInfo.isAuthenticated) {
        throw new WsException('未认证的客户端');
      }
      
      this.updateClientActivity(client.id);
      
      // 解决冲突
      const result = await this.mobileSyncService.resolveConflict(
        clientInfo.userId,
        clientInfo.deviceId,
        message.data.conflictId,
        message.data.resolution,
        message.data.mergedData
      );
      
      // 发送解决结果
      client.emit('conflict_resolved', {
        type: MessageType.SYNC_RESPONSE,
        data: result,
        timestamp: Date.now(),
        messageId: message.messageId
      });
      
    } catch (error) {
      this.logger.error('处理冲突解决失败:', error);
      client.emit('error', {
        type: MessageType.ERROR,
        data: {
          message: error.message,
          code: 'CONFLICT_RESOLUTION_ERROR'
        },
        timestamp: Date.now(),
        messageId: message.messageId
      });
    }
  }

  /**
   * 验证客户端
   */
  private async authenticateClient(client: Socket): Promise<ClientInfo | null> {
    try {
      const token = client.handshake.auth?.token || client.handshake.query?.token;
      const deviceId = client.handshake.query?.deviceId as string;
      const platform = client.handshake.query?.platform as string;
      const version = client.handshake.query?.version as string;
      
      if (!token || !deviceId) {
        this.logger.warn('客户端缺少认证信息');
        return null;
      }
      
      // 验证JWT令牌
      const payload = await this.jwtService.verifyAsync(token);
      
      if (!payload || !payload.sub) {
        this.logger.warn('无效的JWT令牌');
        return null;
      }
      
      return {
        userId: payload.sub,
        deviceId,
        platform: platform || 'unknown',
        version: version || '1.0.0',
        lastActivity: Date.now(),
        isAuthenticated: true
      };
      
    } catch (error) {
      this.logger.error('客户端认证失败:', error);
      return null;
    }
  }

  /**
   * 检查待同步数据
   */
  private async checkPendingSync(client: Socket, clientInfo: ClientInfo): Promise<void> {
    try {
      const pendingChanges = await this.mobileSyncService.getPendingChanges(
        clientInfo.userId,
        clientInfo.deviceId
      );
      
      if (pendingChanges.length > 0) {
        client.emit('pending_sync', {
          type: MessageType.SYNC_REQUEST,
          data: {
            pendingCount: pendingChanges.length,
            lastSyncTime: await this.mobileSyncService.getLastSyncTime(
              clientInfo.userId,
              clientInfo.deviceId
            )
          },
          timestamp: Date.now()
        });
      }
      
    } catch (error) {
      this.logger.error('检查待同步数据失败:', error);
    }
  }

  /**
   * 广播变更到其他设备
   */
  private async broadcastChangeToOtherDevices(
    userId: string,
    excludeDeviceId: string,
    change: any
  ): Promise<void> {
    try {
      // 获取用户的其他设备
      const userDevices = await this.mobileSyncService.getUserDevices(userId);

      for (const device of userDevices) {
        if (device.deviceId !== excludeDeviceId) {
          const socketId = this.deviceToSocket.get(device.deviceId);

          if (socketId) {
            const socket = this.server.sockets.sockets.get(socketId);

            if (socket) {
              socket.emit('remote_change', {
                type: MessageType.CHANGE,
                data: change,
                timestamp: Date.now()
              });
            }
          }
        }
      }

    } catch (error) {
      this.logger.error('广播变更失败:', error);
    }
  }

  /**
   * 更新客户端活动时间
   */
  private updateClientActivity(clientId: string): void {
    const clientInfo = this.clients.get(clientId);
    if (clientInfo) {
      clientInfo.lastActivity = Date.now();
    }
  }

  /**
   * 启动心跳检测
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.checkClientHeartbeat();
    }, 30000); // 30秒检查一次
  }

  /**
   * 检查客户端心跳
   */
  private checkClientHeartbeat(): void {
    const now = Date.now();
    const timeout = 60000; // 60秒超时

    for (const [clientId, clientInfo] of this.clients) {
      if (now - clientInfo.lastActivity > timeout) {
        this.logger.warn(`客户端 ${clientId} 心跳超时，断开连接`);

        const socket = this.server.sockets.sockets.get(clientId);
        if (socket) {
          socket.disconnect();
        }
      }
    }
  }

  /**
   * 向特定设备发送消息
   */
  public sendToDevice(deviceId: string, message: WebSocketMessage): boolean {
    const socketId = this.deviceToSocket.get(deviceId);

    if (socketId) {
      const socket = this.server.sockets.sockets.get(socketId);

      if (socket) {
        socket.emit('message', message);
        return true;
      }
    }

    return false;
  }

  /**
   * 向用户的所有设备发送消息
   */
  public sendToUser(userId: string, message: WebSocketMessage): number {
    let sentCount = 0;
    
    this.server.to(`user:${userId}`).emit('message', message);
    
    // 计算发送的设备数量
    for (const [clientId, clientInfo] of this.clients) {
      if (clientInfo.userId === userId) {
        sentCount++;
      }
    }
    
    return sentCount;
  }

  /**
   * 获取在线设备数量
   */
  public getOnlineDeviceCount(userId?: string): number {
    if (userId) {
      let count = 0;
      for (const [, clientInfo] of this.clients) {
        if (clientInfo.userId === userId) {
          count++;
        }
      }
      return count;
    }
    
    return this.clients.size;
  }

  /**
   * 获取用户的在线设备
   */
  public getUserOnlineDevices(userId: string): string[] {
    const devices: string[] = [];
    
    for (const [, clientInfo] of this.clients) {
      if (clientInfo.userId === userId) {
        devices.push(clientInfo.deviceId);
      }
    }
    
    return devices;
  }

  /**
   * 关闭网关
   */
  public shutdown(): void {
    this.logger.log('正在关闭移动端同步WebSocket网关...');
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    
    // 断开所有客户端连接
    for (const [clientId] of this.clients) {
      const socket = this.server.sockets.sockets.get(clientId);
      if (socket) {
        socket.disconnect();
      }
    }
    
    this.clients.clear();
    this.deviceToSocket.clear();
    
    this.logger.log('移动端同步WebSocket网关已关闭');
  }
}
