import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ResponseDto } from '../dto/response.dto';

/**
 * 响应拦截器 - 统一响应格式
 */
@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, ResponseDto<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<ResponseDto<T>> {
    const request = context.switchToHttp().getRequest();
    
    return next.handle().pipe(
      map(data => {
        // 如果返回的数据已经是ResponseDto格式，直接返回
        if (data instanceof ResponseDto) {
          return data;
        }

        // 否则包装成统一格式
        return ResponseDto.success(data);
      }),
    );
  }
}
