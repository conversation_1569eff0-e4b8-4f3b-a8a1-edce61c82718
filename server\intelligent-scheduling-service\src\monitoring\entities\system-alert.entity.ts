import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * 警告级别枚举
 */
export enum AlertLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

/**
 * 警告状态枚举
 */
export enum AlertStatus {
  ACTIVE = 'active',
  ACKNOWLEDGED = 'acknowledged',
  RESOLVED = 'resolved',
  SUPPRESSED = 'suppressed',
}

/**
 * 警告类型枚举
 */
export enum AlertType {
  PERFORMANCE_DEGRADATION = 'performance_degradation',
  RESOURCE_SHORTAGE = 'resource_shortage',
  SYSTEM_ERROR = 'system_error',
  THRESHOLD_BREACH = 'threshold_breach',
  ANOMALY_DETECTION = 'anomaly_detection',
  CAPACITY_WARNING = 'capacity_warning',
  QUALITY_ISSUE = 'quality_issue',
  SECURITY_ALERT = 'security_alert',
}

/**
 * 系统警告实体
 */
@Entity('system_alerts')
@Index(['level', 'status'])
@Index(['alertType', 'timestamp'])
@Index(['source', 'timestamp'])
export class SystemAlert {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'alert_id', length: 100, unique: true })
  alertId: string;

  @Column({ length: 200 })
  title: string;

  @Column({ type: 'text' })
  message: string;

  @Column({
    type: 'enum',
    enum: AlertLevel,
    default: AlertLevel.INFO,
  })
  level: AlertLevel;

  @Column({
    type: 'enum',
    enum: AlertStatus,
    default: AlertStatus.ACTIVE,
  })
  status: AlertStatus;

  @Column({
    type: 'enum',
    enum: AlertType,
  })
  alertType: AlertType;

  @Column({ length: 100, comment: '警告来源' })
  source: string;

  @Column({ name: 'source_id', length: 100, nullable: true, comment: '来源ID' })
  sourceId: string;

  @Column({ type: 'datetime' })
  timestamp: Date;

  @Column({ name: 'first_occurrence', type: 'datetime' })
  firstOccurrence: Date;

  @Column({ name: 'last_occurrence', type: 'datetime' })
  lastOccurrence: Date;

  @Column({ name: 'occurrence_count', type: 'int', default: 1, comment: '发生次数' })
  occurrenceCount: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, nullable: true, comment: '严重程度评分' })
  severity: number;

  @Column({ type: 'json', nullable: true, comment: '相关指标' })
  relatedMetrics: {
    metricName: string;
    value: number;
    threshold: number;
    unit: string;
  }[];

  @Column({ type: 'json', nullable: true, comment: '影响范围' })
  impactScope: {
    affectedSystems: string[];
    affectedUsers: number;
    businessImpact: string;
  };

  @Column({ name: 'root_cause', type: 'text', nullable: true, comment: '根本原因' })
  rootCause: string;

  @Column({ name: 'recommended_actions', type: 'json', nullable: true, comment: '建议操作' })
  recommendedActions: string[];

  @Column({ name: 'auto_resolution', type: 'boolean', default: false, comment: '是否自动解决' })
  autoResolution: boolean;

  @Column({ name: 'resolution_time', type: 'datetime', nullable: true, comment: '解决时间' })
  resolutionTime: Date;

  @Column({ name: 'resolution_notes', type: 'text', nullable: true, comment: '解决备注' })
  resolutionNotes: string;

  @Column({ name: 'acknowledged_by', length: 100, nullable: true, comment: '确认人' })
  acknowledgedBy: string;

  @Column({ name: 'acknowledged_at', type: 'datetime', nullable: true, comment: '确认时间' })
  acknowledgedAt: Date;

  @Column({ name: 'resolved_by', length: 100, nullable: true, comment: '解决人' })
  resolvedBy: string;

  @Column({ name: 'escalation_level', type: 'int', default: 0, comment: '升级级别' })
  escalationLevel: number;

  @Column({ name: 'notification_sent', type: 'boolean', default: false, comment: '是否已发送通知' })
  notificationSent: boolean;

  @Column({ type: 'json', nullable: true, comment: '标签' })
  tags: Record<string, string>;

  @Column({ type: 'json', nullable: true, comment: '扩展属性' })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
