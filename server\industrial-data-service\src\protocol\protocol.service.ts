import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ProtocolDriver, ProtocolConfig, ConnectionResult, ReadResult, WriteResult } from './interfaces/protocol.interface';
import { ModbusDriver } from './drivers/modbus.driver';
import { OPCUADriver } from './drivers/opcua.driver';
import { MQTTDriver } from './drivers/mqtt.driver';
import { ProtocolType } from '../device-management/entities/device.entity';

@Injectable()
export class ProtocolService {
  private readonly logger = new Logger(ProtocolService.name);
  private drivers = new Map<string, ProtocolDriver>();

  constructor(
    private readonly modbusDriver: ModbusDriver,
    private readonly opcuaDriver: OPCUADriver,
    private readonly mqttDriver: MQTTDriver,
  ) {
    this.initializeDrivers();
  }

  /**
   * 初始化协议驱动
   */
  private initializeDrivers(): void {
    this.drivers.set(ProtocolType.MODBUS_TCP, this.modbusDriver);
    this.drivers.set(ProtocolType.MODBUS_RTU, this.modbusDriver);
    this.drivers.set(ProtocolType.OPC_UA, this.opcuaDriver);
    this.drivers.set(ProtocolType.MQTT, this.mqttDriver);

    this.logger.log('协议驱动初始化完成');
  }

  /**
   * 获取支持的协议列表
   */
  getSupportedProtocols(): string[] {
    return Array.from(this.drivers.keys());
  }

  /**
   * 获取协议驱动信息
   */
  getProtocolInfo(protocol: string): any {
    const driver = this.drivers.get(protocol);
    if (!driver) {
      throw new BadRequestException(`不支持的协议: ${protocol}`);
    }

    return {
      name: driver.name,
      version: driver.version,
      protocol
    };
  }

  /**
   * 连接设备
   */
  async connectDevice(config: ProtocolConfig): Promise<ConnectionResult> {
    try {
      const driver = this.getDriver(config.protocol);
      const result = await driver.connect(config);
      
      this.logger.log(`设备连接${result.success ? '成功' : '失败'}: ${config.deviceId} (${config.protocol})`);
      return result;

    } catch (error) {
      this.logger.error(`设备连接异常: ${config.deviceId}`, error.stack);
      throw error;
    }
  }

  /**
   * 断开设备连接
   */
  async disconnectDevice(deviceId: string, protocol: string): Promise<boolean> {
    try {
      const driver = this.getDriver(protocol);
      const result = await driver.disconnect(deviceId);
      
      this.logger.log(`设备断开连接${result ? '成功' : '失败'}: ${deviceId} (${protocol})`);
      return result;

    } catch (error) {
      this.logger.error(`设备断开连接异常: ${deviceId}`, error.stack);
      return false;
    }
  }

  /**
   * 读取设备数据
   */
  async readDeviceData(deviceId: string, protocol: string, address: string): Promise<ReadResult> {
    try {
      const driver = this.getDriver(protocol);
      const result = await driver.read(deviceId, address);
      
      if (result.success) {
        this.logger.debug(`数据读取成功: ${deviceId} ${address} = ${result.value}`);
      } else {
        this.logger.warn(`数据读取失败: ${deviceId} ${address} - ${result.error}`);
      }
      
      return result;

    } catch (error) {
      this.logger.error(`数据读取异常: ${deviceId} ${address}`, error.stack);
      throw error;
    }
  }

  /**
   * 批量读取设备数据
   */
  async readMultipleDeviceData(deviceId: string, protocol: string, addresses: string[]): Promise<ReadResult[]> {
    try {
      const driver = this.getDriver(protocol);
      const results = await driver.readMultiple(deviceId, addresses);

      const successCount = results.filter(r => r.success).length;
      this.logger.debug(`批量数据读取完成: ${deviceId} (${successCount}/${addresses.length})`);

      return results;

    } catch (error) {
      this.logger.error(`批量数据读取异常: ${deviceId}`, error.stack);
      throw error;
    }
  }

  /**
   * 读取多个标签数据
   */
  async readMultipleTags(deviceId: string, tagIds: string[], deviceConfig: any): Promise<any[]> {
    try {
      const protocol = deviceConfig.protocol || 'modbus_tcp';
      const driver = this.getDriver(protocol);

      // 转换标签ID为地址
      const addresses = tagIds.map(tagId => {
        // 这里可以根据设备配置转换标签ID为实际地址
        return tagId;
      });

      const results = await driver.readMultiple(deviceId, addresses);

      // 转换结果格式
      return results.map((result, index) => ({
        tagId: tagIds[index],
        tagName: tagIds[index],
        value: result.success ? result.value : null,
        quality: result.success ? 'good' : 'bad',
        timestamp: new Date(),
        metadata: result.success ? {} : { error: result.error },
      }));

    } catch (error) {
      this.logger.error(`读取多个标签数据异常: ${deviceId}`, error.stack);
      throw error;
    }
  }

  /**
   * 写入设备数据
   */
  async writeDeviceData(deviceId: string, protocol: string, address: string, value: any): Promise<WriteResult> {
    try {
      const driver = this.getDriver(protocol);
      const result = await driver.write(deviceId, address, value);
      
      if (result.success) {
        this.logger.debug(`数据写入成功: ${deviceId} ${address} = ${value}`);
      } else {
        this.logger.warn(`数据写入失败: ${deviceId} ${address} - ${result.error}`);
      }
      
      return result;

    } catch (error) {
      this.logger.error(`数据写入异常: ${deviceId} ${address}`, error.stack);
      throw error;
    }
  }

  /**
   * 批量写入设备数据
   */
  async writeMultipleDeviceData(
    deviceId: string, 
    protocol: string, 
    data: { address: string; value: any }[]
  ): Promise<WriteResult[]> {
    try {
      const driver = this.getDriver(protocol);
      const results = await driver.writeMultiple(deviceId, data);
      
      const successCount = results.filter(r => r.success).length;
      this.logger.debug(`批量数据写入完成: ${deviceId} (${successCount}/${data.length})`);
      
      return results;

    } catch (error) {
      this.logger.error(`批量数据写入异常: ${deviceId}`, error.stack);
      throw error;
    }
  }

  /**
   * 检查设备连接状态
   */
  isDeviceConnected(deviceId: string, protocol: string): boolean {
    try {
      const driver = this.getDriver(protocol);
      return driver.isConnected(deviceId);
    } catch (error) {
      this.logger.error(`检查设备连接状态异常: ${deviceId}`, error.stack);
      return false;
    }
  }

  /**
   * 获取设备连接信息
   */
  getDeviceConnectionInfo(deviceId: string, protocol: string): any {
    try {
      const driver = this.getDriver(protocol);
      return driver.getConnectionInfo(deviceId);
    } catch (error) {
      this.logger.error(`获取设备连接信息异常: ${deviceId}`, error.stack);
      return null;
    }
  }

  /**
   * 获取所有连接的设备信息
   */
  getAllConnectedDevices(): any[] {
    const connectedDevices = [];
    
    for (const [protocol, driver] of this.drivers) {
      // 这里需要根据实际的驱动实现来获取所有连接的设备
      // 暂时返回空数组，实际项目中需要在驱动中维护连接列表
    }
    
    return connectedDevices;
  }

  /**
   * 获取协议驱动
   */
  private getDriver(protocol: string): ProtocolDriver {
    const driver = this.drivers.get(protocol);
    if (!driver) {
      throw new BadRequestException(`不支持的协议: ${protocol}`);
    }
    return driver;
  }

  /**
   * 测试协议连接
   */
  async testProtocolConnection(config: ProtocolConfig): Promise<{ success: boolean; message: string; responseTime?: number }> {
    const startTime = Date.now();
    
    try {
      const result = await this.connectDevice(config);
      
      if (result.success) {
        // 连接成功后立即断开
        await this.disconnectDevice(config.deviceId, config.protocol);
        
        return {
          success: true,
          message: '协议连接测试成功',
          responseTime: Date.now() - startTime
        };
      } else {
        return {
          success: false,
          message: result.error || '协议连接测试失败'
        };
      }

    } catch (error) {
      return {
        success: false,
        message: `协议连接测试异常: ${error.message}`
      };
    }
  }
}
