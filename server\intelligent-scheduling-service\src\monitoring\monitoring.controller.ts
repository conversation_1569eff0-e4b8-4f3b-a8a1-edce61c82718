import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';

import { PerformanceMonitoringService } from './performance-monitoring.service';
import { AlertService } from './alert.service';
import { MetricsCollectionService } from './metrics-collection.service';

/**
 * 监控控制器
 */
@ApiTags('monitoring')
@Controller('monitoring')
export class MonitoringController {
  constructor(
    private readonly performanceService: PerformanceMonitoringService,
    private readonly alertService: AlertService,
    private readonly metricsService: MetricsCollectionService,
  ) {}

  /**
   * 获取系统监控仪表板
   */
  @Get('dashboard')
  @ApiOperation({ summary: '获取系统监控仪表板' })
  @ApiResponse({ status: 200, description: '监控仪表板数据' })
  async getMonitoringDashboard(): Promise<any> {
    const dashboard = await this.performanceService.getMonitoringDashboard();

    return {
      success: true,
      message: '获取监控仪表板成功',
      data: dashboard,
    };
  }

  /**
   * 获取性能指标
   */
  @Get('metrics')
  @ApiOperation({ summary: '获取性能指标' })
  @ApiQuery({ name: 'metricType', required: false, type: String })
  @ApiQuery({ name: 'source', required: false, type: String })
  @ApiQuery({ name: 'startTime', required: false, type: String })
  @ApiQuery({ name: 'endTime', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiResponse({ status: 200, description: '性能指标数据' })
  async getMetrics(
    @Query('metricType') metricType?: string,
    @Query('source') source?: string,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string,
    @Query('limit') limit: number = 100,
  ): Promise<any> {
    const metrics = await this.metricsService.getMetrics({
      metricType,
      source,
      startTime: startTime ? new Date(startTime) : undefined,
      endTime: endTime ? new Date(endTime) : undefined,
      limit,
    });

    return {
      success: true,
      message: '获取性能指标成功',
      data: metrics,
    };
  }

  /**
   * 获取系统警告
   */
  @Get('alerts')
  @ApiOperation({ summary: '获取系统警告' })
  @ApiQuery({ name: 'level', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'alertType', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'offset', required: false, type: Number })
  @ApiResponse({ status: 200, description: '系统警告列表' })
  async getAlerts(
    @Query('level') level?: string,
    @Query('status') status?: string,
    @Query('alertType') alertType?: string,
    @Query('limit') limit: number = 20,
    @Query('offset') offset: number = 0,
  ): Promise<any> {
    const alerts = await this.alertService.getAlerts({
      level,
      status,
      alertType,
      limit,
      offset,
    });

    return {
      success: true,
      message: '获取系统警告成功',
      data: alerts,
    };
  }

  /**
   * 确认警告
   */
  @Post('alerts/:id/acknowledge')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '确认系统警告' })
  @ApiParam({ name: 'id', description: '警告ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        acknowledgedBy: { type: 'string' },
        notes: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 200, description: '警告确认成功' })
  async acknowledgeAlert(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() request: any,
  ): Promise<any> {
    await this.alertService.acknowledgeAlert(id, request.acknowledgedBy, request.notes);

    return {
      success: true,
      message: '警告确认成功',
    };
  }

  /**
   * 解决警告
   */
  @Post('alerts/:id/resolve')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '解决系统警告' })
  @ApiParam({ name: 'id', description: '警告ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        resolvedBy: { type: 'string' },
        resolutionNotes: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 200, description: '警告解决成功' })
  async resolveAlert(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() request: any,
  ): Promise<any> {
    await this.alertService.resolveAlert(id, request.resolvedBy, request.resolutionNotes);

    return {
      success: true,
      message: '警告解决成功',
    };
  }

  /**
   * 获取性能报告
   */
  @Get('reports/performance')
  @ApiOperation({ summary: '获取性能分析报告' })
  @ApiQuery({ name: 'period', required: false, type: String })
  @ApiQuery({ name: 'scope', required: false, type: String })
  @ApiResponse({ status: 200, description: '性能分析报告' })
  async getPerformanceReport(
    @Query('period') period: string = 'week',
    @Query('scope') scope: string = 'all',
  ): Promise<any> {
    const report = await this.performanceService.generatePerformanceReport(period, scope);

    return {
      success: true,
      message: '获取性能报告成功',
      data: report,
    };
  }

  /**
   * 获取异常检测结果
   */
  @Get('anomalies')
  @ApiOperation({ summary: '获取异常检测结果' })
  @ApiQuery({ name: 'timeRange', required: false, type: String })
  @ApiQuery({ name: 'severity', required: false, type: String })
  @ApiResponse({ status: 200, description: '异常检测结果' })
  async getAnomalies(
    @Query('timeRange') timeRange: string = '24h',
    @Query('severity') severity?: string,
  ): Promise<any> {
    const anomalies = await this.performanceService.detectAnomalies(timeRange, severity);

    return {
      success: true,
      message: '获取异常检测结果成功',
      data: anomalies,
    };
  }

  /**
   * 获取系统健康状态
   */
  @Get('health/status')
  @ApiOperation({ summary: '获取系统健康状态' })
  @ApiResponse({ status: 200, description: '系统健康状态' })
  async getSystemHealth(): Promise<any> {
    const health = await this.performanceService.getSystemHealthStatus();

    return {
      success: true,
      message: '获取系统健康状态成功',
      data: health,
    };
  }

  /**
   * 创建自定义指标
   */
  @Post('metrics/custom')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '创建自定义指标' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        metricName: { type: 'string' },
        metricType: { type: 'string' },
        source: { type: 'string' },
        value: { type: 'number' },
        unit: { type: 'string' },
        tags: { type: 'object' },
      },
    },
  })
  @ApiResponse({ status: 201, description: '自定义指标创建成功' })
  async createCustomMetric(@Body() request: any): Promise<any> {
    const metric = await this.metricsService.createCustomMetric(request);

    return {
      success: true,
      message: '自定义指标创建成功',
      data: metric,
    };
  }
}
