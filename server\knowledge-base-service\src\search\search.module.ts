import { Module } from '@nestjs/common';
import { SearchService } from './search.service';
import { SearchController } from './search.controller';
import { VectorStoreModule } from '../vector-store/vector-store.module';
import { EmbeddingsModule } from '../embeddings/embeddings.module';

@Module({
  imports: [VectorStoreModule, EmbeddingsModule],
  controllers: [SearchController],
  providers: [SearchService],
  exports: [SearchService],
})
export class SearchModule {}
