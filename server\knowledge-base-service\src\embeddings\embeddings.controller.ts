/**
 * 嵌入控制器
 */
import {
  Controller,
  Post,
  Get,
  Body,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { EmbeddingsService } from './embeddings.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { Public } from '../common/decorators/public.decorator';
import { GenerateEmbeddingDto, BatchEmbeddingDto, SimilarityDto } from './dto/embeddings.dto';

@ApiTags('embeddings')
@Controller('embeddings')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class EmbeddingsController {
  constructor(private readonly embeddingsService: EmbeddingsService) {}

  @Post('generate')
  @ApiOperation({ summary: '生成文本嵌入向量' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '生成成功',
    schema: {
      type: 'object',
      properties: {
        text: { type: 'string' },
        embedding: {
          type: 'array',
          items: { type: 'number' },
        },
        dimension: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '文本格式错误或生成失败',
  })
  async generateEmbedding(@Body() dto: GenerateEmbeddingDto) {
    const embedding = await this.embeddingsService.generateEmbedding(dto.text, {
      maxLength: dto.maxLength,
      normalize: dto.normalize,
    });

    return {
      text: dto.text,
      embedding,
      dimension: embedding.length,
    };
  }

  @Post('batch')
  @ApiOperation({ summary: '批量生成文本嵌入向量' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '生成成功',
    schema: {
      type: 'object',
      properties: {
        texts: {
          type: 'array',
          items: { type: 'string' },
        },
        embeddings: {
          type: 'array',
          items: {
            type: 'array',
            items: { type: 'number' },
          },
        },
        dimension: { type: 'number' },
        count: { type: 'number' },
      },
    },
  })
  async generateBatchEmbeddings(@Body() dto: BatchEmbeddingDto) {
    const embeddings = await this.embeddingsService.generateBatchEmbeddings(dto.texts, {
      maxLength: dto.maxLength,
      normalize: dto.normalize,
    });

    return {
      texts: dto.texts,
      embeddings,
      dimension: embeddings.length > 0 ? embeddings[0].length : 0,
      count: embeddings.length,
    };
  }

  @Post('similarity')
  @ApiOperation({ summary: '计算两个文本的相似度' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '计算成功',
    schema: {
      type: 'object',
      properties: {
        text1: { type: 'string' },
        text2: { type: 'string' },
        similarity: { type: 'number' },
      },
    },
  })
  async calculateSimilarity(@Body() dto: SimilarityDto) {
    const similarity = await this.embeddingsService.calculateSimilarity(dto.text1, dto.text2);

    return {
      text1: dto.text1,
      text2: dto.text2,
      similarity,
    };
  }

  @Get('model-info')
  @Public()
  @ApiOperation({ summary: '获取嵌入模型信息' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        model: { type: 'string' },
        isInitialized: { type: 'boolean' },
        defaultDimension: { type: 'number' },
      },
    },
  })
  getModelInfo() {
    return this.embeddingsService.getModelInfo();
  }

  @Get('dimension')
  @ApiOperation({ summary: '获取嵌入向量维度' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        dimension: { type: 'number' },
      },
    },
  })
  async getEmbeddingDimension() {
    const dimension = await this.embeddingsService.getEmbeddingDimension();
    return { dimension };
  }

  @Get('health')
  @Public()
  @ApiOperation({ summary: '嵌入服务健康检查' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '服务正常',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string' },
        isHealthy: { type: 'boolean' },
        timestamp: { type: 'string' },
      },
    },
  })
  async healthCheck() {
    const isHealthy = await this.embeddingsService.healthCheck();
    
    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      isHealthy,
      timestamp: new Date().toISOString(),
    };
  }
}
