/**
 * 嵌入DTO
 */
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsBoolean, IsArray, ArrayMinSize, ArrayMaxSize } from 'class-validator';

export class GenerateEmbeddingDto {
  @ApiProperty({ 
    description: '要生成嵌入的文本', 
    example: '这是一段关于医疗设备的描述文本' 
  })
  @IsString()
  text: string;

  @ApiProperty({ 
    description: '最大文本长度', 
    required: false, 
    minimum: 1, 
    maximum: 2048, 
    default: 512 
  })
  @IsOptional()
  @IsNumber()
  maxLength?: number;

  @ApiProperty({ 
    description: '是否标准化向量', 
    required: false, 
    default: true 
  })
  @IsOptional()
  @IsBoolean()
  normalize?: boolean = true;
}

export class BatchEmbeddingDto {
  @ApiProperty({ 
    description: '要生成嵌入的文本数组', 
    example: [
      '这是第一段文本',
      '这是第二段文本',
      '这是第三段文本'
    ]
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(100)
  @IsString({ each: true })
  texts: string[];

  @ApiProperty({ 
    description: '最大文本长度', 
    required: false, 
    minimum: 1, 
    maximum: 2048, 
    default: 512 
  })
  @IsOptional()
  @IsNumber()
  maxLength?: number;

  @ApiProperty({ 
    description: '是否标准化向量', 
    required: false, 
    default: true 
  })
  @IsOptional()
  @IsBoolean()
  normalize?: boolean = true;
}

export class SimilarityDto {
  @ApiProperty({ 
    description: '第一个文本', 
    example: '高血压是一种常见的心血管疾病' 
  })
  @IsString()
  text1: string;

  @ApiProperty({ 
    description: '第二个文本', 
    example: '血压升高可能导致心脏病' 
  })
  @IsString()
  text2: string;
}
