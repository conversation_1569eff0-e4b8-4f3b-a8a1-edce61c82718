/**
 * Mobile-Service 项目结构验证脚本
 * 
 * 验证项目是否包含所有必需的文件和配置
 */

const fs = require('fs');
const path = require('path');

// 必需的文件和目录
const requiredStructure = {
  files: [
    // 根配置文件
    'package.json',
    'tsconfig.json',
    'nest-cli.json',
    '.env.example',
    'Dockerfile',
    'README.md',
    '项目修复总结.md',
    
    // 应用核心文件
    'src/main.ts',
    'src/app.module.ts',
    'src/app.controller.ts',
    'src/app.service.ts',
    
    // 控制器
    'src/controllers/mobile-sync.controller.ts',
    'src/device/device.controller.ts',
    'src/health/health.controller.ts',
    
    // 服务
    'src/services/mobile-sync.service.ts',
    'src/device/device.service.ts',
    'src/auth/auth.service.ts',
    'src/health/health.service.ts',
    
    // 网关
    'src/gateways/mobile-sync.gateway.ts',
    
    // 实体
    'src/mobile-sync/entities/sync-record.entity.ts',
    'src/mobile-sync/entities/conflict-record.entity.ts',
    'src/device/entities/mobile-device.entity.ts',
    'src/device/entities/device-session.entity.ts',
    
    // 守卫
    'src/guards/jwt-auth.guard.ts',
    'src/guards/mobile-device.guard.ts',
    
    // 模块
    'src/mobile-sync/mobile-sync.module.ts',
    'src/device/device.module.ts',
    'src/auth/auth.module.ts',
    'src/health/health.module.ts',
    
    // 认证策略
    'src/auth/strategies/jwt.strategy.ts',
    
    // 公共组件
    'src/common/filters/global-exception.filter.ts',
    'src/common/interceptors/logging.interceptor.ts',
  ],
  directories: [
    'src',
    'src/controllers',
    'src/services',
    'src/gateways',
    'src/mobile-sync',
    'src/mobile-sync/entities',
    'src/device',
    'src/device/entities',
    'src/auth',
    'src/auth/strategies',
    'src/health',
    'src/guards',
    'src/common',
    'src/common/filters',
    'src/common/interceptors',
    'scripts',
  ]
};

// 必需的依赖包
const requiredDependencies = [
  '@nestjs/common',
  '@nestjs/core',
  '@nestjs/platform-express',
  '@nestjs/platform-socket.io',
  '@nestjs/websockets',
  '@nestjs/microservices',
  '@nestjs/config',
  '@nestjs/jwt',
  '@nestjs/passport',
  '@nestjs/typeorm',
  '@nestjs/swagger',
  '@nestjs/schedule',
  '@nestjs/throttler',
  '@nestjs/terminus',
  'typeorm',
  'mysql2',
  'socket.io',
  'passport',
  'passport-jwt',
  'class-validator',
  'class-transformer',
];

/**
 * 检查文件是否存在
 */
function checkFileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

/**
 * 检查目录是否存在
 */
function checkDirectoryExists(dirPath) {
  try {
    const stats = fs.statSync(dirPath);
    return stats.isDirectory();
  } catch (error) {
    return false;
  }
}

/**
 * 验证项目结构
 */
function verifyProjectStructure() {
  console.log('🔍 开始验证 Mobile-Service 项目结构...\n');
  
  let allValid = true;
  let fileCount = 0;
  let dirCount = 0;
  
  // 检查文件
  console.log('📄 检查必需文件:');
  for (const file of requiredStructure.files) {
    const exists = checkFileExists(file);
    const status = exists ? '✅' : '❌';
    console.log(`  ${status} ${file}`);
    
    if (exists) {
      fileCount++;
    } else {
      allValid = false;
    }
  }
  
  console.log(`\n📁 检查必需目录:`);
  for (const dir of requiredStructure.directories) {
    const exists = checkDirectoryExists(dir);
    const status = exists ? '✅' : '❌';
    console.log(`  ${status} ${dir}/`);
    
    if (exists) {
      dirCount++;
    } else {
      allValid = false;
    }
  }
  
  // 检查package.json中的依赖
  console.log(`\n📦 检查必需依赖:`);
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    let depCount = 0;
    for (const dep of requiredDependencies) {
      const exists = dependencies.hasOwnProperty(dep);
      const status = exists ? '✅' : '❌';
      console.log(`  ${status} ${dep}`);
      
      if (exists) {
        depCount++;
      } else {
        allValid = false;
      }
    }
    
    console.log(`\n📊 依赖统计: ${depCount}/${requiredDependencies.length} 个依赖已配置`);
  } catch (error) {
    console.log('  ❌ 无法读取 package.json');
    allValid = false;
  }
  
  // 输出结果
  console.log('\n' + '='.repeat(60));
  console.log(`📊 验证结果统计:`);
  console.log(`  📄 文件: ${fileCount}/${requiredStructure.files.length} 个存在`);
  console.log(`  📁 目录: ${dirCount}/${requiredStructure.directories.length} 个存在`);
  
  if (allValid) {
    console.log('\n🎉 Mobile-Service 项目结构验证通过！');
    console.log('✅ 所有必需文件和配置都已就位');
    console.log('✅ 项目结构完整，可以开始开发和部署');
    console.log('\n🚀 下一步操作:');
    console.log('  1. npm install          # 安装依赖');
    console.log('  2. cp .env.example .env  # 配置环境变量');
    console.log('  3. npm run start:dev     # 启动开发服务');
    console.log('  4. 访问 http://localhost:3009/api/docs # 查看API文档');
  } else {
    console.log('\n❌ Mobile-Service 项目结构验证失败！');
    console.log('❌ 存在缺失的文件或配置');
    console.log('❌ 请检查上述标记为 ❌ 的项目');
  }
  
  console.log('='.repeat(60));
  
  return allValid;
}

// 运行验证
if (require.main === module) {
  const isValid = verifyProjectStructure();
  process.exit(isValid ? 0 : 1);
}

module.exports = { verifyProjectStructure };
