import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * 优化策略枚举
 */
export enum OptimizationStrategy {
  LOAD_SHIFTING = 'load_shifting',
  PEAK_SHAVING = 'peak_shaving',
  DEMAND_RESPONSE = 'demand_response',
  EFFICIENCY_IMPROVEMENT = 'efficiency_improvement',
  RENEWABLE_INTEGRATION = 'renewable_integration',
  ENERGY_STORAGE = 'energy_storage',
}

/**
 * 计划状态枚举
 */
export enum PlanStatus {
  DRAFT = 'draft',
  APPROVED = 'approved',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

/**
 * 能耗优化计划实体
 */
@Entity('energy_optimization_plans')
@Index(['status', 'startDate'])
@Index(['strategy'])
export class EnergyOptimizationPlan {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'plan_id', length: 100, unique: true })
  planId: string;

  @Column({ length: 200 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: OptimizationStrategy,
  })
  strategy: OptimizationStrategy;

  @Column({
    type: 'enum',
    enum: PlanStatus,
    default: PlanStatus.DRAFT,
  })
  status: PlanStatus;

  @Column({ name: 'start_date', type: 'datetime' })
  startDate: Date;

  @Column({ name: 'end_date', type: 'datetime' })
  endDate: Date;

  @Column({ name: 'target_devices', type: 'json', comment: '目标设备列表' })
  targetDevices: string[];

  @Column({ name: 'baseline_consumption', type: 'decimal', precision: 12, scale: 4, comment: '基线能耗' })
  baselineConsumption: number;

  @Column({ name: 'target_consumption', type: 'decimal', precision: 12, scale: 4, comment: '目标能耗' })
  targetConsumption: number;

  @Column({ name: 'expected_savings', type: 'decimal', precision: 12, scale: 4, comment: '预期节能量' })
  expectedSavings: number;

  @Column({ name: 'actual_savings', type: 'decimal', precision: 12, scale: 4, nullable: true, comment: '实际节能量' })
  actualSavings: number;

  @Column({ name: 'cost_savings', type: 'decimal', precision: 12, scale: 2, nullable: true, comment: '成本节约' })
  costSavings: number;

  @Column({ name: 'implementation_cost', type: 'decimal', precision: 12, scale: 2, nullable: true, comment: '实施成本' })
  implementationCost: number;

  @Column({ name: 'roi_percentage', type: 'decimal', precision: 5, scale: 2, nullable: true, comment: 'ROI百分比' })
  roiPercentage: number;

  @Column({ name: 'carbon_reduction', type: 'decimal', precision: 10, scale: 4, nullable: true, comment: '碳减排量' })
  carbonReduction: number;

  @Column({ name: 'optimization_actions', type: 'json', comment: '优化行动' })
  optimizationActions: {
    actionType: string;
    description: string;
    targetValue: number;
    currentValue: number;
    priority: string;
    estimatedImpact: number;
  }[];

  @Column({ name: 'monitoring_metrics', type: 'json', nullable: true, comment: '监控指标' })
  monitoringMetrics: {
    metricName: string;
    targetValue: number;
    currentValue: number;
    unit: string;
    threshold: number;
  }[];

  @Column({ name: 'implementation_schedule', type: 'json', nullable: true, comment: '实施计划' })
  implementationSchedule: {
    phase: string;
    startDate: Date;
    endDate: Date;
    tasks: string[];
    responsible: string;
  }[];

  @Column({ name: 'risk_assessment', type: 'json', nullable: true, comment: '风险评估' })
  riskAssessment: {
    riskType: string;
    probability: string;
    impact: string;
    mitigation: string;
  }[];

  @Column({ name: 'approval_workflow', type: 'json', nullable: true, comment: '审批流程' })
  approvalWorkflow: {
    step: string;
    approver: string;
    status: string;
    timestamp: Date;
    comments: string;
  }[];

  @Column({ type: 'json', nullable: true, comment: '扩展属性' })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
