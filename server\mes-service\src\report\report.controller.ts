import { <PERSON>, Get, Post, Query, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { ReportService } from './report.service';

@ApiTags('reports')
@Controller('reports')
@ApiBearerAuth()
export class ReportController {
  private readonly logger = new Logger(ReportController.name);

  constructor(private readonly reportService: ReportService) {}

  @Post('production')
  @ApiOperation({ summary: '生成生产报表' })
  async generateProductionReport(@Query() query: any) {
    return await this.reportService.generateProductionReport(query);
  }

  @Post('quality')
  @ApiOperation({ summary: '生成质量报表' })
  async generateQualityReport(@Query() query: any) {
    return await this.reportService.generateQualityReport(query);
  }

  @Post('inventory')
  @ApiOperation({ summary: '生成库存报表' })
  async generateInventoryReport(@Query() query: any) {
    return await this.reportService.generateInventoryReport(query);
  }

  @Get()
  @ApiOperation({ summary: '获取报表列表' })
  async getReports(@Query() query: any) {
    return await this.reportService.getReports(query);
  }

  @Get(':id/export')
  @ApiOperation({ summary: '导出报表' })
  async exportReport(@Param('id') id: string, @Query('format') format: string = 'excel') {
    return await this.reportService.exportReport(id, format);
  }
}
