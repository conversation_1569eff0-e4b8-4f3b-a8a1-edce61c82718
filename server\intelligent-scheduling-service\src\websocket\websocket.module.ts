import { Module } from '@nestjs/common';

// 网关
import { SchedulingGateway } from './scheduling.gateway';
import { OptimizationGateway } from './optimization.gateway';

// 服务
import { WebSocketService } from './websocket.service';
import { NotificationService } from './notification.service';

@Module({
  providers: [
    SchedulingGateway,
    OptimizationGateway,
    WebSocketService,
    NotificationService,
  ],

  exports: [
    WebSocketService,
    NotificationService,
  ],
})
export class WebSocketModule {}
