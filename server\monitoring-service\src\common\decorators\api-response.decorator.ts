import { applyDecorators } from '@nestjs/common';
import { ApiResponse, ApiResponseOptions } from '@nestjs/swagger';
import { ResponseDto } from '../dto/response.dto';

/**
 * API成功响应装饰器
 */
export function ApiSuccessResponse(options?: Omit<ApiResponseOptions, 'status'>) {
  return applyDecorators(
    ApiResponse({
      status: 200,
      description: '操作成功',
      type: ResponseDto,
      ...options,
    }),
  );
}

/**
 * API创建响应装饰器
 */
export function ApiCreatedResponse(options?: Omit<ApiResponseOptions, 'status'>) {
  return applyDecorators(
    ApiResponse({
      status: 201,
      description: '创建成功',
      type: ResponseDto,
      ...options,
    }),
  );
}

/**
 * API错误响应装饰器
 */
export function ApiErrorResponse(options?: Omit<ApiResponseOptions, 'status'>) {
  return applyDecorators(
    ApiResponse({
      status: 400,
      description: '请求参数错误',
      type: ResponseDto,
      ...options,
    }),
    ApiResponse({
      status: 401,
      description: '未授权',
      type: ResponseDto,
    }),
    ApiResponse({
      status: 403,
      description: '禁止访问',
      type: ResponseDto,
    }),
    ApiResponse({
      status: 404,
      description: '资源未找到',
      type: ResponseDto,
    }),
    ApiResponse({
      status: 500,
      description: '服务器内部错误',
      type: ResponseDto,
    }),
  );
}

/**
 * API分页响应装饰器
 */
export function ApiPaginatedResponse(dataType: any, options?: Omit<ApiResponseOptions, 'status'>) {
  return applyDecorators(
    ApiResponse({
      status: 200,
      description: '查询成功',
      schema: {
        allOf: [
          { $ref: '#/components/schemas/ResponseDto' },
          {
            properties: {
              data: {
                type: 'object',
                properties: {
                  data: {
                    type: 'array',
                    items: { $ref: `#/components/schemas/${dataType.name}` },
                  },
                  pagination: {
                    type: 'object',
                    properties: {
                      page: { type: 'number' },
                      limit: { type: 'number' },
                      total: { type: 'number' },
                      totalPages: { type: 'number' },
                      hasNext: { type: 'boolean' },
                      hasPrev: { type: 'boolean' },
                    },
                  },
                },
              },
            },
          },
        ],
      },
      ...options,
    }),
  );
}
