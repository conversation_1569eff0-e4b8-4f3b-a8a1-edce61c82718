# 工业知识图谱服务

## 项目简介

工业知识图谱服务是一个基于NestJS框架开发的智能推理引擎和专家知识系统，专门为工业4.0环境设计。该服务提供了完整的知识图谱管理、智能推理、专家建议等功能。

## 主要功能

### 🧠 知识图谱管理
- **实体管理**: 支持设备、组件、流程、材料、人员等多种实体类型
- **关系管理**: 建立实体间的复杂关系网络
- **图谱查询**: 支持自然语言和Cypher查询
- **相似性搜索**: 基于属性和结构的相似实体发现
- **路径查找**: 实体间关系路径分析

### 🔍 智能推理引擎
- **规则推理**: 基于预定义规则的自动推理
- **递归推理**: 多层次深度推理分析
- **置信度计算**: 推理结果可信度评估
- **推理链追踪**: 完整的推理过程记录

### 👨‍🔬 专家系统
- **故障诊断**: 基于症状的智能故障诊断
- **维护建议**: 预防性维护方案推荐
- **优化建议**: 流程和性能优化建议
- **培训建议**: 个性化技能培训方案

### 📊 实时监控
- **WebSocket支持**: 实时数据推送
- **健康检查**: 完整的服务健康监控
- **性能指标**: 详细的系统性能统计
- **日志记录**: 全面的操作日志追踪

## 技术架构

### 核心技术栈
- **框架**: NestJS 9.x
- **数据库**: MySQL + Neo4j
- **缓存**: Redis
- **文档**: Swagger/OpenAPI
- **通信**: WebSocket + HTTP REST API
- **微服务**: Redis Transport

### 数据存储
- **MySQL**: 结构化数据存储（实体、关系、规则）
- **Neo4j**: 图数据库存储（知识图谱）
- **Redis**: 缓存和会话存储

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- MySQL >= 8.0
- Neo4j >= 4.0
- Redis >= 6.0

### 安装依赖
```bash
npm install
```

### 环境配置
```bash
# 复制环境变量配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 数据库初始化
```bash
# 创建MySQL数据库
mysql -u root -p -e "CREATE DATABASE knowledge_graph;"

# 启动Neo4j服务
neo4j start
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start
```

## API文档

服务启动后，可以通过以下地址访问API文档：
- 开发环境: http://localhost:3025/api/docs
- 生产环境: https://your-domain/api/docs

### 主要API端点

#### 知识图谱管理
- `POST /api/v1/knowledge/entities` - 创建知识实体
- `POST /api/v1/knowledge/relations` - 创建知识关系
- `POST /api/v1/knowledge/query` - 智能查询
- `GET /api/v1/knowledge/statistics` - 获取统计信息

#### 推理引擎
- `POST /api/v1/inference/rules` - 创建推理规则
- `GET /api/v1/inference/rules` - 获取推理规则
- `PUT /api/v1/inference/rules/:id` - 更新推理规则

#### 专家系统
- `POST /api/v1/expert/fault-diagnosis` - 故障诊断
- `POST /api/v1/expert/maintenance` - 维护建议
- `POST /api/v1/expert/optimization` - 优化建议
- `POST /api/v1/expert/training` - 培训建议

#### 健康检查
- `GET /api/v1/health` - 基础健康检查
- `GET /api/v1/health/detailed` - 详细健康检查
- `GET /api/v1/health/info` - 服务信息

## WebSocket事件

### 连接地址
```
ws://localhost:3025/knowledge
```

### 支持的事件
- `query_knowledge` - 知识查询
- `perform_inference` - 执行推理
- `get_statistics` - 获取统计信息

## 配置说明

### 数据库配置
```env
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=knowledge_graph
```

### Neo4j配置
```env
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password
```

### Redis配置
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
```

## 开发指南

### 项目结构
```
src/
├── main.ts                 # 应用入口
├── app.module.ts           # 主模块
├── knowledge/              # 知识图谱模块
│   ├── knowledge.module.ts
│   ├── knowledge.controller.ts
│   ├── knowledge-graph.service.ts
│   ├── knowledge.gateway.ts
│   ├── dto/               # 数据传输对象
│   └── entities/          # 实体定义
├── inference/             # 推理引擎模块
├── expert/               # 专家系统模块
├── health/               # 健康检查模块
├── common/               # 公共组件
└── entities/             # 实体索引
```

### 添加新的推理规则
```typescript
const rule: InferenceRule = {
  name: '新推理规则',
  description: '规则描述',
  condition: 'MATCH (n:Entity) WHERE n.property = "value"',
  conclusion: 'CREATE (n)-[:RELATION]->(m:NewEntity)',
  confidence: 0.8,
  priority: 1,
  enabled: true,
  category: 'custom',
  createdBy: 'system'
};
```

## 部署指南

### Docker部署
```bash
# 构建镜像
docker build -t knowledge-graph-service .

# 运行容器
docker run -p 3025:3025 knowledge-graph-service
```

### 生产环境配置
- 设置环境变量 `NODE_ENV=production`
- 配置数据库连接池
- 启用Redis集群
- 配置负载均衡
- 设置监控和日志

## 监控和维护

### 健康检查
服务提供多层次的健康检查：
- 基础检查：数据库连接、内存使用
- 详细检查：磁盘空间、系统资源
- 自定义检查：业务逻辑健康状态

### 性能监控
- API响应时间
- 数据库查询性能
- 内存和CPU使用率
- 推理引擎性能指标

### 日志管理
- 结构化日志输出
- 不同级别的日志记录
- 错误追踪和报警
- 操作审计日志

## 故障排除

### 常见问题
1. **Neo4j连接失败**: 检查Neo4j服务状态和连接配置
2. **MySQL连接超时**: 调整连接池配置和超时设置
3. **Redis连接异常**: 验证Redis服务和网络连接
4. **内存使用过高**: 检查缓存配置和数据量

### 调试模式
```bash
# 启用调试模式
npm run start:debug

# 查看详细日志
LOG_LEVEL=debug npm run start:dev
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- 文档: https://docs.example.com
- 问题反馈: https://github.com/example/issues
