import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Resource } from './entities/resource.entity';
import { ResourceAllocation } from './entities/resource-allocation.entity';

/**
 * 资源优化服务
 */
@Injectable()
export class ResourceOptimizationService {
  private readonly logger = new Logger(ResourceOptimizationService.name);

  constructor(
    @InjectRepository(Resource)
    private readonly resourceRepository: Repository<Resource>,
    @InjectRepository(ResourceAllocation)
    private readonly allocationRepository: Repository<ResourceAllocation>,
  ) {}

  /**
   * 生成资源优化建议
   */
  async generateOptimizationSuggestions(): Promise<any> {
    try {
      this.logger.log('开始生成资源优化建议');

      const suggestions = [];

      // 分析资源利用率
      const utilizationAnalysis = await this.analyzeResourceUtilization();
      suggestions.push(...this.generateUtilizationSuggestions(utilizationAnalysis));

      // 分析资源瓶颈
      const bottleneckAnalysis = await this.analyzeResourceBottlenecks();
      suggestions.push(...this.generateBottleneckSuggestions(bottleneckAnalysis));

      // 分析成本优化机会
      const costAnalysis = await this.analyzeCostOptimization();
      suggestions.push(...this.generateCostSuggestions(costAnalysis));

      return {
        totalSuggestions: suggestions.length,
        suggestions,
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`生成优化建议失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 分析资源瓶颈
   */
  async analyzeResourceBottlenecks(): Promise<any> {
    try {
      this.logger.log('开始分析资源瓶颈');

      const resources = await this.resourceRepository.find({
        relations: ['allocations'],
      });

      const bottlenecks = [];

      for (const resource of resources) {
        const utilization = await this.calculateResourceUtilization(resource.id);
        
        if (utilization.rate > 0.9) {
          bottlenecks.push({
            resourceId: resource.id,
            resourceName: resource.name,
            type: resource.type,
            utilizationRate: utilization.rate,
            severity: utilization.rate > 0.95 ? 'critical' : 'high',
            impact: this.calculateBottleneckImpact(resource, utilization),
            recommendations: this.generateBottleneckRecommendations(resource, utilization),
          });
        }
      }

      return {
        totalBottlenecks: bottlenecks.length,
        criticalBottlenecks: bottlenecks.filter(b => b.severity === 'critical').length,
        bottlenecks,
        analysisTime: new Date(),
      };
    } catch (error) {
      this.logger.error(`分析资源瓶颈失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 分析资源利用率
   */
  private async analyzeResourceUtilization(): Promise<any> {
    const resources = await this.resourceRepository.find();
    const utilizationData = [];

    for (const resource of resources) {
      const utilization = await this.calculateResourceUtilization(resource.id);
      utilizationData.push({
        resourceId: resource.id,
        resourceName: resource.name,
        type: resource.type,
        utilization,
      });
    }

    return {
      averageUtilization: utilizationData.reduce((sum, r) => sum + r.utilization.rate, 0) / utilizationData.length,
      resources: utilizationData,
    };
  }

  /**
   * 计算资源利用率
   */
  private async calculateResourceUtilization(resourceId: string): Promise<any> {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const allocations = await this.allocationRepository.find({
      where: {
        resourceId,
        status: 'active' as any,
      },
    });

    const totalTime = 7 * 24 * 60; // 一周的分钟数
    const allocatedTime = allocations.reduce((sum, allocation) => {
      const duration = (allocation.endTime.getTime() - allocation.startTime.getTime()) / (1000 * 60);
      return sum + duration;
    }, 0);

    return {
      rate: Math.min(allocatedTime / totalTime, 1),
      allocatedTime,
      totalTime,
      idleTime: totalTime - allocatedTime,
    };
  }

  /**
   * 分析成本优化
   */
  private async analyzeCostOptimization(): Promise<any> {
    const resources = await this.resourceRepository.find();
    const costAnalysis = [];

    for (const resource of resources) {
      const utilization = await this.calculateResourceUtilization(resource.id);
      const costEfficiency = this.calculateCostEfficiency(resource, utilization);
      
      costAnalysis.push({
        resourceId: resource.id,
        resourceName: resource.name,
        hourlyCost: resource.hourlyCost,
        utilization: utilization.rate,
        costEfficiency,
      });
    }

    return {
      totalCost: costAnalysis.reduce((sum, r) => sum + (r.hourlyCost || 0), 0),
      averageCostEfficiency: costAnalysis.reduce((sum, r) => sum + r.costEfficiency, 0) / costAnalysis.length,
      resources: costAnalysis,
    };
  }

  /**
   * 计算成本效率
   */
  private calculateCostEfficiency(resource: Resource, utilization: any): number {
    if (!resource.hourlyCost || utilization.rate === 0) return 0;
    return utilization.rate / resource.hourlyCost;
  }

  /**
   * 计算瓶颈影响
   */
  private calculateBottleneckImpact(resource: Resource, utilization: any): any {
    return {
      delayRisk: utilization.rate > 0.95 ? 'high' : 'medium',
      costImpact: (resource.hourlyCost || 0) * utilization.allocatedTime / 60,
      affectedTasks: Math.floor(utilization.rate * 10), // 简化计算
    };
  }

  /**
   * 生成瓶颈建议
   */
  private generateBottleneckRecommendations(resource: Resource, utilization: any): string[] {
    const recommendations = [];

    if (utilization.rate > 0.95) {
      recommendations.push('考虑增加同类型资源');
      recommendations.push('优化任务调度以减少资源冲突');
    }

    if (utilization.rate > 0.9) {
      recommendations.push('实施预防性维护计划');
      recommendations.push('考虑资源共享或外包');
    }

    return recommendations;
  }

  /**
   * 生成利用率建议
   */
  private generateUtilizationSuggestions(analysis: any): any[] {
    const suggestions = [];

    analysis.resources.forEach(resource => {
      if (resource.utilization.rate < 0.3) {
        suggestions.push({
          type: 'low_utilization',
          resourceId: resource.resourceId,
          message: `资源 ${resource.resourceName} 利用率过低 (${(resource.utilization.rate * 100).toFixed(1)}%)`,
          recommendation: '考虑重新分配任务或减少资源容量',
          priority: 'medium',
        });
      }
    });

    return suggestions;
  }

  /**
   * 生成瓶颈建议
   */
  private generateBottleneckSuggestions(analysis: any): any[] {
    return analysis.bottlenecks.map(bottleneck => ({
      type: 'bottleneck',
      resourceId: bottleneck.resourceId,
      message: `资源 ${bottleneck.resourceName} 存在瓶颈 (利用率: ${(bottleneck.utilizationRate * 100).toFixed(1)}%)`,
      recommendation: bottleneck.recommendations.join('; '),
      priority: bottleneck.severity,
    }));
  }

  /**
   * 生成成本建议
   */
  private generateCostSuggestions(analysis: any): any[] {
    const suggestions = [];

    analysis.resources.forEach(resource => {
      if (resource.costEfficiency < 0.5 && resource.utilization > 0.1) {
        suggestions.push({
          type: 'cost_optimization',
          resourceId: resource.resourceId,
          message: `资源 ${resource.resourceName} 成本效率较低`,
          recommendation: '考虑优化资源配置或寻找更经济的替代方案',
          priority: 'low',
        });
      }
    });

    return suggestions;
  }
}
