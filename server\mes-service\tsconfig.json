{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@/order/*": ["src/order/*"], "@/process/*": ["src/process/*"], "@/quality/*": ["src/quality/*"], "@/inventory/*": ["src/inventory/*"], "@/scheduling/*": ["src/scheduling/*"], "@/tracking/*": ["src/tracking/*"], "@/report/*": ["src/report/*"], "@/websocket/*": ["src/websocket/*"], "@/common/*": ["src/common/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}