import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CollaborationSession } from '../database/entities/collaboration-session.entity';

/**
 * 协作服务
 * 管理多用户协作会话
 */
@Injectable()
export class CollaborationService {
  private readonly logger = new Logger(CollaborationService.name);

  constructor(
    @InjectRepository(CollaborationSession)
    private readonly collaborationSessionRepository: Repository<CollaborationSession>,
  ) {}

  /**
   * 创建协作会话
   */
  async createCollaborationSession(data: any): Promise<CollaborationSession> {
    try {
      const session = this.collaborationSessionRepository.create({
        sessionName: data.sessionName,
        sessionType: data.sessionType || 'training',
        participants: data.participants || [],
        hostUserId: data.hostUserId,
        arvrSceneId: data.arvrSceneId,
        startTime: new Date(),
        status: 'waiting',
        settings: data.settings || {
          maxParticipants: 10,
          allowVoice: true,
          allowGesture: true,
          recordSession: false,
          shareScreen: false,
        },
      });

      const savedSession = await this.collaborationSessionRepository.save(session);
      this.logger.log(`协作会话已创建: ${savedSession.id}`);
      return savedSession;

    } catch (error) {
      this.logger.error('创建协作会话失败:', error);
      throw error;
    }
  }

  /**
   * 加入协作会话
   */
  async joinCollaborationSession(sessionId: string, userId: string, role: string = 'participant'): Promise<any> {
    try {
      const session = await this.collaborationSessionRepository.findOne({
        where: { id: sessionId },
      });

      if (!session) {
        throw new Error(`协作会话不存在: ${sessionId}`);
      }

      // 检查是否已经是参与者
      const existingParticipant = session.participants.find(p => p.userId === userId);
      if (existingParticipant) {
        return { success: true, message: '已经是会话参与者' };
      }

      // 添加新参与者
      session.participants.push({
        userId,
        role,
        joinTime: new Date(),
        status: 'active',
      });

      await this.collaborationSessionRepository.save(session);
      this.logger.log(`用户 ${userId} 已加入协作会话 ${sessionId}`);

      return { success: true, message: '成功加入协作会话' };

    } catch (error) {
      this.logger.error('加入协作会话失败:', error);
      throw error;
    }
  }

  /**
   * 获取协作会话列表
   */
  async getCollaborationSessions(userId?: string): Promise<CollaborationSession[]> {
    try {
      const queryBuilder = this.collaborationSessionRepository.createQueryBuilder('session');

      if (userId) {
        queryBuilder.where('session.hostUserId = :userId', { userId })
          .orWhere('JSON_CONTAINS(session.participants, JSON_OBJECT("userId", :userId))', { userId });
      }

      return await queryBuilder
        .orderBy('session.createdAt', 'DESC')
        .getMany();

    } catch (error) {
      this.logger.error('获取协作会话列表失败:', error);
      throw error;
    }
  }
}
