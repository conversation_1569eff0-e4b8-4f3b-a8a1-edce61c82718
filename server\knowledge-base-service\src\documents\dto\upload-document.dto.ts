/**
 * 文档上传DTO
 */
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject, IsUUID } from 'class-validator';

export class UploadDocumentDto {
  @ApiProperty({ description: '知识库ID', example: 'kb_123' })
  @IsUUID()
  knowledgeBaseId: string;

  @ApiProperty({ 
    description: '文档元数据', 
    required: false,
    example: {
      title: '医疗设备说明书',
      category: '设备文档',
      tags: ['医疗', '设备', '说明书'],
      author: '设备厂商'
    }
  })
  @IsOptional()
  @IsObject()
  metadata?: {
    title?: string;
    category?: string;
    tags?: string[];
    author?: string;
    description?: string;
    [key: string]: any;
  };
}

export class DocumentQueryDto {
  @ApiProperty({ description: '页码', required: false, minimum: 1, default: 1 })
  @IsOptional()
  page?: number = 1;

  @ApiProperty({ description: '每页数量', required: false, minimum: 1, maximum: 100, default: 10 })
  @IsOptional()
  limit?: number = 10;

  @ApiProperty({ description: '文档状态过滤', required: false })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({ description: '搜索关键词', required: false })
  @IsOptional()
  @IsString()
  search?: string;
}
