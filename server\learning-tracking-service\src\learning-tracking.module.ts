import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { BullModule } from '@nestjs/bull';
import { ScheduleModule } from '@nestjs/schedule';
import { JwtModule } from '@nestjs/jwt';

// 实体
import { LearningRecord } from './entities/learning-record.entity';
import { LearnerProfileEntity } from './entities/learner-profile.entity';
import { RecommendationEntity } from './entities/recommendation.entity';
import { ContentEntity } from './entities/content.entity';

// 控制器
import { LearningTrackingController } from './controllers/learning-tracking.controller';

// 服务
import { XAPIClientService } from './xapi/xapi-client.service';
import { XAPIStatementBuilderService } from './xapi/xapi-statement-builder.service';
import { LearninglocketClientService } from './learninglocker/learninglocker-client.service';
import { LearningDataSyncService } from './sync/learning-data-sync.service';
import { LearnerProfileAnalyzerService } from './profile/learner-profile-analyzer.service';
import { PersonalizedRecommendationService } from './recommendation/personalized-recommendation.service';

// 队列处理器
import { LearningSyncProcessor } from './processors/learning-sync.processor';

// 守卫
import { JwtAuthGuard } from './guards/jwt-auth.guard';

/**
 * 学习记录跟踪模块
 * 整合xAPI数据采集、用户画像分析和个性化推荐功能
 */
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 3306),
        username: configService.get('DB_USERNAME', 'root'),
        password: configService.get('DB_PASSWORD', ''),
        database: configService.get('DB_DATABASE', 'learning_tracking'),
        entities: [
          LearningRecord,
          LearnerProfileEntity,
          RecommendationEntity,
          ContentEntity
        ],
        synchronize: configService.get('NODE_ENV') === 'development',
        logging: configService.get('DB_LOGGING', false),
        timezone: '+08:00',
        charset: 'utf8mb4',
        extra: {
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
        },
      }),
      inject: [ConfigService],
    }),

    // TypeORM实体注册
    TypeOrmModule.forFeature([
      LearningRecord,
      LearnerProfileEntity,
      RecommendationEntity,
      ContentEntity
    ]),

    // HTTP客户端模块
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: configService.get('HTTP_TIMEOUT', 30000),
        maxRedirects: 5,
        retries: 3,
      }),
      inject: [ConfigService],
    }),

    // 队列模块
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get('REDIS_HOST', 'localhost'),
          port: configService.get('REDIS_PORT', 6379),
          password: configService.get('REDIS_PASSWORD'),
          db: configService.get('REDIS_DB', 0),
        },
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
        },
      }),
      inject: [ConfigService],
    }),

    // 注册队列
    BullModule.registerQueue({
      name: 'learning-sync',
    }),

    BullModule.registerQueue({
      name: 'profile-analysis',
    }),

    BullModule.registerQueue({
      name: 'recommendation-generation',
    }),

    // 定时任务模块
    ScheduleModule.forRoot(),

    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET', 'default-secret'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '24h'),
        },
      }),
      inject: [ConfigService],
    }),
  ],

  controllers: [
    LearningTrackingController,
  ],

  providers: [
    // xAPI相关服务
    XAPIClientService,
    XAPIStatementBuilderService,

    // Learninglocker集成服务
    LearninglocketClientService,

    // 数据同步服务
    LearningDataSyncService,

    // 用户画像分析服务
    LearnerProfileAnalyzerService,

    // 个性化推荐服务
    PersonalizedRecommendationService,

    // 队列处理器
    LearningSyncProcessor,

    // 守卫
    JwtAuthGuard,
  ],

  exports: [
    // 导出核心服务供其他模块使用
    LearningDataSyncService,
    LearnerProfileAnalyzerService,
    PersonalizedRecommendationService,
    XAPIClientService,
    XAPIStatementBuilderService,
  ],
})
export class LearningTrackingModule {
  constructor(private configService: ConfigService) {
    this.logModuleConfiguration();
  }

  /**
   * 记录模块配置信息
   */
  private logModuleConfiguration(): void {
    const config = {
      database: {
        host: this.configService.get('DB_HOST'),
        database: this.configService.get('DB_DATABASE'),
      },
      redis: {
        host: this.configService.get('REDIS_HOST'),
        port: this.configService.get('REDIS_PORT'),
      },
      xapi: {
        endpoint: this.configService.get('XAPI_ENDPOINT'),
        version: this.configService.get('XAPI_VERSION'),
      },
      learninglocker: {
        endpoint: this.configService.get('LEARNINGLOCKER_ENDPOINT'),
        organization: this.configService.get('LEARNINGLOCKER_ORGANIZATION'),
      },
    };

    console.log('学习记录跟踪模块配置:', JSON.stringify(config, null, 2));
  }
}
