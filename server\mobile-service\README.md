# 移动端服务 (Mobile Service)

移动端服务是DL引擎生态系统中专门为移动设备提供数据同步和实时通信功能的微服务。

## 🚀 功能特性

### 核心功能
- **数据同步**: 支持移动设备与服务器之间的双向数据同步
- **实时通信**: 基于WebSocket的实时数据推送和通信
- **设备管理**: 移动设备注册、状态管理和监控
- **冲突解决**: 智能的数据冲突检测和解决机制
- **离线支持**: 支持离线数据缓存和后续同步

### 技术特性
- **高性能**: 优化的数据传输和处理机制
- **高可用**: 完善的健康检查和监控体系
- **安全性**: JWT认证和设备验证机制
- **可扩展**: 模块化架构，易于扩展新功能

## 📋 系统要求

- Node.js >= 18.0.0
- MySQL >= 8.0
- Redis >= 6.0 (可选)
- npm >= 8.0.0

## 🛠️ 安装和配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd server/mobile-service
```

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

### 4. 数据库设置
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE mobile_service CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行数据库迁移（如果有）
npm run migration:run
```

### 5. 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

## 📚 API 文档

服务启动后，可以通过以下地址访问API文档：

- **Swagger文档**: http://localhost:3009/api/docs
- **健康检查**: http://localhost:3009/api/v1/health

## 🏗️ 项目结构

```
src/
├── main.ts                     # 应用启动入口
├── app.module.ts               # 主应用模块
├── app.controller.ts           # 主控制器
├── app.service.ts              # 主服务
│
├── auth/                       # 认证模块
│   ├── auth.module.ts
│   ├── auth.service.ts
│   └── strategies/
│       └── jwt.strategy.ts
│
├── mobile-sync/                # 移动端同步模块
│   ├── mobile-sync.module.ts
│   └── entities/
│       ├── sync-record.entity.ts
│       └── conflict-record.entity.ts
│
├── device/                     # 设备管理模块
│   ├── device.module.ts
│   ├── device.controller.ts
│   ├── device.service.ts
│   └── entities/
│       ├── mobile-device.entity.ts
│       └── device-session.entity.ts
│
├── controllers/                # 控制器
│   └── mobile-sync.controller.ts
│
├── services/                   # 服务层
│   └── mobile-sync.service.ts
│
├── gateways/                   # WebSocket网关
│   └── mobile-sync.gateway.ts
│
├── guards/                     # 守卫
│   ├── jwt-auth.guard.ts
│   └── mobile-device.guard.ts
│
├── health/                     # 健康检查模块
│   ├── health.module.ts
│   ├── health.controller.ts
│   └── health.service.ts
│
└── common/                     # 公共组件
    ├── filters/
    │   └── global-exception.filter.ts
    └── interceptors/
        └── logging.interceptor.ts
```

## 🔧 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `NODE_ENV` | 运行环境 | `development` |
| `PORT` | 服务端口 | `3009` |
| `DB_HOST` | 数据库主机 | `localhost` |
| `DB_PORT` | 数据库端口 | `3306` |
| `DB_USERNAME` | 数据库用户名 | `root` |
| `DB_PASSWORD` | 数据库密码 | - |
| `DB_DATABASE` | 数据库名称 | `mobile_service` |
| `JWT_SECRET` | JWT密钥 | - |
| `JWT_EXPIRES_IN` | JWT过期时间 | `7d` |

### 数据库配置

服务使用MySQL作为主数据库，存储以下数据：
- 移动设备信息
- 同步记录
- 冲突记录
- 设备会话

## 🚀 部署

### Docker 部署

```bash
# 构建镜像
docker build -t mobile-service:latest .

# 运行容器
docker run -d \
  --name mobile-service \
  -p 3009:3009 \
  -e DB_HOST=your-db-host \
  -e DB_PASSWORD=your-db-password \
  mobile-service:latest
```

### Docker Compose 部署

```yaml
version: '3.8'
services:
  mobile-service:
    build: .
    ports:
      - "3009:3009"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PASSWORD=your-password
    depends_on:
      - mysql
      
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=your-password
      - MYSQL_DATABASE=mobile_service
    volumes:
      - mysql_data:/var/lib/mysql

volumes:
  mysql_data:
```

## 🧪 测试

```bash
# 单元测试
npm run test

# E2E测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

## 📊 监控和日志

### 健康检查端点

- `GET /api/v1/health` - 基础健康检查
- `GET /api/v1/health/detailed` - 详细健康信息
- `GET /api/v1/health/status` - 服务状态
- `GET /api/v1/health/dependencies` - 依赖服务状态

### 日志配置

服务使用NestJS内置的日志系统，支持以下日志级别：
- `error` - 错误信息
- `warn` - 警告信息
- `log` - 一般信息
- `debug` - 调试信息

## 🔒 安全性

### 认证机制
- JWT令牌认证
- 设备ID验证
- 用户权限检查

### 数据安全
- 请求参数验证
- SQL注入防护
- XSS攻击防护

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 创建 Issue
- 发送邮件到项目维护者
- 查看项目文档和FAQ
