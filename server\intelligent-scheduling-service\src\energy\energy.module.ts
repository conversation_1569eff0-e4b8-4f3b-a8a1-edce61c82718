import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// 实体
import { EnergyConsumption } from './entities/energy-consumption.entity';
import { EnergyOptimizationPlan } from './entities/energy-optimization-plan.entity';

// 控制器
import { EnergyController } from './energy.controller';

// 服务
import { EnergyManagementService } from './energy-management.service';
import { EnergyOptimizationService } from './energy-optimization.service';
import { EnergyMonitoringService } from './energy-monitoring.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      EnergyConsumption,
      EnergyOptimizationPlan,
    ]),
  ],

  controllers: [EnergyController],

  providers: [
    EnergyManagementService,
    EnergyOptimizationService,
    EnergyMonitoringService,
  ],

  exports: [
    EnergyManagementService,
    EnergyOptimizationService,
    EnergyMonitoringService,
  ],
})
export class EnergyModule {}
