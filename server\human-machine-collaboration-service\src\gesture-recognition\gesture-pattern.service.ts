import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GesturePattern } from '../database/entities/gesture-pattern.entity';

/**
 * 手势模式服务
 * 管理手势模式的存储、加载和匹配
 */
@Injectable()
export class GesturePatternService {
  private readonly logger = new Logger(GesturePatternService.name);
  private patterns: GesturePattern[] = [];

  constructor(
    @InjectRepository(GesturePattern)
    private readonly gesturePatternRepository: Repository<GesturePattern>,
  ) {}

  /**
   * 加载手势模式
   */
  async loadPatterns(): Promise<void> {
    try {
      this.patterns = await this.gesturePatternRepository.find({
        where: { isActive: true },
      });

      // 如果没有预定义模式，创建一些基础模式
      if (this.patterns.length === 0) {
        await this.createDefaultPatterns();
        this.patterns = await this.gesturePatternRepository.find({
          where: { isActive: true },
        });
      }

      this.logger.log(`已加载 ${this.patterns.length} 个手势模式`);
    } catch (error) {
      this.logger.error('加载手势模式失败:', error);
    }
  }

  /**
   * 获取活跃模式
   */
  async getActivePatterns(): Promise<GesturePattern[]> {
    return this.patterns.filter(pattern => pattern.isActive);
  }

  /**
   * 获取所有模式
   */
  async getAllPatterns(): Promise<GesturePattern[]> {
    return await this.gesturePatternRepository.find();
  }

  /**
   * 更新使用统计
   */
  async updateUsageStats(patternId: string, accuracy: number): Promise<void> {
    try {
      const pattern = await this.gesturePatternRepository.findOne({
        where: { id: patternId },
      });

      if (pattern) {
        pattern.usageCount += 1;
        pattern.averageAccuracy = 
          (pattern.averageAccuracy * (pattern.usageCount - 1) + accuracy) / pattern.usageCount;
        
        await this.gesturePatternRepository.save(pattern);
      }
    } catch (error) {
      this.logger.error('更新使用统计失败:', error);
    }
  }

  /**
   * 获取使用统计
   */
  async getUsageStatistics(): Promise<any> {
    try {
      const totalPatterns = await this.gesturePatternRepository.count();
      const mostUsed = await this.gesturePatternRepository.find({
        order: { usageCount: 'DESC' },
        take: 5,
      });

      const avgAccuracy = await this.gesturePatternRepository
        .createQueryBuilder('pattern')
        .select('AVG(pattern.averageAccuracy)', 'avg')
        .getRawOne();

      return {
        totalPatterns,
        mostUsed: mostUsed.map(p => ({
          name: p.patternName,
          usageCount: p.usageCount,
          accuracy: p.averageAccuracy,
        })),
        averageAccuracy: parseFloat(avgAccuracy?.avg || '0'),
      };
    } catch (error) {
      this.logger.error('获取使用统计失败:', error);
      return {
        totalPatterns: 0,
        mostUsed: [],
        averageAccuracy: 0,
      };
    }
  }

  /**
   * 创建默认手势模式
   */
  private async createDefaultPatterns(): Promise<void> {
    const defaultPatterns = [
      {
        patternName: '点击',
        gestureType: 'static',
        description: '食指指向前方的点击手势',
        keyPoints: this.generateClickGestureKeyPoints(),
        threshold: 0.8,
        difficulty: 'easy',
        associatedAction: 'click',
        actionParameters: { type: 'primary' },
      },
      {
        patternName: '抓取',
        gestureType: 'static',
        description: '五指收拢的抓取手势',
        keyPoints: this.generateGrabGestureKeyPoints(),
        threshold: 0.8,
        difficulty: 'medium',
        associatedAction: 'grab',
        actionParameters: { type: 'object' },
      },
      {
        patternName: '挥手',
        gestureType: 'dynamic',
        description: '左右摆动的挥手手势',
        sequence: this.generateWaveGestureSequence(),
        threshold: 0.7,
        difficulty: 'medium',
        associatedAction: 'wave',
        actionParameters: { type: 'greeting' },
      },
    ];

    for (const patternData of defaultPatterns) {
      const pattern = this.gesturePatternRepository.create(patternData);
      await this.gesturePatternRepository.save(pattern);
    }

    this.logger.log('默认手势模式创建完成');
  }

  /**
   * 生成点击手势关键点
   */
  private generateClickGestureKeyPoints(): any[] {
    // 简化的点击手势关键点
    return [
      { x: 0.5, y: 0.3, z: 0.0 }, // 食指尖
      { x: 0.5, y: 0.4, z: 0.0 }, // 食指关节
      // ... 其他关键点
    ];
  }

  /**
   * 生成抓取手势关键点
   */
  private generateGrabGestureKeyPoints(): any[] {
    // 简化的抓取手势关键点
    return [
      { x: 0.5, y: 0.5, z: 0.0 }, // 手掌中心
      { x: 0.4, y: 0.4, z: 0.0 }, // 拇指
      // ... 其他关键点
    ];
  }

  /**
   * 生成挥手手势序列
   */
  private generateWaveGestureSequence(): any[] {
    // 简化的挥手手势序列
    return [
      { frame: 0, keyPoints: [{ x: 0.3, y: 0.5, z: 0.0 }] },
      { frame: 10, keyPoints: [{ x: 0.7, y: 0.5, z: 0.0 }] },
      { frame: 20, keyPoints: [{ x: 0.3, y: 0.5, z: 0.0 }] },
    ];
  }
}
