/**
 * 文档队列处理器
 * 处理文档上传和解析任务
 */
import { Processor, Process } from '@nestjs/bull';
import { Job } from 'bull';
import { Logger } from '@nestjs/common';
import { DocumentsService } from '../documents.service';

export interface DocumentProcessJob {
  documentId: string;
  filePath: string;
}

@Processor('document-processing')
export class DocumentQueueProcessor {
  private readonly logger = new Logger(DocumentQueueProcessor.name);

  constructor(private readonly documentsService: DocumentsService) {}

  @Process('process-document')
  async handleDocumentProcessing(job: Job<DocumentProcessJob>) {
    const { documentId, filePath } = job.data;
    
    this.logger.log(`开始处理文档: ${documentId}`);
    
    try {
      await this.documentsService.processDocument(documentId);
      this.logger.log(`文档处理完成: ${documentId}`);
    } catch (error) {
      this.logger.error(`文档处理失败: ${documentId}`, error.stack);
      throw error;
    }
  }

  @Process('reprocess-document')
  async handleDocumentReprocessing(job: Job<DocumentProcessJob>) {
    const { documentId } = job.data;
    
    this.logger.log(`开始重新处理文档: ${documentId}`);
    
    try {
      await this.documentsService.processDocument(documentId);
      this.logger.log(`文档重新处理完成: ${documentId}`);
    } catch (error) {
      this.logger.error(`文档重新处理失败: ${documentId}`, error.stack);
      throw error;
    }
  }

  @Process('batch-process')
  async handleBatchProcessing(job: Job<{ documentIds: string[] }>) {
    const { documentIds } = job.data;
    
    this.logger.log(`开始批量处理文档: ${documentIds.length} 个文档`);
    
    const results = [];
    
    for (const documentId of documentIds) {
      try {
        await this.documentsService.processDocument(documentId);
        results.push({ documentId, status: 'success' });
        this.logger.log(`文档处理成功: ${documentId}`);
      } catch (error) {
        results.push({ documentId, status: 'failed', error: error.message });
        this.logger.error(`文档处理失败: ${documentId}`, error.stack);
      }
    }
    
    this.logger.log(`批量处理完成: ${results.length} 个文档`);
    return results;
  }
}
