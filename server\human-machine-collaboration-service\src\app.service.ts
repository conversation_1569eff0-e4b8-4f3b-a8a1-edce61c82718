import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';

/**
 * 应用主服务
 * 提供系统级别的功能和定时任务
 */
@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * 获取服务健康状态
   */
  getHealth(): any {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'human-machine-collaboration-service',
      version: '1.0.0',
      environment: this.configService.get('NODE_ENV'),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      features: {
        arvrGuidance: true,
        aiAssistant: true,
        voiceInteraction: true,
        gestureRecognition: true,
        realTimeCollaboration: true,
      },
    };
  }

  /**
   * 获取服务信息
   */
  getServiceInfo(): any {
    return {
      name: '人机协作增强服务',
      description: 'AR/VR维护指导、智能助手、语音交互、手势识别',
      version: '1.0.0',
      author: 'DL Engine Team',
      features: [
        {
          name: 'AR/VR维护指导',
          description: '沉浸式维护指导场景，实时3D可视化指导',
          endpoints: ['/api/v1/arvr/scenes', '/api/v1/arvr/sessions'],
        },
        {
          name: '智能助手',
          description: '自然语言理解，智能问答系统',
          endpoints: ['/api/v1/ai/chat', '/api/v1/ai/knowledge'],
        },
        {
          name: '语音交互',
          description: '语音识别和合成，多语言支持',
          endpoints: ['/api/v1/voice/recognize', '/api/v1/voice/synthesize'],
        },
        {
          name: '手势识别',
          description: '实时手势捕捉，动作识别和分析',
          endpoints: ['/api/v1/gesture/recognize', '/api/v1/gesture/patterns'],
        },
      ],
      documentation: '/api/docs',
      websocket: {
        namespace: '/collaboration',
        events: ['session-update', 'interaction', 'feedback'],
      },
    };
  }

  /**
   * 系统统计信息
   */
  async getSystemStats(): Promise<any> {
    try {
      // 这里可以添加更多系统统计逻辑
      return {
        timestamp: new Date().toISOString(),
        system: {
          platform: process.platform,
          arch: process.arch,
          nodeVersion: process.version,
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
        },
        service: {
          environment: this.configService.get('NODE_ENV'),
          port: this.configService.get('PORT'),
          database: {
            host: this.configService.get('DB_HOST'),
            port: this.configService.get('DB_PORT'),
            database: this.configService.get('DB_DATABASE'),
          },
          redis: {
            host: this.configService.get('REDIS_HOST'),
            port: this.configService.get('REDIS_PORT'),
          },
        },
        features: {
          arvrEnabled: true,
          aiEnabled: true,
          voiceEnabled: true,
          gestureEnabled: true,
          websocketEnabled: true,
        },
      };
    } catch (error) {
      this.logger.error('获取系统统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 定时清理任务 - 每天凌晨2点执行
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async handleDailyCleanup(): Promise<void> {
    try {
      this.logger.log('开始执行每日清理任务...');
      
      // 清理过期的会话数据
      await this.cleanupExpiredSessions();
      
      // 清理临时文件
      await this.cleanupTempFiles();
      
      // 清理日志文件
      await this.cleanupOldLogs();
      
      this.logger.log('每日清理任务完成');
    } catch (error) {
      this.logger.error('每日清理任务失败:', error);
    }
  }

  /**
   * 定时性能监控 - 每5分钟执行
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async handlePerformanceMonitoring(): Promise<void> {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      // 记录性能指标
      this.logger.debug('性能监控:', {
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024) + 'MB',
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + 'MB',
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + 'MB',
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system,
        },
        uptime: Math.round(process.uptime()) + 's',
      });
      
      // 检查内存使用是否过高
      if (memoryUsage.heapUsed > 500 * 1024 * 1024) { // 500MB
        this.logger.warn('内存使用过高:', Math.round(memoryUsage.heapUsed / 1024 / 1024) + 'MB');
      }
      
    } catch (error) {
      this.logger.error('性能监控失败:', error);
    }
  }

  /**
   * 清理过期会话
   */
  private async cleanupExpiredSessions(): Promise<void> {
    // 实现会话清理逻辑
    this.logger.debug('清理过期会话...');
  }

  /**
   * 清理临时文件
   */
  private async cleanupTempFiles(): Promise<void> {
    // 实现临时文件清理逻辑
    this.logger.debug('清理临时文件...');
  }

  /**
   * 清理旧日志文件
   */
  private async cleanupOldLogs(): Promise<void> {
    // 实现日志文件清理逻辑
    this.logger.debug('清理旧日志文件...');
  }
}
