import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ResponseDto } from '../dto/response.dto';

/**
 * HTTP异常过滤器
 */
@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();

    const exceptionResponse = exception.getResponse();
    const message = typeof exceptionResponse === 'string' 
      ? exceptionResponse 
      : (exceptionResponse as any).message || exception.message;

    // 记录错误日志
    this.logger.error(
      `HTTP异常: ${status} - ${message}`,
      exception.stack,
      `${request.method} ${request.url}`,
    );

    // 构建错误响应
    const errorResponse = new ResponseDto(
      status,
      Array.isArray(message) ? message.join(', ') : message,
      null,
      request.url,
    );

    response.status(status).json(errorResponse);
  }
}

/**
 * 全局异常过滤器
 */
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = '服务器内部错误';

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      message = typeof exceptionResponse === 'string' 
        ? exceptionResponse 
        : (exceptionResponse as any).message || exception.message;
    } else if (exception instanceof Error) {
      message = exception.message;
    }

    // 记录错误日志
    this.logger.error(
      `未处理异常: ${status} - ${message}`,
      exception instanceof Error ? exception.stack : String(exception),
      `${request.method} ${request.url}`,
    );

    // 构建错误响应
    const errorResponse = new ResponseDto(
      status,
      Array.isArray(message) ? message.join(', ') : message,
      null,
      request.url,
    );

    response.status(status).json(errorResponse);
  }
}
