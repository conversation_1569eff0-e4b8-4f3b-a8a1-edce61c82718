import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';

// 控制器
import { OptimizationController } from './optimization.controller';

// 服务
import { OptimizationEngineService } from './optimization-engine.service';
import { AlgorithmService } from './algorithm.service';
import { PerformanceAnalysisService } from './performance-analysis.service';

// 队列处理器
import { OptimizationProcessor } from './processors/optimization.processor';

@Module({
  imports: [
    BullModule.registerQueue({
      name: 'optimization-engine',
    }),
  ],

  controllers: [OptimizationController],

  providers: [
    OptimizationEngineService,
    AlgorithmService,
    PerformanceAnalysisService,
    OptimizationProcessor,
  ],

  exports: [
    OptimizationEngineService,
    AlgorithmService,
    PerformanceAnalysisService,
  ],
})
export class OptimizationModule {}
