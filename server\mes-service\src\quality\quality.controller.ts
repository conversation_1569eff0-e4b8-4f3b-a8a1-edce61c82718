import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { QualityService } from './quality.service';
import { QualityInspection, QualityControlPlan, InspectionResult } from './entities/quality-inspection.entity';

/**
 * 质量管理控制器
 */
@ApiTags('quality')
@Controller('quality')
@ApiBearerAuth()
export class QualityController {
  private readonly logger = new Logger(QualityController.name);

  constructor(private readonly qualityService: QualityService) {}

  /**
   * 创建质量检验
   */
  @Post('inspections')
  @ApiOperation({ summary: '创建质量检验', description: '创建新的质量检验任务' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '检验创建成功',
    type: QualityInspection,
  })
  async createInspection(@Body() createInspectionDto: any): Promise<QualityInspection> {
    this.logger.log(`创建质量检验: ${createInspectionDto.inspectionNumber}`);
    return await this.qualityService.createInspection(createInspectionDto);
  }

  /**
   * 获取检验列表
   */
  @Get('inspections')
  @ApiOperation({ summary: '获取检验列表', description: '分页查询质量检验列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
  })
  async getInspections(@Query() query: any) {
    this.logger.log(`查询检验列表: 页码=${query.page}, 限制=${query.limit}`);
    return await this.qualityService.getInspections(query);
  }

  /**
   * 获取检验详情
   */
  @Get('inspections/:id')
  @ApiOperation({ summary: '获取检验详情', description: '根据ID获取质量检验详细信息' })
  @ApiParam({ name: 'id', description: '检验ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    type: QualityInspection,
  })
  async getInspectionById(@Param('id') id: string): Promise<QualityInspection> {
    this.logger.log(`获取检验详情: ${id}`);
    return await this.qualityService.getInspectionById(id);
  }

  /**
   * 更新检验
   */
  @Put('inspections/:id')
  @ApiOperation({ summary: '更新检验', description: '更新质量检验信息' })
  @ApiParam({ name: 'id', description: '检验ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '更新成功',
    type: QualityInspection,
  })
  async updateInspection(
    @Param('id') id: string,
    @Body() updateInspectionDto: any,
  ): Promise<QualityInspection> {
    this.logger.log(`更新检验: ${id}`);
    return await this.qualityService.updateInspection(id, updateInspectionDto);
  }

  /**
   * 删除检验
   */
  @Delete('inspections/:id')
  @ApiOperation({ summary: '删除检验', description: '删除质量检验' })
  @ApiParam({ name: 'id', description: '检验ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '删除成功',
  })
  async deleteInspection(@Param('id') id: string): Promise<void> {
    this.logger.log(`删除检验: ${id}`);
    await this.qualityService.deleteInspection(id);
  }

  /**
   * 开始检验
   */
  @Post('inspections/:id/start')
  @ApiOperation({ summary: '开始检验', description: '开始执行质量检验' })
  @ApiParam({ name: 'id', description: '检验ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '开始成功',
    type: QualityInspection,
  })
  async startInspection(@Param('id') id: string): Promise<QualityInspection> {
    this.logger.log(`开始检验: ${id}`);
    return await this.qualityService.startInspection(id);
  }

  /**
   * 完成检验
   */
  @Post('inspections/:id/complete')
  @ApiOperation({ summary: '完成检验', description: '完成质量检验并记录结果' })
  @ApiParam({ name: 'id', description: '检验ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '完成成功',
    type: QualityInspection,
  })
  async completeInspection(
    @Param('id') id: string,
    @Body() body: { result: InspectionResult },
  ): Promise<QualityInspection> {
    this.logger.log(`完成检验: ${id}, 结果: ${body.result}`);
    return await this.qualityService.completeInspection(id, body.result);
  }

  /**
   * 获取质量统计
   */
  @Get('stats/overview')
  @ApiOperation({ summary: '获取质量统计', description: '获取质量管理统计数据' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
  })
  async getQualityStatistics() {
    this.logger.log('获取质量统计数据');
    return await this.qualityService.getQualityStatistics();
  }

  /**
   * 创建质控计划
   */
  @Post('control-plans')
  @ApiOperation({ summary: '创建质控计划', description: '创建新的质量控制计划' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '计划创建成功',
    type: QualityControlPlan,
  })
  async createControlPlan(@Body() createPlanDto: any): Promise<QualityControlPlan> {
    this.logger.log(`创建质控计划: ${createPlanDto.planNumber}`);
    return await this.qualityService.createControlPlan(createPlanDto);
  }

  /**
   * 获取质控计划列表
   */
  @Get('control-plans')
  @ApiOperation({ summary: '获取质控计划列表', description: '分页查询质量控制计划列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
  })
  async getControlPlans(@Query() query: any) {
    this.logger.log(`查询质控计划列表: 页码=${query.page}, 限制=${query.limit}`);
    return await this.qualityService.getControlPlans(query);
  }
}
