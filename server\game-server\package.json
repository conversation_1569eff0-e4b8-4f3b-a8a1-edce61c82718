{"name": "game-server", "version": "1.0.0", "description": "DL（Digital Learning）引擎游戏服务器", "author": "IR Engine Team", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@google-cloud/agones-sdk": "^1.30.0", "@nestjs/common": "^9.4.3", "@nestjs/config": "^2.3.4", "@nestjs/core": "^9.4.3", "@nestjs/event-emitter": "^1.4.2", "@nestjs/microservices": "^9.4.3", "@nestjs/platform-express": "^9.4.3", "@nestjs/platform-socket.io": "^9.4.3", "@nestjs/schedule": "^2.2.3", "@nestjs/websockets": "^9.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "ioredis": "^5.3.2", "pg": "^8.11.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.2", "uuid": "^9.0.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "ws": "^8.13.0"}, "devDependencies": {"@nestjs/cli": "^9.5.0", "@nestjs/schematics": "^9.2.0", "@nestjs/testing": "^9.4.3", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/pg": "^8.10.2", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.1", "@types/winston": "^2.4.4", "@types/ws": "^8.5.5", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}