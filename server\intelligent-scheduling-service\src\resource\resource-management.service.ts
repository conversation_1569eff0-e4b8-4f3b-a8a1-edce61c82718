import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions } from 'typeorm';
import { Resource, ResourceStatus } from './entities/resource.entity';
import { ResourceAllocation } from './entities/resource-allocation.entity';

/**
 * 资源管理服务
 */
@Injectable()
export class ResourceManagementService {
  private readonly logger = new Logger(ResourceManagementService.name);

  constructor(
    @InjectRepository(Resource)
    private readonly resourceRepository: Repository<Resource>,
    @InjectRepository(ResourceAllocation)
    private readonly allocationRepository: Repository<ResourceAllocation>,
  ) {}

  /**
   * 获取资源列表
   */
  async getResources(options: {
    type?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ resources: Resource[]; total: number }> {
    try {
      const { type, status, limit = 20, offset = 0 } = options;

      const queryOptions: FindManyOptions<Resource> = {
        take: limit,
        skip: offset,
        order: { createdAt: 'DESC' },
        relations: ['allocations'],
      };

      if (type || status) {
        queryOptions.where = {};
        if (type) {
          queryOptions.where.type = type as any;
        }
        if (status) {
          queryOptions.where.status = status as ResourceStatus;
        }
      }

      const [resources, total] = await this.resourceRepository.findAndCount(queryOptions);

      return { resources, total };
    } catch (error) {
      this.logger.error(`获取资源列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据ID获取资源
   */
  async getResourceById(id: string): Promise<Resource> {
    try {
      const resource = await this.resourceRepository.findOne({
        where: { id },
        relations: ['allocations'],
      });

      if (!resource) {
        throw new NotFoundException(`资源不存在: ${id}`);
      }

      return resource;
    } catch (error) {
      this.logger.error(`获取资源失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 检查资源可用性
   */
  async checkResourceAvailability(
    resourceId: string,
    startTime: Date,
    endTime: Date,
    requiredQuantity: number = 1,
  ): Promise<{ available: boolean; conflicts: any[] }> {
    try {
      const resource = await this.getResourceById(resourceId);
      
      if (resource.status !== ResourceStatus.AVAILABLE) {
        return {
          available: false,
          conflicts: [{
            type: 'status',
            message: `资源状态为: ${resource.status}`,
          }],
        };
      }

      // 检查时间段内的分配冲突
      const conflictingAllocations = await this.allocationRepository.find({
        where: {
          resourceId,
          status: 'active' as any,
        },
      });

      const conflicts = conflictingAllocations.filter(allocation => {
        return (
          (startTime >= allocation.startTime && startTime < allocation.endTime) ||
          (endTime > allocation.startTime && endTime <= allocation.endTime) ||
          (startTime <= allocation.startTime && endTime >= allocation.endTime)
        );
      });

      // 检查容量是否足够
      const totalAllocated = conflicts.reduce(
        (sum, allocation) => sum + allocation.allocatedQuantity,
        0
      );

      const availableQuantity = resource.availableCapacity - totalAllocated;
      const hasCapacity = availableQuantity >= requiredQuantity;

      return {
        available: conflicts.length === 0 && hasCapacity,
        conflicts: conflicts.map(allocation => ({
          type: 'time_conflict',
          allocationId: allocation.id,
          taskId: allocation.taskId,
          startTime: allocation.startTime,
          endTime: allocation.endTime,
          quantity: allocation.allocatedQuantity,
        })),
      };
    } catch (error) {
      this.logger.error(`检查资源可用性失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 分配资源
   */
  async allocateResource(allocationData: {
    resourceId: string;
    taskId: string;
    startTime: Date;
    endTime: Date;
    quantity: number;
    solutionId?: string;
  }): Promise<ResourceAllocation> {
    try {
      const { resourceId, taskId, startTime, endTime, quantity, solutionId } = allocationData;

      // 检查资源可用性
      const availability = await this.checkResourceAvailability(
        resourceId,
        startTime,
        endTime,
        quantity
      );

      if (!availability.available) {
        throw new Error(`资源分配失败: 资源不可用`);
      }

      const allocation = this.allocationRepository.create({
        resourceId,
        taskId,
        startTime,
        endTime,
        allocatedQuantity: quantity,
        solutionId,
        status: 'planned' as any,
      });

      const savedAllocation = await this.allocationRepository.save(allocation);
      this.logger.log(`资源分配成功: ${resourceId} -> ${taskId}`);

      return savedAllocation;
    } catch (error) {
      this.logger.error(`分配资源失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 释放资源分配
   */
  async releaseResourceAllocation(allocationId: string): Promise<void> {
    try {
      const allocation = await this.allocationRepository.findOne({
        where: { id: allocationId },
      });

      if (!allocation) {
        throw new NotFoundException(`资源分配不存在: ${allocationId}`);
      }

      allocation.status = 'cancelled' as any;
      await this.allocationRepository.save(allocation);

      this.logger.log(`资源分配已释放: ${allocationId}`);
    } catch (error) {
      this.logger.error(`释放资源分配失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取资源分配历史
   */
  async getResourceAllocationHistory(
    resourceId: string,
    limit: number = 50,
  ): Promise<ResourceAllocation[]> {
    try {
      return await this.allocationRepository.find({
        where: { resourceId },
        order: { createdAt: 'DESC' },
        take: limit,
      });
    } catch (error) {
      this.logger.error(`获取资源分配历史失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
