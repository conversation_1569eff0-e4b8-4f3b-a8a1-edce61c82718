import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

/**
 * 数据采集任务实体
 */
@Entity('collection_tasks')
@Index(['deviceId'])
@Index(['enabled'])
export class CollectionTask {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 200 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'device_id', type: 'uuid' })
  deviceId: string;

  @Column({ name: 'device_config', type: 'text' })
  deviceConfig: string;

  @Column({ name: 'tag_ids', type: 'text' })
  tagIds: string;

  @Column({ type: 'int', default: 10 })
  interval: number;

  @Column({ type: 'boolean', default: true })
  enabled: boolean;

  @Column({ name: 'last_execution', type: 'timestamp', nullable: true })
  lastExecution: Date;

  @Column({ name: 'success_count', type: 'int', default: 0 })
  successCount: number;

  @Column({ name: 'error_count', type: 'int', default: 0 })
  errorCount: number;

  @Column({ name: 'last_error', type: 'text', nullable: true })
  lastError: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
