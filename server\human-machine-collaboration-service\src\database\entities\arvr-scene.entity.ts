import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { UserSession } from './user-session.entity';

/**
 * AR/VR场景实体
 * 存储AR/VR维护指导场景的配置和内容
 */
@Entity('arvr_scenes')
@Index(['contentType', 'equipmentModel'])
@Index(['difficulty', 'estimatedDuration'])
export class ARVRScene {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100 })
  name: string;

  @Column({
    type: 'enum',
    enum: [
      'maintenance_guide',
      'assembly_instruction',
      'safety_warning',
      'operation_manual',
      'training_simulation',
      'quality_inspection',
      'troubleshooting',
    ],
    default: 'maintenance_guide',
  })
  contentType: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  deviceId: string;

  @Column({ type: 'varchar', length: 100 })
  equipmentModel: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'json' })
  steps: any[];

  @Column({ type: 'json' })
  assets: any[];

  @Column({ type: 'json' })
  interactionModes: string[];

  @Column({
    type: 'enum',
    enum: ['beginner', 'intermediate', 'advanced'],
    default: 'intermediate',
  })
  difficulty: string;

  @Column({ type: 'int', comment: '预计时长（分钟）' })
  estimatedDuration: number;

  @Column({ type: 'json', nullable: true })
  prerequisites: string[];

  @Column({ type: 'json', nullable: true })
  safetyRequirements: string[];

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'varchar', length: 50, nullable: true })
  createdBy: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  updatedBy: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => UserSession, (session) => session.scene)
  sessions: UserSession[];
}
