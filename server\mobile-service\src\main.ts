/**
 * 移动端服务启动入口
 * 
 * 提供移动设备专用的数据同步和实时通信功能
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { Transport } from '@nestjs/microservices';
import * as compression from 'compression';
import * as helmet from 'helmet';
import { AppModule } from './app.module';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';

async function bootstrap() {
  // 创建Nest应用实例
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  
  // 配置微服务
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: configService.get<string>('MOBILE_SERVICE_HOST', 'localhost'),
      port: configService.get<number>('MOBILE_SERVICE_PORT', 3009),
    },
  });
  
  // 配置HTTP服务
  // 全局前缀
  app.setGlobalPrefix('api');
  
  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );
  
  // 全局过滤器
  app.useGlobalFilters(new GlobalExceptionFilter());
  
  // 全局拦截器
  app.useGlobalInterceptors(new LoggingInterceptor());
  
  // 启用CORS
  const corsEnabled = configService.get<boolean>('CORS_ENABLED', true);
  if (corsEnabled) {
    app.enableCors({
      origin: configService.get<string>('WEBSOCKET_CORS_ORIGIN', '*'),
      credentials: true,
    });
  }
  
  // 启用压缩
  app.use(compression());
  
  // 启用安全头
  app.use(helmet.default({
    contentSecurityPolicy: false, // 为了支持Swagger UI
  }));
  
  // Swagger文档
  const config = new DocumentBuilder()
    .setTitle('移动端服务API')
    .setDescription('移动设备专用的数据同步和实时通信API')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('移动端同步', '移动设备数据同步相关接口')
    .addTag('设备管理', '移动设备管理相关接口')
    .addTag('实时通信', 'WebSocket实时通信相关接口')
    .addTag('健康检查', '服务健康状态检查接口')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);
  
  // 启动微服务
  await app.startAllMicroservices();
  console.log('移动端微服务已启动');
  
  // 启动HTTP服务
  const port = configService.get<number>('PORT', 3009);
  await app.listen(port);
  console.log(`移动端服务已启动，端口: ${port}`);
  console.log(`API文档地址: http://localhost:${port}/api/docs`);
  console.log(`健康检查: http://localhost:${port}/api/v1/health`);
}

bootstrap().catch((error) => {
  console.error('启动移动端服务失败:', error);
  process.exit(1);
});
