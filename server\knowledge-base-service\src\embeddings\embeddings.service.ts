import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { pipeline, Pipeline } from '@xenova/transformers';

export interface EmbeddingOptions {
  model?: string;
  maxLength?: number;
  normalize?: boolean;
}

@Injectable()
export class EmbeddingsService {
  private embeddingPipeline: Pipeline;
  private isInitialized = false;
  private readonly defaultModel = 'Xenova/multilingual-e5-large';

  constructor(private configService: ConfigService) {
    this.initializeEmbeddingModel();
  }

  /**
   * 初始化嵌入模型
   */
  private async initializeEmbeddingModel(): Promise<void> {
    try {
      const modelName = this.configService.get<string>('EMBEDDING_MODEL', this.defaultModel);
      
      console.log(`正在加载嵌入模型: ${modelName}`);
      
      this.embeddingPipeline = await pipeline('feature-extraction', modelName, {
        quantized: true, // 使用量化模型以减少内存使用
      }) as any;
      
      this.isInitialized = true;
      console.log('嵌入模型加载完成');
    } catch (error) {
      console.error('嵌入模型加载失败:', error);
      throw new BadRequestException(`嵌入模型初始化失败: ${error.message}`);
    }
  }

  /**
   * 等待模型初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initializeEmbeddingModel();
    }
  }

  /**
   * 生成单个文本的嵌入
   */
  async generateEmbedding(text: string, options: EmbeddingOptions = {}): Promise<number[]> {
    await this.ensureInitialized();

    try {
      // 预处理文本
      const processedText = this.preprocessText(text, options.maxLength);
      
      // 生成嵌入
      const output = await this.embeddingPipeline(processedText, {
        pooling: 'mean',
        normalize: options.normalize !== false,
      });

      // 提取嵌入向量
      const embedding = Array.from(output.data) as number[];

      return embedding;
    } catch (error) {
      throw new BadRequestException(`生成嵌入失败: ${error.message}`);
    }
  }

  /**
   * 批量生成嵌入
   */
  async generateBatchEmbeddings(
    texts: string[],
    options: EmbeddingOptions = {},
  ): Promise<number[][]> {
    await this.ensureInitialized();

    try {
      const batchSize = this.configService.get<number>('EMBEDDING_BATCH_SIZE', 32);
      const embeddings: number[][] = [];

      // 分批处理以避免内存溢出
      for (let i = 0; i < texts.length; i += batchSize) {
        const batch = texts.slice(i, i + batchSize);
        const batchEmbeddings = await this.processBatch(batch, options);
        embeddings.push(...batchEmbeddings);
      }

      return embeddings;
    } catch (error) {
      throw new BadRequestException(`批量生成嵌入失败: ${error.message}`);
    }
  }

  /**
   * 处理批次
   */
  private async processBatch(
    texts: string[],
    options: EmbeddingOptions,
  ): Promise<number[][]> {
    const processedTexts = texts.map(text => this.preprocessText(text, options.maxLength));
    
    const outputs = await Promise.all(
      processedTexts.map(text =>
        this.embeddingPipeline(text, {
          pooling: 'mean',
          normalize: options.normalize !== false,
        }),
      ),
    );

    return outputs.map(output => Array.from(output.data));
  }

  /**
   * 预处理文本
   */
  private preprocessText(text: string, maxLength?: number): string {
    // 清理文本
    let processed = text
      .replace(/\s+/g, ' ')
      .trim();

    // 限制长度
    const limit = maxLength || this.configService.get<number>('MAX_TEXT_LENGTH', 512);
    if (processed.length > limit) {
      processed = processed.substring(0, limit);
    }

    return processed;
  }

  /**
   * 计算文本相似度
   */
  async calculateSimilarity(text1: string, text2: string): Promise<number> {
    const [embedding1, embedding2] = await Promise.all([
      this.generateEmbedding(text1),
      this.generateEmbedding(text2),
    ]);

    return this.cosineSimilarity(embedding1, embedding2);
  }

  /**
   * 计算余弦相似度
   */
  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      throw new Error('向量维度不匹配');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  /**
   * 获取嵌入维度
   */
  async getEmbeddingDimension(): Promise<number> {
    await this.ensureInitialized();
    
    // 使用测试文本获取维度
    const testEmbedding = await this.generateEmbedding('test');
    return testEmbedding.length;
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.ensureInitialized();
      
      // 生成测试嵌入
      const testEmbedding = await this.generateEmbedding('健康检查测试');
      
      return testEmbedding.length > 0;
    } catch (error) {
      console.error('嵌入服务健康检查失败:', error);
      return false;
    }
  }

  /**
   * 获取模型信息
   */
  getModelInfo(): {
    model: string;
    isInitialized: boolean;
    defaultDimension: number;
  } {
    return {
      model: this.configService.get<string>('EMBEDDING_MODEL', this.defaultModel),
      isInitialized: this.isInitialized,
      defaultDimension: 1024, // multilingual-e5-large的默认维度
    };
  }

  /**
   * 重新加载模型
   */
  async reloadModel(modelName?: string): Promise<void> {
    this.isInitialized = false;
    
    if (modelName) {
      // 临时更新模型名称
      process.env.EMBEDDING_MODEL = modelName;
    }
    
    await this.initializeEmbeddingModel();
  }
}
