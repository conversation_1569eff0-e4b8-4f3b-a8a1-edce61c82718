import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';

import { SupplyChainOptimizationService } from './supply-chain-optimization.service';
import { CollaborationService } from './collaboration.service';
import { SupplyChainAnalyticsService } from './supply-chain-analytics.service';

/**
 * 供应链控制器
 */
@ApiTags('supply-chain')
@Controller('supply-chain')
export class SupplyChainController {
  constructor(
    private readonly optimizationService: SupplyChainOptimizationService,
    private readonly collaborationService: CollaborationService,
    private readonly analyticsService: SupplyChainAnalyticsService,
  ) {}

  /**
   * 获取供应链网络分析
   */
  @Get('network/analysis')
  @ApiOperation({ summary: '获取供应链网络分析' })
  @ApiResponse({ status: 200, description: '网络分析结果' })
  async getNetworkAnalysis(): Promise<any> {
    const analysis = await this.analyticsService.analyzeSupplyChainNetwork();

    return {
      success: true,
      message: '获取供应链网络分析成功',
      data: analysis,
    };
  }

  /**
   * 供应链优化建议
   */
  @Post('optimization/suggestions')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '获取供应链优化建议' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        scope: { type: 'string' },
        objectives: { type: 'array', items: { type: 'string' } },
        constraints: { type: 'object' },
      },
    },
  })
  @ApiResponse({ status: 200, description: '优化建议' })
  async getOptimizationSuggestions(@Body() request: any): Promise<any> {
    const suggestions = await this.optimizationService.generateOptimizationSuggestions(request);

    return {
      success: true,
      message: '获取优化建议成功',
      data: suggestions,
    };
  }

  /**
   * 风险评估
   */
  @Get('risk/assessment')
  @ApiOperation({ summary: '供应链风险评估' })
  @ApiQuery({ name: 'scope', required: false, type: String })
  @ApiResponse({ status: 200, description: '风险评估结果' })
  async getRiskAssessment(
    @Query('scope') scope: string = 'all',
  ): Promise<any> {
    const assessment = await this.analyticsService.assessSupplyChainRisks(scope);

    return {
      success: true,
      message: '风险评估完成',
      data: assessment,
    };
  }

  /**
   * 创建协同计划
   */
  @Post('collaboration/plans')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '创建供应链协同计划' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        type: { type: 'string' },
        participatingNodes: { type: 'array' },
        objectives: { type: 'array' },
        timeframe: { type: 'object' },
      },
    },
  })
  @ApiResponse({ status: 201, description: '协同计划创建成功' })
  async createCollaborationPlan(@Body() request: any): Promise<any> {
    const plan = await this.collaborationService.createCollaborationPlan(request);

    return {
      success: true,
      message: '协同计划创建成功',
      data: plan,
    };
  }

  /**
   * 获取协同计划列表
   */
  @Get('collaboration/plans')
  @ApiOperation({ summary: '获取协同计划列表' })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'type', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'offset', required: false, type: Number })
  @ApiResponse({ status: 200, description: '协同计划列表' })
  async getCollaborationPlans(
    @Query('status') status?: string,
    @Query('type') type?: string,
    @Query('limit') limit: number = 20,
    @Query('offset') offset: number = 0,
  ): Promise<any> {
    const plans = await this.collaborationService.getCollaborationPlans({
      status,
      type,
      limit,
      offset,
    });

    return {
      success: true,
      message: '获取协同计划列表成功',
      data: plans,
    };
  }

  /**
   * 获取协同计划详情
   */
  @Get('collaboration/plans/:id')
  @ApiOperation({ summary: '获取协同计划详情' })
  @ApiParam({ name: 'id', description: '计划ID' })
  @ApiResponse({ status: 200, description: '协同计划详情' })
  async getCollaborationPlan(@Param('id', ParseUUIDPipe) id: string): Promise<any> {
    const plan = await this.collaborationService.getCollaborationPlanById(id);

    return {
      success: true,
      message: '获取协同计划详情成功',
      data: plan,
    };
  }

  /**
   * 供应商绩效分析
   */
  @Get('suppliers/performance')
  @ApiOperation({ summary: '供应商绩效分析' })
  @ApiQuery({ name: 'period', required: false, type: String })
  @ApiResponse({ status: 200, description: '供应商绩效分析' })
  async getSupplierPerformance(
    @Query('period') period: string = 'quarter',
  ): Promise<any> {
    const performance = await this.analyticsService.analyzeSupplierPerformance(period);

    return {
      success: true,
      message: '获取供应商绩效分析成功',
      data: performance,
    };
  }

  /**
   * 库存优化建议
   */
  @Post('inventory/optimization')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '获取库存优化建议' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        nodes: { type: 'array', items: { type: 'string' } },
        items: { type: 'array', items: { type: 'string' } },
        objectives: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  @ApiResponse({ status: 200, description: '库存优化建议' })
  async getInventoryOptimization(@Body() request: any): Promise<any> {
    const optimization = await this.optimizationService.optimizeInventory(request);

    return {
      success: true,
      message: '获取库存优化建议成功',
      data: optimization,
    };
  }

  /**
   * 供应链可视化数据
   */
  @Get('visualization/network')
  @ApiOperation({ summary: '获取供应链网络可视化数据' })
  @ApiResponse({ status: 200, description: '可视化数据' })
  async getNetworkVisualization(): Promise<any> {
    const visualization = await this.analyticsService.generateNetworkVisualization();

    return {
      success: true,
      message: '获取可视化数据成功',
      data: visualization,
    };
  }
}
