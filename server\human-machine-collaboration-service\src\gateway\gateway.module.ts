import { Module } from '@nestjs/common';
import { CollaborationGateway } from './collaboration.gateway';
import { CollaborationModule } from '../collaboration/collaboration.module';
import { AIAssistantModule } from '../ai-assistant/ai-assistant.module';
import { VoiceInteractionModule } from '../voice-interaction/voice-interaction.module';
import { GestureRecognitionModule } from '../gesture-recognition/gesture-recognition.module';

/**
 * 网关模块
 * 提供WebSocket实时通信功能
 */
@Module({
  imports: [
    CollaborationModule,
    AIAssistantModule,
    VoiceInteractionModule,
    GestureRecognitionModule,
  ],
  providers: [
    CollaborationGateway,
  ],
  exports: [
    CollaborationGateway,
  ],
})
export class GatewayModule {}
