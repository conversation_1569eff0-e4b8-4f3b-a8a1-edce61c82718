import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { ScheduledTask } from './scheduled-task.entity';

/**
 * 优化算法枚举
 */
export enum OptimizationAlgorithm {
  GENETIC_ALGORITHM = 'genetic_algorithm',
  SIMULATED_ANNEALING = 'simulated_annealing',
  PARTICLE_SWARM = 'particle_swarm',
  ANT_COLONY = 'ant_colony',
  TABU_SEARCH = 'tabu_search',
  LINEAR_PROGRAMMING = 'linear_programming',
  CONSTRAINT_PROGRAMMING = 'constraint_programming',
  HEURISTIC = 'heuristic',
}

/**
 * 调度目标枚举
 */
export enum SchedulingObjective {
  MINIMIZE_MAKESPAN = 'minimize_makespan',
  MINIMIZE_COST = 'minimize_cost',
  MAXIMIZE_THROUGHPUT = 'maximize_throughput',
  MINIMIZE_ENERGY = 'minimize_energy',
  MAXIMIZE_UTILIZATION = 'maximize_utilization',
  MINIMIZE_TARDINESS = 'minimize_tardiness',
  BALANCE_WORKLOAD = 'balance_workload',
}

/**
 * 方案状态枚举
 */
export enum SolutionStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  CANCELLED = 'cancelled',
}

/**
 * 调度方案实体
 */
@Entity('scheduling_solutions')
@Index(['status', 'createdAt'])
@Index(['algorithm'])
@Index(['optimality'])
export class SchedulingSolution {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'solution_id', length: 100, unique: true })
  solutionId: string;

  @Column({ length: 200, nullable: true })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: OptimizationAlgorithm,
    default: OptimizationAlgorithm.HEURISTIC,
  })
  algorithm: OptimizationAlgorithm;

  @Column({
    type: 'enum',
    enum: SolutionStatus,
    default: SolutionStatus.DRAFT,
  })
  status: SolutionStatus;

  @Column({ type: 'json', comment: '优化目标' })
  objectives: {
    objective: SchedulingObjective;
    weight: number;
    value: number;
    normalized: number;
  }[];

  @Column({ type: 'decimal', precision: 10, scale: 2, comment: '完工时间(小时)' })
  makespan: number;

  @Column({ name: 'total_cost', type: 'decimal', precision: 12, scale: 2, comment: '总成本' })
  totalCost: number;

  @Column({ name: 'energy_consumption', type: 'decimal', precision: 10, scale: 2, comment: '能耗' })
  energyConsumption: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, comment: '资源利用率' })
  utilization: number;

  @Column({ type: 'boolean', default: true, comment: '是否可行' })
  feasible: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 4, comment: '最优性评分' })
  optimality: number;

  @Column({ name: 'task_count', type: 'int', comment: '任务数量' })
  taskCount: number;

  @Column({ name: 'resource_count', type: 'int', comment: '资源数量' })
  resourceCount: number;

  @Column({ name: 'constraint_count', type: 'int', comment: '约束数量' })
  constraintCount: number;

  @Column({ name: 'generation_time', type: 'int', comment: '生成时间(毫秒)' })
  generationTime: number;

  @Column({ name: 'optimization_iterations', type: 'int', nullable: true, comment: '优化迭代次数' })
  optimizationIterations: number;

  @Column({ name: 'resource_allocations', type: 'json', comment: '资源分配' })
  resourceAllocations: {
    resourceId: string;
    resourceType: string;
    allocations: {
      startTime: Date;
      endTime: Date;
      taskId: string;
      quantity: number;
    }[];
    utilization: number;
    conflicts: string[];
  }[];

  @Column({ name: 'performance_metrics', type: 'json', nullable: true, comment: '性能指标' })
  performanceMetrics: any;

  @Column({ name: 'validation_results', type: 'json', nullable: true, comment: '验证结果' })
  validationResults: any;

  @Column({ type: 'json', nullable: true, comment: '扩展属性' })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => ScheduledTask, scheduledTask => scheduledTask.solution)
  scheduledTasks: ScheduledTask[];
}
