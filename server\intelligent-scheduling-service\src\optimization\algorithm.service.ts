import { Injectable, Logger } from '@nestjs/common';

/**
 * 算法服务
 */
@Injectable()
export class AlgorithmService {
  private readonly logger = new Logger(AlgorithmService.name);

  /**
   * 获取可用算法列表
   */
  async getAvailableAlgorithms(): Promise<any> {
    return {
      algorithms: [
        {
          id: 'genetic_algorithm',
          name: '遗传算法',
          description: '基于自然选择和遗传机制的优化算法',
          complexity: 'high',
          suitableFor: ['复杂优化问题', '多目标优化', '大搜索空间'],
          parameters: {
            populationSize: { default: 100, range: [50, 500] },
            crossoverRate: { default: 0.8, range: [0.1, 1.0] },
            mutationRate: { default: 0.1, range: [0.001, 0.5] },
            maxGenerations: { default: 1000, range: [100, 5000] },
          },
          performance: {
            quality: 'high',
            speed: 'medium',
            scalability: 'good',
          },
        },
        {
          id: 'simulated_annealing',
          name: '模拟退火',
          description: '基于物理退火过程的优化算法',
          complexity: 'medium',
          suitableFor: ['局部搜索', '避免局部最优', '连续优化'],
          parameters: {
            initialTemperature: { default: 1000, range: [100, 10000] },
            coolingRate: { default: 0.95, range: [0.8, 0.99] },
            minTemperature: { default: 0.1, range: [0.01, 1.0] },
            maxIterations: { default: 10000, range: [1000, 50000] },
          },
          performance: {
            quality: 'high',
            speed: 'fast',
            scalability: 'medium',
          },
        },
        {
          id: 'particle_swarm',
          name: '粒子群优化',
          description: '基于鸟群觅食行为的群体智能算法',
          complexity: 'medium',
          suitableFor: ['连续优化', '多维搜索', '快速收敛'],
          parameters: {
            swarmSize: { default: 50, range: [20, 200] },
            inertiaWeight: { default: 0.9, range: [0.1, 1.0] },
            c1: { default: 2.0, range: [0.5, 4.0] },
            c2: { default: 2.0, range: [0.5, 4.0] },
            maxIterations: { default: 1000, range: [100, 5000] },
          },
          performance: {
            quality: 'medium',
            speed: 'fast',
            scalability: 'good',
          },
        },
        {
          id: 'ant_colony',
          name: '蚁群算法',
          description: '基于蚂蚁觅食行为的优化算法',
          complexity: 'medium',
          suitableFor: ['路径优化', '组合优化', '图论问题'],
          parameters: {
            antCount: { default: 50, range: [20, 200] },
            alpha: { default: 1.0, range: [0.1, 5.0] },
            beta: { default: 2.0, range: [0.1, 5.0] },
            evaporationRate: { default: 0.1, range: [0.01, 0.5] },
            maxIterations: { default: 1000, range: [100, 5000] },
          },
          performance: {
            quality: 'medium',
            speed: 'medium',
            scalability: 'good',
          },
        },
        {
          id: 'tabu_search',
          name: '禁忌搜索',
          description: '使用记忆结构避免循环的局部搜索算法',
          complexity: 'medium',
          suitableFor: ['组合优化', '避免循环', '局部搜索增强'],
          parameters: {
            tabuListSize: { default: 20, range: [5, 100] },
            maxIterations: { default: 1000, range: [100, 10000] },
            aspirationCriteria: { default: true, range: [true, false] },
          },
          performance: {
            quality: 'high',
            speed: 'fast',
            scalability: 'medium',
          },
        },
        {
          id: 'linear_programming',
          name: '线性规划',
          description: '求解线性目标函数和线性约束的优化方法',
          complexity: 'low',
          suitableFor: ['线性问题', '资源分配', '精确解'],
          parameters: {
            solver: { default: 'simplex', options: ['simplex', 'interior_point'] },
            tolerance: { default: 1e-6, range: [1e-10, 1e-3] },
          },
          performance: {
            quality: 'optimal',
            speed: 'very_fast',
            scalability: 'excellent',
          },
        },
        {
          id: 'constraint_programming',
          name: '约束规划',
          description: '基于约束满足的优化方法',
          complexity: 'high',
          suitableFor: ['复杂约束', '逻辑推理', '可行性问题'],
          parameters: {
            searchStrategy: { default: 'dfs', options: ['dfs', 'bfs', 'best_first'] },
            propagationLevel: { default: 'domain', options: ['bounds', 'domain', 'value'] },
            restartStrategy: { default: 'luby', options: ['none', 'luby', 'geometric'] },
          },
          performance: {
            quality: 'high',
            speed: 'variable',
            scalability: 'medium',
          },
        },
        {
          id: 'heuristic',
          name: '启发式算法',
          description: '基于经验规则的快速近似算法',
          complexity: 'low',
          suitableFor: ['快速求解', '大规模问题', '实时调度'],
          parameters: {
            strategy: { default: 'greedy', options: ['greedy', 'priority', 'earliest_due_date'] },
            lookahead: { default: 1, range: [1, 10] },
          },
          performance: {
            quality: 'medium',
            speed: 'very_fast',
            scalability: 'excellent',
          },
        },
      ],
      totalAlgorithms: 8,
      categories: {
        evolutionary: ['genetic_algorithm'],
        physics_based: ['simulated_annealing'],
        swarm_intelligence: ['particle_swarm', 'ant_colony'],
        local_search: ['tabu_search'],
        mathematical: ['linear_programming', 'constraint_programming'],
        heuristic: ['heuristic'],
      },
    };
  }

  /**
   * 比较算法性能
   */
  async compareAlgorithms(algorithms: string[], testData: any): Promise<any> {
    try {
      this.logger.log(`开始比较算法性能: ${algorithms.join(', ')}`);

      const results = [];

      for (const algorithmId of algorithms) {
        const result = await this.runAlgorithmBenchmark(algorithmId, testData);
        results.push(result);
      }

      // 计算排名
      const rankedResults = this.rankAlgorithmResults(results);

      return {
        testData: {
          taskCount: testData?.taskCount || 100,
          resourceCount: testData?.resourceCount || 20,
          complexity: testData?.complexity || 'medium',
        },
        results: rankedResults,
        summary: this.generateComparisonSummary(rankedResults),
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`算法性能比较失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 运行算法基准测试
   */
  private async runAlgorithmBenchmark(algorithmId: string, testData: any): Promise<any> {
    const startTime = Date.now();

    // 模拟算法执行
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));

    const executionTime = Date.now() - startTime;

    // 模拟性能指标
    const performance = this.generateMockPerformance(algorithmId, testData);

    return {
      algorithmId,
      algorithmName: this.getAlgorithmName(algorithmId),
      executionTime,
      performance,
      metrics: {
        convergenceRate: Math.random() * 0.5 + 0.5,
        memoryUsage: Math.random() * 100 + 50, // MB
        cpuUsage: Math.random() * 80 + 20, // %
      },
    };
  }

  /**
   * 生成模拟性能数据
   */
  private generateMockPerformance(algorithmId: string, testData: any): any {
    const baseQuality = {
      genetic_algorithm: 0.9,
      simulated_annealing: 0.85,
      particle_swarm: 0.8,
      ant_colony: 0.75,
      tabu_search: 0.82,
      linear_programming: 0.95,
      constraint_programming: 0.88,
      heuristic: 0.7,
    };

    const quality = baseQuality[algorithmId] || 0.7;
    const variation = (Math.random() - 0.5) * 0.2;

    return {
      quality: Math.max(0, Math.min(1, quality + variation)),
      makespan: Math.random() * 100 + 50,
      cost: Math.random() * 10000 + 5000,
      utilization: Math.random() * 0.4 + 0.6,
      feasible: Math.random() > 0.1,
    };
  }

  /**
   * 算法结果排名
   */
  private rankAlgorithmResults(results: any[]): any[] {
    return results
      .map((result, index) => ({
        ...result,
        overallScore: this.calculateOverallScore(result),
      }))
      .sort((a, b) => b.overallScore - a.overallScore)
      .map((result, index) => ({
        ...result,
        rank: index + 1,
      }));
  }

  /**
   * 计算综合评分
   */
  private calculateOverallScore(result: any): number {
    const weights = {
      quality: 0.4,
      speed: 0.3,
      efficiency: 0.3,
    };

    const speedScore = Math.max(0, 1 - result.executionTime / 5000); // 5秒为基准
    const efficiencyScore = 1 - result.metrics.memoryUsage / 200; // 200MB为基准

    return (
      result.performance.quality * weights.quality +
      speedScore * weights.speed +
      efficiencyScore * weights.efficiency
    );
  }

  /**
   * 生成比较总结
   */
  private generateComparisonSummary(results: any[]): any {
    const best = results[0];
    const fastest = results.reduce((prev, curr) => 
      curr.executionTime < prev.executionTime ? curr : prev
    );
    const mostAccurate = results.reduce((prev, curr) => 
      curr.performance.quality > prev.performance.quality ? curr : prev
    );

    return {
      bestOverall: {
        algorithm: best.algorithmName,
        score: best.overallScore,
      },
      fastest: {
        algorithm: fastest.algorithmName,
        time: fastest.executionTime,
      },
      mostAccurate: {
        algorithm: mostAccurate.algorithmName,
        quality: mostAccurate.performance.quality,
      },
      recommendations: this.generateRecommendations(results),
    };
  }

  /**
   * 生成推荐建议
   */
  private generateRecommendations(results: any[]): string[] {
    const recommendations = [];
    const best = results[0];

    recommendations.push(`推荐使用 ${best.algorithmName}，综合性能最佳`);

    const fastest = results.reduce((prev, curr) => 
      curr.executionTime < prev.executionTime ? curr : prev
    );

    if (fastest.algorithmId !== best.algorithmId) {
      recommendations.push(`如需快速求解，推荐使用 ${fastest.algorithmName}`);
    }

    return recommendations;
  }

  /**
   * 获取算法名称
   */
  private getAlgorithmName(algorithmId: string): string {
    const names = {
      genetic_algorithm: '遗传算法',
      simulated_annealing: '模拟退火',
      particle_swarm: '粒子群优化',
      ant_colony: '蚁群算法',
      tabu_search: '禁忌搜索',
      linear_programming: '线性规划',
      constraint_programming: '约束规划',
      heuristic: '启发式算法',
    };

    return names[algorithmId] || algorithmId;
  }
}
