import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EnergyConsumption } from './entities/energy-consumption.entity';

/**
 * 能耗管理服务
 */
@Injectable()
export class EnergyManagementService {
  private readonly logger = new Logger(EnergyManagementService.name);

  constructor(
    @InjectRepository(EnergyConsumption)
    private readonly consumptionRepository: Repository<EnergyConsumption>,
  ) {}

  /**
   * 获取能耗数据
   */
  async getEnergyConsumption(options: {
    deviceId?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  }): Promise<any> {
    try {
      const { deviceId, startDate, endDate, limit = 100 } = options;

      const queryBuilder = this.consumptionRepository.createQueryBuilder('consumption');

      if (deviceId) {
        queryBuilder.andWhere('consumption.deviceId = :deviceId', { deviceId });
      }

      if (startDate) {
        queryBuilder.andWhere('consumption.timestamp >= :startDate', { startDate });
      }

      if (endDate) {
        queryBuilder.andWhere('consumption.timestamp <= :endDate', { endDate });
      }

      const [data, total] = await queryBuilder
        .orderBy('consumption.timestamp', 'DESC')
        .take(limit)
        .getManyAndCount();

      return {
        data,
        total,
        summary: await this.calculateConsumptionSummary(data),
      };
    } catch (error) {
      this.logger.error(`获取能耗数据失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取能耗统计
   */
  async getEnergyStats(period: string): Promise<any> {
    try {
      const now = new Date();
      let startDate: Date;

      switch (period) {
        case 'hour':
          startDate = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case 'day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }

      const consumption = await this.consumptionRepository.find({
        where: {
          timestamp: {
            $gte: startDate,
          } as any,
        },
        order: { timestamp: 'ASC' },
      });

      return {
        period,
        startDate,
        endDate: now,
        totalConsumption: consumption.reduce((sum, c) => sum + Number(c.consumption), 0),
        averageConsumption: consumption.length > 0 
          ? consumption.reduce((sum, c) => sum + Number(c.consumption), 0) / consumption.length 
          : 0,
        peakConsumption: consumption.length > 0 
          ? Math.max(...consumption.map(c => Number(c.consumption))) 
          : 0,
        deviceCount: new Set(consumption.map(c => c.deviceId)).size,
        dataPoints: consumption.length,
        byEnergyType: this.groupByEnergyType(consumption),
        byDevice: this.groupByDevice(consumption),
        trends: this.calculateTrends(consumption),
      };
    } catch (error) {
      this.logger.error(`获取能耗统计失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 计算能耗摘要
   */
  private async calculateConsumptionSummary(data: EnergyConsumption[]): Promise<any> {
    if (data.length === 0) {
      return {
        totalConsumption: 0,
        averageConsumption: 0,
        peakConsumption: 0,
        totalCost: 0,
      };
    }

    const totalConsumption = data.reduce((sum, item) => sum + Number(item.consumption), 0);
    const totalCost = data.reduce((sum, item) => sum + Number(item.totalCost || 0), 0);
    const peakConsumption = Math.max(...data.map(item => Number(item.consumption)));

    return {
      totalConsumption,
      averageConsumption: totalConsumption / data.length,
      peakConsumption,
      totalCost,
      carbonFootprint: data.reduce((sum, item) => sum + Number(item.carbonFootprint || 0), 0),
    };
  }

  /**
   * 按能源类型分组
   */
  private groupByEnergyType(consumption: EnergyConsumption[]): any {
    const grouped = consumption.reduce((acc, item) => {
      const type = item.energyType;
      if (!acc[type]) {
        acc[type] = {
          totalConsumption: 0,
          count: 0,
          totalCost: 0,
        };
      }
      acc[type].totalConsumption += Number(item.consumption);
      acc[type].count += 1;
      acc[type].totalCost += Number(item.totalCost || 0);
      return acc;
    }, {});

    return grouped;
  }

  /**
   * 按设备分组
   */
  private groupByDevice(consumption: EnergyConsumption[]): any {
    const grouped = consumption.reduce((acc, item) => {
      const deviceId = item.deviceId;
      if (!acc[deviceId]) {
        acc[deviceId] = {
          deviceName: item.deviceName,
          totalConsumption: 0,
          count: 0,
          totalCost: 0,
        };
      }
      acc[deviceId].totalConsumption += Number(item.consumption);
      acc[deviceId].count += 1;
      acc[deviceId].totalCost += Number(item.totalCost || 0);
      return acc;
    }, {});

    return grouped;
  }

  /**
   * 计算趋势
   */
  private calculateTrends(consumption: EnergyConsumption[]): any {
    if (consumption.length < 2) {
      return { trend: 'stable', change: 0 };
    }

    const sortedData = consumption.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    const firstHalf = sortedData.slice(0, Math.floor(sortedData.length / 2));
    const secondHalf = sortedData.slice(Math.floor(sortedData.length / 2));

    const firstHalfAvg = firstHalf.reduce((sum, item) => sum + Number(item.consumption), 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, item) => sum + Number(item.consumption), 0) / secondHalf.length;

    const change = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100;

    return {
      trend: change > 5 ? 'increasing' : change < -5 ? 'decreasing' : 'stable',
      change: Math.round(change * 100) / 100,
      firstPeriodAvg: Math.round(firstHalfAvg * 100) / 100,
      secondPeriodAvg: Math.round(secondHalfAvg * 100) / 100,
    };
  }
}
