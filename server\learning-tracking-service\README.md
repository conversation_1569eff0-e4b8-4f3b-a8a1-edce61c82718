# 学习记录跟踪服务 (Learning Tracking Service)

## 概述

学习记录跟踪服务是一个基于NestJS的企业级微服务，专门用于学习数据的采集、分析和个性化推荐。该服务基于xAPI标准，提供完整的学习记录管理、用户画像分析和智能推荐功能。

## 主要功能

### 🎯 核心功能
- **xAPI数据采集** - 标准化学习数据收集和存储
- **学习记录管理** - 完整的学习轨迹跟踪和管理
- **用户画像分析** - 基于学习行为的智能用户画像构建
- **个性化推荐** - 智能学习内容和路径推荐
- **数据同步** - 与外部学习平台的数据同步
- **Learninglocker集成** - 与Learninglocker LRS的无缝集成

### 📊 分析功能
- **学习行为分析** - 深度学习行为模式分析
- **学习效果评估** - 多维度学习效果评估
- **学习路径优化** - 基于数据的学习路径优化建议
- **实时监控** - 学习过程实时监控和预警

### 🔧 管理功能
- **内容管理** - 学习内容元数据管理
- **推荐策略配置** - 灵活的推荐算法配置
- **数据质量监控** - 学习数据质量监控和清洗
- **性能优化** - 智能缓存和队列处理

## 技术栈

- **框架**: NestJS 10.x
- **语言**: TypeScript 5.x
- **数据库**: MySQL + TypeORM
- **缓存**: Redis
- **消息队列**: Bull + Redis
- **标准**: xAPI 2.0
- **文档**: Swagger/OpenAPI
- **测试**: Jest
- **监控**: 自定义健康检查

## 支持的标准

- **xAPI 2.0** - Experience API标准
- **SCORM** - 可扩展支持SCORM标准
- **LTI** - Learning Tools Interoperability
- **QTI** - Question & Test Interoperability

## 快速开始

### 环境要求
- Node.js >= 18.0.0
- MySQL >= 8.0.0
- Redis >= 6.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 环境配置
复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的参数：
```env
# 基础配置
NODE_ENV=development
PORT=3030
HOST=0.0.0.0

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=learning_tracking

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# xAPI配置
XAPI_ENDPOINT=http://localhost:8080/xapi
XAPI_VERSION=2.0.0
```

### 数据库初始化
```bash
# 运行数据库迁移
npm run migration:run

# 初始化基础数据
npm run seed:run
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产构建
npm run build

# 生产运行
npm run start:prod
```

## API 接口

### 学习记录管理
- `POST /api/v1/learning-tracking/records` - 创建学习记录
- `GET /api/v1/learning-tracking/records` - 获取学习记录列表
- `GET /api/v1/learning-tracking/records/:id` - 获取学习记录详情
- `PUT /api/v1/learning-tracking/records/:id` - 更新学习记录
- `DELETE /api/v1/learning-tracking/records/:id` - 删除学习记录

### xAPI数据采集
- `POST /api/v1/learning-tracking/xapi/statements` - 提交xAPI语句
- `GET /api/v1/learning-tracking/xapi/statements` - 查询xAPI语句
- `POST /api/v1/learning-tracking/xapi/batch` - 批量提交xAPI语句

### 用户画像分析
- `GET /api/v1/learning-tracking/profile/:userId` - 获取用户画像
- `POST /api/v1/learning-tracking/profile/:userId/analyze` - 触发画像分析
- `GET /api/v1/learning-tracking/profile/:userId/insights` - 获取学习洞察

### 个性化推荐
- `GET /api/v1/learning-tracking/recommendations/:userId` - 获取个性化推荐
- `POST /api/v1/learning-tracking/recommendations/:userId/feedback` - 推荐反馈
- `GET /api/v1/learning-tracking/recommendations/:userId/history` - 推荐历史

### 数据同步
- `POST /api/v1/learning-tracking/sync/trigger` - 触发数据同步
- `GET /api/v1/learning-tracking/sync/status` - 获取同步状态
- `GET /api/v1/learning-tracking/sync/logs` - 获取同步日志

### 健康检查
- `GET /api/v1/health` - 基础健康检查
- `GET /api/v1/health/detailed` - 详细健康检查
- `GET /api/v1/health/ready` - 就绪检查
- `GET /api/v1/health/live` - 存活检查

## 配置说明

### 数据库配置
服务支持MySQL数据库，需要配置以下参数：
- `DB_HOST`: 数据库主机地址
- `DB_PORT`: 数据库端口
- `DB_USERNAME`: 数据库用户名
- `DB_PASSWORD`: 数据库密码
- `DB_DATABASE`: 数据库名称

### Redis配置
用于缓存和消息队列：
- `REDIS_HOST`: Redis主机地址
- `REDIS_PORT`: Redis端口
- `REDIS_PASSWORD`: Redis密码（可选）
- `REDIS_DB`: Redis数据库编号

### xAPI配置
与xAPI兼容的LRS集成：
- `XAPI_ENDPOINT`: xAPI端点URL
- `XAPI_VERSION`: xAPI版本
- `XAPI_USERNAME`: 认证用户名
- `XAPI_PASSWORD`: 认证密码

## 监控和日志

- 健康检查: `GET /api/v1/health`
- 服务信息: `GET /api/v1/`
- 版本信息: `GET /api/v1/version`
- API文档: `http://localhost:3030/api/docs`

## 部署

### Docker部署
```bash
# 构建镜像
docker build -t learning-tracking-service .

# 运行容器
docker run -p 3030:3030 learning-tracking-service
```

### Kubernetes部署
```bash
# 应用配置
kubectl apply -f k8s/

# 检查状态
kubectl get pods -l app=learning-tracking-service
```

## 开发指南

### 项目结构
```
src/
├── controllers/          # 控制器
├── entities/            # 数据实体
├── health/              # 健康检查
├── learninglocker/      # Learninglocker集成
├── migrations/          # 数据库迁移
├── processors/          # 队列处理器
├── profile/             # 用户画像分析
├── recommendation/      # 个性化推荐
├── sync/               # 数据同步
├── xapi/               # xAPI相关
├── app.module.ts       # 应用模块
├── app.controller.ts   # 应用控制器
├── app.service.ts      # 应用服务
└── main.ts             # 启动文件
```

### 测试
```bash
# 单元测试
npm run test

# 测试覆盖率
npm run test:cov

# E2E测试
npm run test:e2e
```

## 许可证

MIT License
