import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
import { AIAssistantService } from './ai-assistant.service';
import { NLPService } from './nlp.service';
import { KnowledgeBaseService } from './knowledge-base.service';
import { AIConversation } from '../database/entities/ai-conversation.entity';

describe('AIAssistantService', () => {
  let service: AIAssistantService;
  let conversationRepository: Repository<AIConversation>;
  let nlpService: NLPService;
  let knowledgeBaseService: KnowledgeBaseService;

  const mockConversationRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockNLPService = {
    analyzeMessage: jest.fn(),
    extractIntent: jest.fn(),
    extractEntities: jest.fn(),
  };

  const mockKnowledgeBaseService = {
    searchMaintenance: jest.fn(),
    searchTroubleshooting: jest.fn(),
    searchTraining: jest.fn(),
    searchFAQ: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AIAssistantService,
        {
          provide: getRepositoryToken(AIConversation),
          useValue: mockConversationRepository,
        },
        {
          provide: NLPService,
          useValue: mockNLPService,
        },
        {
          provide: KnowledgeBaseService,
          useValue: mockKnowledgeBaseService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AIAssistantService>(AIAssistantService);
    conversationRepository = module.get<Repository<AIConversation>>(getRepositoryToken(AIConversation));
    nlpService = module.get<NLPService>(NLPService);
    knowledgeBaseService = module.get<KnowledgeBaseService>(KnowledgeBaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('processMessage', () => {
    it('should process maintenance question successfully', async () => {
      const userId = 'user123';
      const message = '如何维护CNC机床';
      const context = {};

      // Mock NLP service responses
      mockNLPService.analyzeMessage.mockResolvedValue({
        originalMessage: message,
        cleanedMessage: '如何维护cnc机床',
        tokens: ['如何', '维护', 'cnc', '机床'],
        sentiment: { score: 0, sentiment: 'neutral' },
        keywords: ['维护', 'cnc', '机床'],
        language: 'zh-CN',
      });

      mockNLPService.extractIntent.mockResolvedValue({
        name: 'maintenance_question',
        confidence: 0.9,
        alternatives: [],
      });

      mockNLPService.extractEntities.mockResolvedValue([
        { type: 'equipment', value: 'CNC机床', confidence: 0.9 },
        { type: 'procedure', value: '维护', confidence: 0.8 },
      ]);

      // Mock knowledge base response
      mockKnowledgeBaseService.searchMaintenance.mockResolvedValue([
        {
          content: 'CNC机床维护步骤：1. 检查冷却液 2. 清洁工作台 3. 润滑导轨',
          score: 0.9,
        },
      ]);

      // Mock conversation save
      const mockConversation = {
        id: 'conv123',
        userId,
        userMessage: message,
        aiResponse: expect.any(String),
      };
      mockConversationRepository.create.mockReturnValue(mockConversation);
      mockConversationRepository.save.mockResolvedValue(mockConversation);

      const result = await service.processMessage(userId, message, context);

      expect(result).toHaveProperty('id', 'conv123');
      expect(result).toHaveProperty('response');
      expect(result).toHaveProperty('intent', 'maintenance_question');
      expect(result).toHaveProperty('confidence', 0.9);
      expect(result).toHaveProperty('entities');
      expect(result).toHaveProperty('responseTime');
      expect(result).toHaveProperty('suggestions');

      expect(mockNLPService.analyzeMessage).toHaveBeenCalledWith(message);
      expect(mockNLPService.extractIntent).toHaveBeenCalledWith(message);
      expect(mockNLPService.extractEntities).toHaveBeenCalledWith(message);
      expect(mockKnowledgeBaseService.searchMaintenance).toHaveBeenCalled();
      expect(mockConversationRepository.create).toHaveBeenCalled();
      expect(mockConversationRepository.save).toHaveBeenCalled();
    });

    it('should handle unknown intent gracefully', async () => {
      const userId = 'user123';
      const message = '随机文本';

      mockNLPService.analyzeMessage.mockResolvedValue({
        originalMessage: message,
        cleanedMessage: '随机文本',
        tokens: ['随机', '文本'],
        sentiment: { score: 0, sentiment: 'neutral' },
        keywords: ['随机', '文本'],
        language: 'zh-CN',
      });

      mockNLPService.extractIntent.mockResolvedValue({
        name: 'unknown',
        confidence: 0.1,
        alternatives: [],
      });

      mockNLPService.extractEntities.mockResolvedValue([]);

      const mockConversation = {
        id: 'conv124',
        userId,
        userMessage: message,
        aiResponse: expect.any(String),
      };
      mockConversationRepository.create.mockReturnValue(mockConversation);
      mockConversationRepository.save.mockResolvedValue(mockConversation);

      const result = await service.processMessage(userId, message);

      expect(result).toHaveProperty('intent', 'unknown');
      expect(result.response).toContain('理解您的问题');
      expect(result.suggestions).toContain('我需要维护帮助');
    });

    it('should handle service errors', async () => {
      const userId = 'user123';
      const message = '测试消息';

      mockNLPService.analyzeMessage.mockRejectedValue(new Error('NLP服务错误'));

      await expect(service.processMessage(userId, message)).rejects.toThrow('NLP服务错误');
    });
  });

  describe('getConversationHistory', () => {
    it('should return conversation history', async () => {
      const userId = 'user123';
      const limit = 10;

      const mockHistory = [
        {
          id: 'conv1',
          userId,
          userMessage: '消息1',
          aiResponse: '响应1',
          createdAt: new Date(),
        },
        {
          id: 'conv2',
          userId,
          userMessage: '消息2',
          aiResponse: '响应2',
          createdAt: new Date(),
        },
      ];

      mockConversationRepository.find.mockResolvedValue(mockHistory);

      const result = await service.getConversationHistory(userId, limit);

      expect(result).toEqual(mockHistory);
      expect(mockConversationRepository.find).toHaveBeenCalledWith({
        where: { userId },
        order: { createdAt: 'DESC' },
        take: limit,
      });
    });
  });

  describe('updateFeedback', () => {
    it('should update conversation feedback', async () => {
      const conversationId = 'conv123';
      const feedback = { rating: 5, helpful: true, comment: '很有帮助' };

      mockConversationRepository.update.mockResolvedValue({ affected: 1 });

      await service.updateFeedback(conversationId, feedback);

      expect(mockConversationRepository.update).toHaveBeenCalledWith(conversationId, { feedback });
    });
  });

  describe('getStatistics', () => {
    it('should return AI assistant statistics', async () => {
      const mockStats = {
        totalConversations: 100,
        avgResponseTime: 1500,
      };

      mockConversationRepository.count.mockResolvedValue(100);
      mockConversationRepository.createQueryBuilder.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ avgResponseTime: 1500 }),
      });

      const result = await service.getStatistics();

      expect(result).toHaveProperty('totalConversations', 100);
      expect(result).toHaveProperty('averageResponseTime', 1500);
    });
  });
});
