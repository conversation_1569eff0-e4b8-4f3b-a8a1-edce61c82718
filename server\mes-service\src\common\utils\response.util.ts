/**
 * 统一响应格式工具
 */
export class ResponseUtil {
  /**
   * 成功响应
   */
  static success<T>(data: T, message: string = '操作成功'): ApiResponse<T> {
    return {
      code: 200,
      message,
      data,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 分页响应
   */
  static paginate<T>(
    data: T[],
    total: number,
    page: number,
    limit: number,
    message: string = '查询成功',
  ): PaginatedResponse<T> {
    return {
      code: 200,
      message,
      data,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 错误响应
   */
  static error(code: number, message: string, details?: any): ApiResponse<null> {
    return {
      code,
      message,
      data: null,
      details,
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * API响应接口
 */
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  details?: any;
  timestamp: string;
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T> {
  code: number;
  message: string;
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  timestamp: string;
}
