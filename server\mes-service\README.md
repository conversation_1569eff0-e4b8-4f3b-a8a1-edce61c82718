# 制造执行系统 (MES) 服务

## 📋 项目概述

制造执行系统(MES)服务是智慧工厂解决方案的核心组件，负责生产过程的执行、监控和管理。该服务提供完整的制造执行功能，包括生产订单管理、工艺路线控制、质量管理、库存管理、生产调度和实时跟踪等核心功能。

## 🏗️ 系统架构

### 核心模块
- **订单管理 (Order)**: 生产订单的创建、调度和执行管理
- **工艺管理 (Process)**: 工艺路线定义和执行控制
- **质量管理 (Quality)**: 质量检验、质量控制和质量分析
- **库存管理 (Inventory)**: 原材料、半成品、成品库存管理
- **生产调度 (Scheduling)**: 智能生产调度和资源优化
- **生产跟踪 (Tracking)**: 实时生产进度跟踪和状态监控
- **报表分析 (Report)**: 生产报表和数据分析
- **实时通信 (WebSocket)**: 实时数据推送和状态更新

### 技术栈
- **框架**: NestJS + TypeScript
- **数据库**: MySQL + TypeORM
- **缓存**: Redis
- **实时通信**: Socket.IO
- **任务调度**: @nestjs/schedule
- **API文档**: Swagger/OpenAPI
- **验证**: class-validator + class-transformer

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- MySQL >= 8.0
- Redis >= 6.0

### 安装依赖
```bash
npm install
```

### 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 数据库初始化
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE mes_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行数据库迁移（开发环境会自动同步）
npm run start:dev
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod

# 调试模式
npm run start:debug
```

## 📡 API 接口

### 服务信息
- **服务地址**: http://localhost:3008
- **API前缀**: /api/v1
- **API文档**: http://localhost:3008/api/docs

### 主要接口
- **订单管理**: `/api/v1/orders`
- **工艺管理**: `/api/v1/processes`
- **质量管理**: `/api/v1/quality`
- **库存管理**: `/api/v1/inventory`
- **生产调度**: `/api/v1/scheduling`
- **生产跟踪**: `/api/v1/tracking`
- **报表分析**: `/api/v1/reports`

## 🔧 开发指南

### 项目结构
```
src/
├── main.ts                 # 应用入口
├── app.module.ts           # 主模块
├── order/                  # 订单管理模块
│   ├── entities/          # 数据实体
│   ├── dto/               # 数据传输对象
│   ├── order.controller.ts
│   ├── order.service.ts
│   └── order.module.ts
├── process/               # 工艺管理模块
├── quality/               # 质量管理模块
├── inventory/             # 库存管理模块
├── scheduling/            # 生产调度模块
├── tracking/              # 生产跟踪模块
├── report/                # 报表分析模块
├── websocket/             # 实时通信模块
└── common/                # 公共组件
    ├── decorators/        # 装饰器
    ├── filters/           # 异常过滤器
    ├── guards/            # 守卫
    ├── interceptors/      # 拦截器
    └── utils/             # 工具函数
```

### 开发规范
1. 使用TypeScript严格模式
2. 遵循NestJS最佳实践
3. 使用class-validator进行数据验证
4. 使用Swagger注解生成API文档
5. 编写单元测试和集成测试

## 🧪 测试

```bash
# 单元测试
npm run test

# 监听模式测试
npm run test:watch

# 测试覆盖率
npm run test:cov

# E2E测试
npm run test:e2e
```

## 📦 部署

### Docker部署
```bash
# 构建镜像
docker build -t mes-service .

# 运行容器
docker run -p 3008:3008 mes-service
```

### 生产环境
```bash
# 构建生产版本
npm run build

# 启动生产服务
npm run start:prod
```

## 📊 监控指标

### 健康检查
- **端点**: `/api/v1/health`
- **数据库连接**: 检查MySQL连接状态
- **Redis连接**: 检查Redis连接状态
- **内存使用**: 监控内存使用情况

### 性能指标
- **响应时间**: API响应时间统计
- **吞吐量**: 请求处理能力
- **错误率**: 错误请求比例
- **资源使用**: CPU和内存使用率

## 🔒 安全特性

- **输入验证**: 严格的数据验证和清理
- **SQL注入防护**: 使用参数化查询
- **CORS配置**: 跨域请求控制
- **速率限制**: API调用频率限制
- **日志审计**: 完整的操作日志记录

## 📝 更新日志

### v1.0.0 (2024-06-30)
- ✅ 初始版本发布
- ✅ 完整的MES核心功能
- ✅ 实时数据推送
- ✅ 完善的API文档
- ✅ 健康检查和监控

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
