/**
 * 学习记录跟踪服务应用模块
 */

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TerminusModule } from '@nestjs/terminus';
import { LearningTrackingModule } from './learning-tracking.module';
import { HealthModule } from './health/health.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // 健康检查模块
    TerminusModule,
    HealthModule,

    // 核心业务模块
    LearningTrackingModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
