/**
 * 日志配置
 */

import { LoggerService } from '@nestjs/common';

export interface LoggerConfig {
  level: string;
  fileEnabled: boolean;
  filePath: string;
  maxFiles: number;
  maxSize: string;
}

export class CustomLogger implements LoggerService {
  private context?: string;

  constructor(context?: string) {
    this.context = context;
  }

  log(message: any, context?: string) {
    const timestamp = new Date().toISOString();
    const logContext = context || this.context || 'Application';
    console.log(`[${timestamp}] [LOG] [${logContext}] ${message}`);
  }

  error(message: any, trace?: string, context?: string) {
    const timestamp = new Date().toISOString();
    const logContext = context || this.context || 'Application';
    console.error(`[${timestamp}] [ERROR] [${logContext}] ${message}`);
    if (trace) {
      console.error(`[${timestamp}] [ERROR] [${logContext}] Trace: ${trace}`);
    }
  }

  warn(message: any, context?: string) {
    const timestamp = new Date().toISOString();
    const logContext = context || this.context || 'Application';
    console.warn(`[${timestamp}] [WARN] [${logContext}] ${message}`);
  }

  debug(message: any, context?: string) {
    const timestamp = new Date().toISOString();
    const logContext = context || this.context || 'Application';
    console.debug(`[${timestamp}] [DEBUG] [${logContext}] ${message}`);
  }

  verbose(message: any, context?: string) {
    const timestamp = new Date().toISOString();
    const logContext = context || this.context || 'Application';
    console.log(`[${timestamp}] [VERBOSE] [${logContext}] ${message}`);
  }
}

export const createLogger = (context?: string): CustomLogger => {
  return new CustomLogger(context);
};
