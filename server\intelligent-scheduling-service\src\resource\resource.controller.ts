import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { ResourceManagementService } from './resource-management.service';
import { ResourceOptimizationService } from './resource-optimization.service';
import { ResourceMonitoringService } from './resource-monitoring.service';

/**
 * 资源管理控制器
 */
@ApiTags('resource')
@Controller('resource')
export class ResourceController {
  constructor(
    private readonly resourceService: ResourceManagementService,
    private readonly optimizationService: ResourceOptimizationService,
    private readonly monitoringService: ResourceMonitoringService,
  ) {}

  /**
   * 获取资源列表
   */
  @Get()
  @ApiOperation({ summary: '获取资源列表' })
  @ApiQuery({ name: 'type', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'offset', required: false, type: Number })
  @ApiResponse({ status: 200, description: '资源列表' })
  async getResources(
    @Query('type') type?: string,
    @Query('status') status?: string,
    @Query('limit') limit: number = 20,
    @Query('offset') offset: number = 0,
  ): Promise<any> {
    const resources = await this.resourceService.getResources({
      type,
      status,
      limit,
      offset,
    });

    return {
      success: true,
      message: '获取资源列表成功',
      data: resources,
    };
  }

  /**
   * 获取资源详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取资源详情' })
  @ApiParam({ name: 'id', description: '资源ID' })
  @ApiResponse({ status: 200, description: '资源详情' })
  async getResource(@Param('id', ParseUUIDPipe) id: string): Promise<any> {
    const resource = await this.resourceService.getResourceById(id);

    return {
      success: true,
      message: '获取资源详情成功',
      data: resource,
    };
  }

  /**
   * 获取资源利用率
   */
  @Get(':id/utilization')
  @ApiOperation({ summary: '获取资源利用率' })
  @ApiParam({ name: 'id', description: '资源ID' })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiResponse({ status: 200, description: '资源利用率' })
  async getResourceUtilization(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<any> {
    const utilization = await this.monitoringService.getResourceUtilization(
      id,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );

    return {
      success: true,
      message: '获取资源利用率成功',
      data: utilization,
    };
  }

  /**
   * 资源优化建议
   */
  @Post('optimization/suggestions')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '获取资源优化建议' })
  @ApiResponse({ status: 200, description: '优化建议' })
  async getOptimizationSuggestions(): Promise<any> {
    const suggestions = await this.optimizationService.generateOptimizationSuggestions();

    return {
      success: true,
      message: '获取优化建议成功',
      data: suggestions,
    };
  }

  /**
   * 资源瓶颈分析
   */
  @Get('analysis/bottlenecks')
  @ApiOperation({ summary: '资源瓶颈分析' })
  @ApiResponse({ status: 200, description: '瓶颈分析结果' })
  async analyzeBottlenecks(): Promise<any> {
    const analysis = await this.optimizationService.analyzeResourceBottlenecks();

    return {
      success: true,
      message: '瓶颈分析完成',
      data: analysis,
    };
  }

  /**
   * 资源监控统计
   */
  @Get('monitoring/stats')
  @ApiOperation({ summary: '获取资源监控统计' })
  @ApiResponse({ status: 200, description: '监控统计数据' })
  async getMonitoringStats(): Promise<any> {
    const stats = await this.monitoringService.getMonitoringStats();

    return {
      success: true,
      message: '获取监控统计成功',
      data: stats,
    };
  }
}
