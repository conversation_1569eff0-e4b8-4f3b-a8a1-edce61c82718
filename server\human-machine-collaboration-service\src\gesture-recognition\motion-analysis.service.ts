import { Injectable, Logger } from '@nestjs/common';

/**
 * 运动分析服务
 * 分析手部运动轨迹和动作模式
 */
@Injectable()
export class MotionAnalysisService {
  private readonly logger = new Logger(MotionAnalysisService.name);
  private motionHistory: Map<string, any[]> = new Map();

  /**
   * 初始化运动分析
   */
  async initialize(): Promise<void> {
    this.logger.log('运动分析服务初始化完成');
  }

  /**
   * 分析运动
   * @param hands 手部数据
   * @param sessionId 会话ID
   * @returns 分析结果
   */
  async analyzeMotion(hands: any[], sessionId: string): Promise<any> {
    try {
      if (!this.motionHistory.has(sessionId)) {
        this.motionHistory.set(sessionId, []);
      }

      const history = this.motionHistory.get(sessionId)!;
      history.push({
        timestamp: Date.now(),
        hands,
      });

      // 保持历史记录在合理范围内
      if (history.length > 30) {
        history.shift();
      }

      // 分析运动特征
      const motionFeatures = this.extractMotionFeatures(history);
      
      return {
        velocity: motionFeatures.velocity,
        acceleration: motionFeatures.acceleration,
        direction: motionFeatures.direction,
        smoothness: motionFeatures.smoothness,
        stability: motionFeatures.stability,
      };

    } catch (error) {
      this.logger.error('分析运动失败:', error);
      return null;
    }
  }

  /**
   * 提取运动特征
   */
  private extractMotionFeatures(history: any[]): any {
    if (history.length < 2) {
      return {
        velocity: 0,
        acceleration: 0,
        direction: { x: 0, y: 0 },
        smoothness: 1,
        stability: 1,
      };
    }

    // 简化的运动特征提取
    const latest = history[history.length - 1];
    const previous = history[history.length - 2];

    const timeDiff = latest.timestamp - previous.timestamp;
    
    // 计算速度（简化）
    const velocity = this.calculateVelocity(latest.hands, previous.hands, timeDiff);
    
    return {
      velocity,
      acceleration: 0, // 简化
      direction: { x: 0, y: 0 }, // 简化
      smoothness: 0.8, // 模拟值
      stability: 0.9, // 模拟值
    };
  }

  /**
   * 计算速度
   */
  private calculateVelocity(currentHands: any[], previousHands: any[], timeDiff: number): number {
    if (!currentHands.length || !previousHands.length || timeDiff === 0) {
      return 0;
    }

    // 简化的速度计算
    const currentPos = currentHands[0].landmarks[0]; // 使用第一个关键点
    const previousPos = previousHands[0].landmarks[0];

    const distance = Math.sqrt(
      Math.pow(currentPos.x - previousPos.x, 2) +
      Math.pow(currentPos.y - previousPos.y, 2)
    );

    return distance / (timeDiff / 1000); // 像素/秒
  }
}
