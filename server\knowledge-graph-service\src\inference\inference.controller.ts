import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { InferenceService } from './inference.service';
import { CreateRuleDto } from './dto/create-rule.dto';
import { UpdateRuleDto } from './dto/update-rule.dto';

@ApiTags('inference')
@Controller('inference')
@ApiBearerAuth()
export class InferenceController {
  private readonly logger = new Logger(InferenceController.name);

  constructor(
    private readonly inferenceService: InferenceService,
  ) {}

  @Post('rules')
  @ApiOperation({ summary: '创建推理规则' })
  @ApiResponse({ status: 201, description: '规则创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async createRule(@Body() createRuleDto: CreateRuleDto) {
    try {
      const rule = await this.inferenceService.createRule(createRuleDto);
      return {
        success: true,
        data: rule,
        message: '推理规则创建成功',
      };
    } catch (error: any) {
      this.logger.error('创建推理规则失败', error);
      throw new HttpException(
        {
          success: false,
          message: '创建推理规则失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('rules')
  @ApiOperation({ summary: '获取所有启用的推理规则' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getEnabledRules() {
    try {
      const rules = await this.inferenceService.getEnabledRules();
      return {
        success: true,
        data: rules,
        message: '推理规则获取成功',
      };
    } catch (error: any) {
      this.logger.error('获取推理规则失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取推理规则失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Put('rules/:id')
  @ApiOperation({ summary: '更新推理规则' })
  @ApiParam({ name: 'id', description: '规则ID' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '规则不存在' })
  async updateRule(
    @Param('id') id: string,
    @Body() updateRuleDto: UpdateRuleDto,
  ) {
    try {
      const rule = await this.inferenceService.updateRule(id, updateRuleDto);
      return {
        success: true,
        data: rule,
        message: '推理规则更新成功',
      };
    } catch (error: any) {
      this.logger.error('更新推理规则失败', error);
      throw new HttpException(
        {
          success: false,
          message: '更新推理规则失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Delete('rules/:id')
  @ApiOperation({ summary: '删除推理规则' })
  @ApiParam({ name: 'id', description: '规则ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '规则不存在' })
  async deleteRule(@Param('id') id: string) {
    try {
      await this.inferenceService.deleteRule(id);
      return {
        success: true,
        message: '推理规则删除成功',
      };
    } catch (error: any) {
      this.logger.error('删除推理规则失败', error);
      throw new HttpException(
        {
          success: false,
          message: '删除推理规则失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('rules/statistics')
  @ApiOperation({ summary: '获取推理规则统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getRuleStatistics() {
    try {
      const statistics = await this.inferenceService.getRuleStatistics();
      return {
        success: true,
        data: statistics,
        message: '推理规则统计信息获取成功',
      };
    } catch (error: any) {
      this.logger.error('获取推理规则统计信息失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取推理规则统计信息失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
