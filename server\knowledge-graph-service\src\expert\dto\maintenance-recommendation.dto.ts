import { IsString, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class MaintenanceRecommendationDto {
  @ApiProperty({
    description: '设备ID',
    example: 'equipment_001',
  })
  @IsString()
  equipmentId: string;

  @ApiProperty({
    description: '维护历史记录',
    example: [
      {
        date: '2023-01-01',
        type: 'preventive',
        description: '定期保养',
      },
    ],
  })
  @IsArray()
  maintenanceHistory: any[];
}
