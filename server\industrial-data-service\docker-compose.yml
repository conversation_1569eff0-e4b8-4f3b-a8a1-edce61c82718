version: '3.8'

services:
  # 工业数据采集服务
  industrial-data-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: industrial-data-service
    ports:
      - "3007:3007"
    environment:
      - NODE_ENV=production
      - PORT=3007
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=industrial123
      - DB_DATABASE=industrial_data
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - industrial-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3007/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: industrial-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=industrial123
      - MYSQL_DATABASE=industrial_data
      - MYSQL_USER=industrial
      - MYSQL_PASSWORD=industrial123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - industrial-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: industrial-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - industrial-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # InfluxDB时序数据库（可选）
  influxdb:
    image: influxdb:2.7-alpine
    container_name: industrial-influxdb
    ports:
      - "8086:8086"
    environment:
      - INFLUXDB_DB=industrial_data
      - INFLUXDB_ADMIN_USER=admin
      - INFLUXDB_ADMIN_PASSWORD=industrial123
    volumes:
      - influxdb_data:/var/lib/influxdb2
    networks:
      - industrial-network
    restart: unless-stopped

  # Grafana监控面板（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: industrial-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=industrial123
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - industrial-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  influxdb_data:
  grafana_data:

networks:
  industrial-network:
    driver: bridge
