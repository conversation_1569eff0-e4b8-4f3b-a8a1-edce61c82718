#!/bin/bash

# 监控服务启动脚本

set -e

echo "🚀 启动监控服务..."

# 检查Node.js版本
NODE_VERSION=$(node --version)
echo "Node.js版本: $NODE_VERSION"

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "⚠️  未找到.env文件，复制.env.example为.env"
    cp .env.example .env
    echo "✅ 已创建.env文件，请根据实际环境修改配置"
fi

# 安装依赖
echo "📦 安装依赖..."
npm install

# 构建项目
echo "🔨 构建项目..."
npm run build

# 检查数据库连接
echo "🔍 检查数据库连接..."
npm run migration:check || {
    echo "⚠️  数据库连接失败，请检查数据库配置"
    exit 1
}

# 运行数据库迁移
echo "🗄️  运行数据库迁移..."
npm run migration:run

# 启动服务
echo "🎯 启动监控服务..."
if [ "$NODE_ENV" = "production" ]; then
    npm run start:prod
else
    npm run start:dev
fi
