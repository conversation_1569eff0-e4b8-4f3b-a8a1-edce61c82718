import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('inference_rules')
@Index(['name'])
@Index(['priority'])
@Index(['enabled'])
export class InferenceRuleModel {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: '规则名称',
  })
  name: string;

  @Column({
    type: 'text',
    comment: '规则描述',
  })
  description: string;

  @Column({
    type: 'text',
    comment: '规则条件（Cypher查询）',
  })
  condition: string;

  @Column({
    type: 'text',
    comment: '推理结论（Cypher查询）',
  })
  conclusion: string;

  @Column({
    type: 'decimal',
    precision: 3,
    scale: 2,
    default: 1.0,
    comment: '置信度',
  })
  confidence: number;

  @Column({
    type: 'int',
    default: 1,
    comment: '优先级（数字越小优先级越高）',
  })
  priority: number;

  @Column({
    type: 'boolean',
    default: true,
    comment: '是否启用',
  })
  enabled: boolean;

  @Column({
    type: 'varchar',
    length: 100,
    comment: '规则类别',
  })
  category: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: '规则参数',
  })
  parameters: Record<string, any>;

  @Column({
    type: 'json',
    nullable: true,
    comment: '规则标签',
  })
  tags: string[];

  @Column({
    type: 'varchar',
    length: 100,
    comment: '创建者',
  })
  createdBy: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '最后修改者',
  })
  updatedBy: string;

  @Column({
    type: 'int',
    default: 0,
    comment: '应用次数',
  })
  appliedCount: number;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: '最后应用时间',
  })
  lastAppliedAt: Date;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  updatedAt: Date;
}
