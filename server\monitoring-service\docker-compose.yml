version: '3.8'

services:
  monitoring-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: monitoring-service
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=production
      - PORT=3003
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=password
      - DB_DATABASE=monitoring
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - ELASTICSEARCH_NODE=http://elasticsearch:9200
      - PROMETHEUS_ENDPOINT=http://prometheus:9090
    depends_on:
      - mysql
      - redis
      - elasticsearch
      - prometheus
    volumes:
      - ./logs:/app/logs
    networks:
      - monitoring-network
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    container_name: monitoring-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=monitoring
      - MYSQL_USER=monitoring
      - MYSQL_PASSWORD=monitoring123
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - monitoring-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: monitoring-redis
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - monitoring-network
    restart: unless-stopped

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: monitoring-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9201:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - monitoring-network
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest
    container_name: monitoring-prometheus
    ports:
      - "9091:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - monitoring-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: monitoring-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana:/etc/grafana/provisioning
    networks:
      - monitoring-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  elasticsearch_data:
  prometheus_data:
  grafana_data:

networks:
  monitoring-network:
    driver: bridge
