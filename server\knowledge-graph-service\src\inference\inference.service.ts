import { Injectable, Logger, Optional } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InferenceRuleModel } from '../knowledge/entities/inference-rule.entity';

@Injectable()
export class InferenceService {
  private readonly logger = new Logger(InferenceService.name);

  constructor(
    @Optional()
    @InjectRepository(InferenceRuleModel)
    private readonly inferenceRuleRepository?: Repository<InferenceRuleModel>,
  ) {}

  /**
   * 创建推理规则
   */
  async createRule(ruleData: Partial<InferenceRuleModel>): Promise<InferenceRuleModel> {
    try {
      if (!this.inferenceRuleRepository) {
        throw new Error('数据库未配置，无法创建推理规则');
      }
      const rule = this.inferenceRuleRepository.create(ruleData);
      const savedRule = await this.inferenceRuleRepository.save(rule);
      this.logger.log(`推理规则创建成功: ${savedRule.name}`);
      return savedRule;
    } catch (error) {
      this.logger.error('创建推理规则失败', error);
      throw error;
    }
  }

  /**
   * 获取所有启用的推理规则
   */
  async getEnabledRules(): Promise<InferenceRuleModel[]> {
    if (!this.inferenceRuleRepository) {
      this.logger.warn('数据库未配置，返回空的推理规则列表');
      return [];
    }
    return this.inferenceRuleRepository.find({
      where: { enabled: true },
      order: { priority: 'ASC' },
    });
  }

  /**
   * 更新推理规则
   */
  async updateRule(id: string, updateData: Partial<InferenceRuleModel>): Promise<InferenceRuleModel> {
    try {
      await this.inferenceRuleRepository.update(id, updateData);
      const updatedRule = await this.inferenceRuleRepository.findOne({ where: { id } });
      this.logger.log(`推理规则更新成功: ${updatedRule?.name}`);
      return updatedRule!;
    } catch (error) {
      this.logger.error('更新推理规则失败', error);
      throw error;
    }
  }

  /**
   * 删除推理规则
   */
  async deleteRule(id: string): Promise<void> {
    try {
      await this.inferenceRuleRepository.delete(id);
      this.logger.log(`推理规则删除成功: ${id}`);
    } catch (error) {
      this.logger.error('删除推理规则失败', error);
      throw error;
    }
  }

  /**
   * 记录规则应用
   */
  async recordRuleApplication(ruleId: string): Promise<void> {
    try {
      await this.inferenceRuleRepository.increment(
        { id: ruleId },
        'appliedCount',
        1,
      );
      await this.inferenceRuleRepository.update(ruleId, {
        lastAppliedAt: new Date(),
      });
    } catch (error) {
      this.logger.error('记录规则应用失败', error);
    }
  }

  /**
   * 获取规则统计信息
   */
  async getRuleStatistics(): Promise<any> {
    try {
      const totalRules = await this.inferenceRuleRepository.count();
      const enabledRules = await this.inferenceRuleRepository.count({
        where: { enabled: true },
      });
      const disabledRules = totalRules - enabledRules;

      const mostUsedRules = await this.inferenceRuleRepository.find({
        order: { appliedCount: 'DESC' },
        take: 10,
      });

      return {
        totalRules,
        enabledRules,
        disabledRules,
        mostUsedRules,
      };
    } catch (error) {
      this.logger.error('获取规则统计信息失败', error);
      throw error;
    }
  }
}
