import { IsS<PERSON>, <PERSON><PERSON><PERSON>ber, IsBoolean, IsOptional, IsArray, IsObject, Min, Max } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateRuleDto {
  @ApiProperty({
    description: '规则名称',
    example: '设备故障推理规则',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: '规则描述',
    example: '当设备温度异常时，推理可能的故障原因',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: '规则条件（Cypher查询）',
    example: 'MATCH (d:Equipment)-[:HAS_SYMPTOM]->(s:Symptom {type: "temperature_high"})',
  })
  @IsString()
  condition: string;

  @ApiProperty({
    description: '推理结论（Cypher查询）',
    example: 'CREATE (d)-[:LIKELY_HAS_FAULT]->(f:Fault {type: "cooling_system_failure"})',
  })
  @IsString()
  conclusion: string;

  @ApiProper<PERSON>({
    description: '置信度',
    minimum: 0,
    maximum: 1,
    example: 0.8,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence: number;

  @ApiProperty({
    description: '优先级（数字越小优先级越高）',
    example: 1,
  })
  @IsNumber()
  @Min(1)
  priority: number;

  @ApiProperty({
    description: '是否启用',
    example: true,
  })
  @IsBoolean()
  enabled: boolean;

  @ApiProperty({
    description: '规则类别',
    example: 'fault_diagnosis',
  })
  @IsString()
  category: string;

  @ApiProperty({
    description: '规则参数',
    example: { threshold: 0.7 },
    required: false,
  })
  @IsObject()
  @IsOptional()
  parameters?: Record<string, any>;

  @ApiProperty({
    description: '规则标签',
    example: ['设备', '故障', '诊断'],
    required: false,
  })
  @IsArray()
  @IsOptional()
  tags?: string[];

  @ApiProperty({
    description: '创建者',
    example: 'admin',
  })
  @IsString()
  createdBy: string;
}
