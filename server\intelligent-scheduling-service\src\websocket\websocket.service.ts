import { Injectable, Logger } from '@nestjs/common';
import { SchedulingGateway } from './scheduling.gateway';

/**
 * WebSocket服务
 */
@Injectable()
export class WebSocketService {
  private readonly logger = new Logger(WebSocketService.name);

  constructor(
    private readonly schedulingGateway: SchedulingGateway,
  ) {}

  /**
   * 通知调度方案更新
   */
  notifyScheduleUpdate(solution: any): void {
    try {
      this.schedulingGateway.broadcastScheduleUpdate(solution);
      this.logger.log('调度方案更新通知已发送');
    } catch (error) {
      this.logger.error(`发送调度更新通知失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 通知优化进度
   */
  notifyOptimizationProgress(progress: {
    jobId: string;
    progress: number;
    stage: string;
    message?: string;
  }): void {
    try {
      this.schedulingGateway.broadcastOptimizationProgress(progress);
      this.logger.debug(`优化进度通知: ${progress.jobId} - ${progress.progress}%`);
    } catch (error) {
      this.logger.error(`发送优化进度通知失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 发送系统警告
   */
  sendSystemAlert(alert: {
    level: 'info' | 'warning' | 'error' | 'critical';
    message: string;
    source: string;
    details?: any;
  }): void {
    try {
      this.schedulingGateway.broadcastSystemAlert(alert);
      this.logger.log(`系统警告已发送: ${alert.level} - ${alert.message}`);
    } catch (error) {
      this.logger.error(`发送系统警告失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): {
    connectedClients: number;
    status: 'active' | 'inactive';
  } {
    const connectedClients = this.schedulingGateway.getConnectedClientsCount();
    
    return {
      connectedClients,
      status: connectedClients > 0 ? 'active' : 'inactive',
    };
  }
}
