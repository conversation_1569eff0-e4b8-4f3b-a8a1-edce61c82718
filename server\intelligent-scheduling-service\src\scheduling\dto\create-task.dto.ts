import {
  IsString,
  IsNumber,
  IsEnum,
  IsDate,
  IsArray,
  IsOptional,
  IsObject,
  ValidateNested,
  Min,
  Max,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TaskPriority, ResourceType } from '../entities/production-task.entity';

/**
 * 资源需求DTO
 */
export class ResourceRequirementDto {
  @ApiProperty({ description: '资源类型', enum: ResourceType })
  @IsEnum(ResourceType)
  resourceType: ResourceType;

  @ApiProperty({ description: '资源ID' })
  @IsString()
  resourceId: string;

  @ApiProperty({ description: '需求数量', minimum: 1 })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({ description: '使用时长(分钟)', minimum: 1 })
  @IsNumber()
  @Min(1)
  duration: number;

  @ApiPropertyOptional({ description: '备选资源ID列表', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  alternatives?: string[];
}

/**
 * 创建任务DTO
 */
export class CreateTaskDto {
  @ApiProperty({ description: '任务ID', example: 'TASK_001' })
  @IsString()
  taskId: string;

  @ApiProperty({ description: '订单ID', example: 'ORDER_001' })
  @IsString()
  orderId: string;

  @ApiProperty({ description: '产品ID', example: 'PRODUCT_001' })
  @IsString()
  productId: string;

  @ApiProperty({ description: '任务名称', example: '生产任务A' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: '任务描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '生产数量', minimum: 1, example: 100 })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({ 
    description: '任务优先级', 
    enum: TaskPriority,
    example: TaskPriority.NORMAL 
  })
  @IsEnum(TaskPriority)
  priority: TaskPriority;

  @ApiProperty({ description: '截止日期', example: '2024-12-31T23:59:59Z' })
  @IsDate()
  @Transform(({ value }) => new Date(value))
  dueDate: Date;

  @ApiProperty({ description: '预估时长(分钟)', minimum: 1, example: 120 })
  @IsNumber()
  @Min(1)
  estimatedDuration: number;

  @ApiProperty({ 
    description: '所需资源列表',
    type: [ResourceRequirementDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ResourceRequirementDto)
  requiredResources: ResourceRequirementDto[];

  @ApiPropertyOptional({ 
    description: '依赖任务ID列表',
    type: [String],
    example: ['TASK_002', 'TASK_003']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  dependencies?: string[];

  @ApiPropertyOptional({ 
    description: '技能要求列表',
    type: [String],
    example: ['welding', 'assembly']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  skillRequirements?: string[];

  @ApiPropertyOptional({ description: '准备时间(分钟)', minimum: 0, example: 30 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  setupTime?: number;

  @ApiPropertyOptional({ description: '清理时间(分钟)', minimum: 0, example: 15 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  teardownTime?: number;

  @ApiPropertyOptional({ description: '批次大小', minimum: 1, example: 10 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  batchSize?: number;

  @ApiPropertyOptional({ description: '质量要求' })
  @IsOptional()
  @IsObject()
  qualityRequirements?: any;

  @ApiPropertyOptional({ description: '成本估算', minimum: 0, example: 1000.50 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  costEstimate?: number;

  @ApiPropertyOptional({ description: '能耗估算', minimum: 0, example: 50.25 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  energyEstimate?: number;

  @ApiPropertyOptional({ description: '扩展属性' })
  @IsOptional()
  @IsObject()
  metadata?: any;
}
