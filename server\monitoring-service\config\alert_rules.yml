# Prometheus 告警规则配置
groups:
  - name: system_alerts
    rules:
      # CPU使用率告警
      - alert: HighCPUUsage
        expr: cpu_usage > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "CPU使用率过高"
          description: "CPU使用率已超过80%，当前值: {{ $value }}%"

      - alert: CriticalCPUUsage
        expr: cpu_usage > 90
        for: 2m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "CPU使用率严重过高"
          description: "CPU使用率已超过90%，当前值: {{ $value }}%"

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: memory_usage > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "内存使用率过高"
          description: "内存使用率已超过85%，当前值: {{ $value }}%"

      - alert: CriticalMemoryUsage
        expr: memory_usage > 95
        for: 2m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "内存使用率严重过高"
          description: "内存使用率已超过95%，当前值: {{ $value }}%"

      # 磁盘使用率告警
      - alert: HighDiskUsage
        expr: disk_usage > 80
        for: 10m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "磁盘使用率过高"
          description: "磁盘使用率已超过80%，当前值: {{ $value }}%"

      - alert: CriticalDiskUsage
        expr: disk_usage > 90
        for: 5m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "磁盘使用率严重过高"
          description: "磁盘使用率已超过90%，当前值: {{ $value }}%"

  - name: service_alerts
    rules:
      # 服务响应时间告警
      - alert: HighResponseTime
        expr: service_response_time > 2000
        for: 3m
        labels:
          severity: warning
          service: "{{ $labels.service_name }}"
        annotations:
          summary: "服务响应时间过长"
          description: "服务 {{ $labels.service_name }} 响应时间超过2秒，当前值: {{ $value }}ms"

      - alert: CriticalResponseTime
        expr: service_response_time > 5000
        for: 1m
        labels:
          severity: critical
          service: "{{ $labels.service_name }}"
        annotations:
          summary: "服务响应时间严重过长"
          description: "服务 {{ $labels.service_name }} 响应时间超过5秒，当前值: {{ $value }}ms"

      # 服务错误率告警
      - alert: HighErrorRate
        expr: service_error_rate > 5
        for: 3m
        labels:
          severity: warning
          service: "{{ $labels.service_name }}"
        annotations:
          summary: "服务错误率过高"
          description: "服务 {{ $labels.service_name }} 错误率超过5%，当前值: {{ $value }}%"

      - alert: CriticalErrorRate
        expr: service_error_rate > 10
        for: 1m
        labels:
          severity: critical
          service: "{{ $labels.service_name }}"
        annotations:
          summary: "服务错误率严重过高"
          description: "服务 {{ $labels.service_name }} 错误率超过10%，当前值: {{ $value }}%"

      # 服务不可用告警
      - alert: ServiceDown
        expr: service_status == 0
        for: 1m
        labels:
          severity: critical
          service: "{{ $labels.service_name }}"
        annotations:
          summary: "服务不可用"
          description: "服务 {{ $labels.service_name }} 已停止响应"

  - name: database_alerts
    rules:
      # 数据库连接数告警
      - alert: HighDatabaseConnections
        expr: mysql_global_status_threads_connected > 80
        for: 5m
        labels:
          severity: warning
          service: mysql
        annotations:
          summary: "数据库连接数过高"
          description: "MySQL连接数已超过80，当前值: {{ $value }}"

      # 数据库慢查询告警
      - alert: HighSlowQueries
        expr: rate(mysql_global_status_slow_queries[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: mysql
        annotations:
          summary: "数据库慢查询过多"
          description: "MySQL慢查询频率过高，当前值: {{ $value }}/秒"

  - name: redis_alerts
    rules:
      # Redis内存使用告警
      - alert: HighRedisMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis内存使用率已超过80%，当前值: {{ $value }}%"

      # Redis连接数告警
      - alert: HighRedisConnections
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis连接数过高"
          description: "Redis连接数已超过100，当前值: {{ $value }}"

  - name: elasticsearch_alerts
    rules:
      # Elasticsearch集群状态告警
      - alert: ElasticsearchClusterRed
        expr: elasticsearch_cluster_health_status{color="red"} == 1
        for: 1m
        labels:
          severity: critical
          service: elasticsearch
        annotations:
          summary: "Elasticsearch集群状态异常"
          description: "Elasticsearch集群状态为红色，存在严重问题"

      # Elasticsearch磁盘使用告警
      - alert: HighElasticsearchDiskUsage
        expr: elasticsearch_filesystem_data_used_percent > 85
        for: 5m
        labels:
          severity: warning
          service: elasticsearch
        annotations:
          summary: "Elasticsearch磁盘使用率过高"
          description: "Elasticsearch磁盘使用率已超过85%，当前值: {{ $value }}%"
