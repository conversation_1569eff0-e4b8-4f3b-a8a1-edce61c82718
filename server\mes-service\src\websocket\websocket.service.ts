import { Injectable, Logger } from '@nestjs/common';

/**
 * WebSocket服务
 */
@Injectable()
export class WebSocketService {
  private readonly logger = new Logger(WebSocketService.name);
  private clients: Map<string, any> = new Map();

  /**
   * 添加客户端连接
   */
  addClient(clientId: string, client: any) {
    this.clients.set(clientId, client);
    this.logger.log(`客户端连接: ${clientId}`);
  }

  /**
   * 移除客户端连接
   */
  removeClient(clientId: string) {
    this.clients.delete(clientId);
    this.logger.log(`客户端断开: ${clientId}`);
  }

  /**
   * 广播消息
   */
  broadcast(event: string, data: any) {
    this.logger.log(`广播消息: ${event}`);
    this.clients.forEach((client, clientId) => {
      try {
        client.emit(event, data);
      } catch (error) {
        this.logger.error(`发送消息失败: ${clientId}`, error);
        this.clients.delete(clientId);
      }
    });
  }

  /**
   * 发送消息给特定客户端
   */
  sendToClient(clientId: string, event: string, data: any) {
    const client = this.clients.get(clientId);
    if (client) {
      try {
        client.emit(event, data);
        this.logger.log(`发送消息给客户端: ${clientId}, 事件: ${event}`);
      } catch (error) {
        this.logger.error(`发送消息失败: ${clientId}`, error);
        this.clients.delete(clientId);
      }
    }
  }

  /**
   * 获取连接的客户端数量
   */
  getClientCount(): number {
    return this.clients.size;
  }

  /**
   * 推送订单状态更新
   */
  pushOrderUpdate(orderId: string, status: string, data: any) {
    this.broadcast('order-update', {
      orderId,
      status,
      data,
      timestamp: new Date(),
    });
  }

  /**
   * 推送质量检验结果
   */
  pushQualityResult(inspectionId: string, result: string, data: any) {
    this.broadcast('quality-result', {
      inspectionId,
      result,
      data,
      timestamp: new Date(),
    });
  }

  /**
   * 推送库存变化
   */
  pushInventoryChange(materialCode: string, change: any) {
    this.broadcast('inventory-change', {
      materialCode,
      change,
      timestamp: new Date(),
    });
  }

  /**
   * 推送生产进度
   */
  pushProductionProgress(orderId: string, progress: number, data: any) {
    this.broadcast('production-progress', {
      orderId,
      progress,
      data,
      timestamp: new Date(),
    });
  }

  /**
   * 推送系统警告
   */
  pushSystemAlert(type: string, message: string, level: string = 'info') {
    this.broadcast('system-alert', {
      type,
      message,
      level,
      timestamp: new Date(),
    });
  }
}
