/**
 * 移动设备实体
 * 
 * 存储移动设备的基本信息和状态
 */

import {
  Entity,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('mobile_devices')
@Index(['userId'])
@Index(['platform'])
@Index(['isOnline'])
export class MobileDevice {
  @PrimaryColumn('varchar', { length: 100 })
  deviceId: string;

  @Column('varchar', { length: 36 })
  @Index()
  userId: string;

  @Column('varchar', { length: 100 })
  deviceName: string;

  @Column('varchar', { length: 50 })
  platform: string; // 'ios' | 'android' | 'web'

  @Column('varchar', { length: 20 })
  version: string;

  @Column('json')
  capabilities: string[];

  @Column('boolean', { default: false })
  @Index()
  isOnline: boolean;

  @Column('varchar', { length: 20, default: 'idle' })
  syncStatus: string; // 'idle' | 'syncing' | 'conflict' | 'error'

  @Column('timestamp', { default: () => 'CURRENT_TIMESTAMP' })
  lastActivity: Date;

  @Column('timestamp', { nullable: true })
  lastSyncTime?: Date;

  @Column('json', { nullable: true })
  deviceInfo?: {
    model?: string;
    osVersion?: string;
    appVersion?: string;
    screenSize?: string;
    language?: string;
    timezone?: string;
  };

  @Column('json', { nullable: true })
  settings?: {
    autoSync?: boolean;
    syncInterval?: number;
    wifiOnly?: boolean;
    backgroundSync?: boolean;
  };

  @Column('int', { default: 0 })
  totalSyncs: number;

  @Column('int', { default: 0 })
  successfulSyncs: number;

  @Column('int', { default: 0 })
  failedSyncs: number;

  @Column('timestamp', { nullable: true })
  firstRegistered?: Date;

  @Column('boolean', { default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
