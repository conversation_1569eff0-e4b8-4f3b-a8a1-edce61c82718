import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { CollaborationService } from '../collaboration/collaboration.service';
import { AIAssistantService } from '../ai-assistant/ai-assistant.service';
import { VoiceInteractionService } from '../voice-interaction/voice-interaction.service';
import { GestureRecognitionService } from '../gesture-recognition/gesture-recognition.service';

/**
 * 协作WebSocket网关
 * 提供实时协作通信功能
 */
@WebSocketGateway({
  namespace: '/collaboration',
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
  },
})
export class CollaborationGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(CollaborationGateway.name);
  private connectedUsers: Map<string, any> = new Map();

  constructor(
    private readonly collaborationService: CollaborationService,
    private readonly aiAssistantService: AIAssistantService,
    private readonly voiceInteractionService: VoiceInteractionService,
    private readonly gestureRecognitionService: GestureRecognitionService,
  ) {}

  /**
   * 客户端连接
   */
  async handleConnection(client: Socket) {
    try {
      const userId = client.handshake.query.userId as string;
      const sessionId = client.handshake.query.sessionId as string;

      if (!userId) {
        client.disconnect();
        return;
      }

      this.connectedUsers.set(client.id, {
        userId,
        sessionId,
        connectTime: new Date(),
        client,
      });

      this.logger.log(`用户连接: ${userId} (${client.id})`);
      
      // 加入用户房间
      if (sessionId) {
        client.join(sessionId);
        client.to(sessionId).emit('user-joined', { userId });
      }

      // 发送欢迎消息
      client.emit('connected', {
        message: '欢迎使用人机协作增强服务',
        userId,
        sessionId,
        timestamp: new Date(),
      });

    } catch (error) {
      this.logger.error('处理客户端连接失败:', error);
      client.disconnect();
    }
  }

  /**
   * 客户端断开连接
   */
  async handleDisconnect(client: Socket) {
    try {
      const userInfo = this.connectedUsers.get(client.id);
      if (userInfo) {
        this.logger.log(`用户断开连接: ${userInfo.userId} (${client.id})`);
        
        // 通知其他用户
        if (userInfo.sessionId) {
          client.to(userInfo.sessionId).emit('user-left', { userId: userInfo.userId });
        }

        this.connectedUsers.delete(client.id);
      }
    } catch (error) {
      this.logger.error('处理客户端断开连接失败:', error);
    }
  }

  /**
   * 加入协作会话
   */
  @SubscribeMessage('join-session')
  async handleJoinSession(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: any,
  ) {
    try {
      const { sessionId, userId, role } = data;
      
      // 加入协作会话
      await this.collaborationService.joinCollaborationSession(sessionId, userId, role);
      
      // 加入Socket房间
      client.join(sessionId);
      
      // 通知其他用户
      client.to(sessionId).emit('user-joined', { userId, role });
      
      // 发送确认消息
      client.emit('session-joined', {
        success: true,
        sessionId,
        message: '成功加入协作会话',
      });

    } catch (error) {
      this.logger.error('加入协作会话失败:', error);
      client.emit('session-joined', {
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * AI助手对话
   */
  @SubscribeMessage('ai-chat')
  async handleAIChat(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: any,
  ) {
    try {
      const { userId, message, context } = data;
      
      const response = await this.aiAssistantService.processMessage(userId, message, context);
      
      client.emit('ai-response', response);

    } catch (error) {
      this.logger.error('AI对话处理失败:', error);
      client.emit('ai-response', {
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * 语音交互
   */
  @SubscribeMessage('voice-input')
  async handleVoiceInput(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: any,
  ) {
    try {
      const { audioData, userId, sessionId } = data;
      
      const response = await this.voiceInteractionService.processVoiceInput(audioData, userId, sessionId);
      
      client.emit('voice-response', response);

    } catch (error) {
      this.logger.error('语音交互处理失败:', error);
      client.emit('voice-response', {
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * 手势识别
   */
  @SubscribeMessage('gesture-frame')
  async handleGestureFrame(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: any,
  ) {
    try {
      const { sessionId, frameData } = data;
      
      const response = await this.gestureRecognitionService.processFrame(sessionId, frameData);
      
      client.emit('gesture-result', response);

    } catch (error) {
      this.logger.error('手势识别处理失败:', error);
      client.emit('gesture-result', {
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * 广播消息到会话
   */
  @SubscribeMessage('broadcast-message')
  async handleBroadcastMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: any,
  ) {
    try {
      const { sessionId, message, type } = data;
      const userInfo = this.connectedUsers.get(client.id);
      
      if (userInfo && sessionId) {
        // 广播消息到会话中的所有用户
        this.server.to(sessionId).emit('session-message', {
          userId: userInfo.userId,
          message,
          type,
          timestamp: new Date(),
        });
      }

    } catch (error) {
      this.logger.error('广播消息失败:', error);
    }
  }

  /**
   * 获取在线用户
   */
  @SubscribeMessage('get-online-users')
  async handleGetOnlineUsers(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: any,
  ) {
    try {
      const { sessionId } = data;
      
      const onlineUsers = Array.from(this.connectedUsers.values())
        .filter(user => user.sessionId === sessionId)
        .map(user => ({
          userId: user.userId,
          connectTime: user.connectTime,
        }));

      client.emit('online-users', { users: onlineUsers });

    } catch (error) {
      this.logger.error('获取在线用户失败:', error);
    }
  }

  /**
   * 获取连接统计
   */
  getConnectionStats(): any {
    return {
      totalConnections: this.connectedUsers.size,
      connectedUsers: Array.from(this.connectedUsers.values()).map(user => ({
        userId: user.userId,
        sessionId: user.sessionId,
        connectTime: user.connectTime,
      })),
    };
  }
}
