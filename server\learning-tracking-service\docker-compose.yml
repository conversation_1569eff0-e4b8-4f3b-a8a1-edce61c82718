version: '3.8'

services:
  # 学习记录跟踪服务
  learning-tracking-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3030:3030"
      - "3031:3031"
    environment:
      - NODE_ENV=development
      - PORT=3030
      - MICROSERVICE_PORT=3031
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=password
      - DB_DATABASE=learning_tracking
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/app/logs
    networks:
      - learning-tracking-network
    restart: unless-stopped

  # MySQL数据库
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=learning_tracking
      - MYSQL_USER=learning_user
      - MYSQL_PASSWORD=learning_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./migrations:/docker-entrypoint-initdb.d
    networks:
      - learning-tracking-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  # Redis缓存和消息队列
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - learning-tracking-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Redis Commander (可选的Redis管理界面)
  redis-commander:
    image: rediscommander/redis-commander:latest
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - learning-tracking-network
    restart: unless-stopped

  # phpMyAdmin (可选的MySQL管理界面)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    ports:
      - "8080:80"
    environment:
      - PMA_HOST=mysql
      - PMA_PORT=3306
      - PMA_USER=root
      - PMA_PASSWORD=password
    depends_on:
      - mysql
    networks:
      - learning-tracking-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:

networks:
  learning-tracking-network:
    driver: bridge
