import { Controller, Get, Post, Body, Param, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { GestureRecognitionService } from '../gesture-recognition/gesture-recognition.service';

/**
 * 手势识别控制器
 */
@ApiTags('Gesture')
@Controller('gesture')
export class GestureController {
  constructor(private readonly gestureRecognitionService: GestureRecognitionService) {}

  /**
   * 开始手势识别会话
   */
  @Post('sessions')
  @ApiOperation({ summary: '开始手势识别会话' })
  @ApiResponse({ status: 201, description: '会话已创建' })
  async startSession(@Body() sessionData: any) {
    const { userId, sessionId, options } = sessionData;
    return await this.gestureRecognitionService.startRecognitionSession(userId, sessionId, options);
  }

  /**
   * 处理视频帧
   */
  @Post('sessions/:sessionId/frames')
  @ApiOperation({ summary: '处理视频帧进行手势识别' })
  @ApiResponse({ status: 200, description: '帧处理结果' })
  async processFrame(
    @Param('sessionId') sessionId: string,
    @Body() frameData: any,
  ) {
    return await this.gestureRecognitionService.processFrame(sessionId, frameData);
  }

  /**
   * 停止手势识别会话
   */
  @Delete('sessions/:sessionId')
  @ApiOperation({ summary: '停止手势识别会话' })
  @ApiResponse({ status: 200, description: '会话已结束' })
  async stopSession(@Param('sessionId') sessionId: string) {
    return await this.gestureRecognitionService.stopRecognitionSession(sessionId);
  }

  /**
   * 获取支持的手势
   */
  @Get('patterns')
  @ApiOperation({ summary: '获取支持的手势列表' })
  @ApiResponse({ status: 200, description: '手势列表' })
  async getSupportedGestures() {
    return await this.gestureRecognitionService.getSupportedGestures();
  }

  /**
   * 获取手势识别统计
   */
  @Get('statistics')
  @ApiOperation({ summary: '获取手势识别统计' })
  @ApiResponse({ status: 200, description: '统计数据' })
  async getStatistics() {
    return await this.gestureRecognitionService.getRecognitionStats();
  }
}
