/**
 * 设备会话实体
 * 
 * 存储设备的WebSocket连接会话信息
 */

import {
  Entity,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('device_sessions')
@Index(['userId'])
@Index(['deviceId'])
@Index(['isActive'])
export class DeviceSession {
  @PrimaryColumn('varchar', { length: 36 })
  sessionId: string;

  @Column('varchar', { length: 36 })
  @Index()
  userId: string;

  @Column('varchar', { length: 100 })
  @Index()
  deviceId: string;

  @Column('varchar', { length: 100 })
  socketId: string;

  @Column('varchar', { length: 45, nullable: true })
  ipAddress?: string;

  @Column('text', { nullable: true })
  userAgent?: string;

  @Column('boolean', { default: true })
  @Index()
  isActive: boolean;

  @Column('timestamp', { default: () => 'CURRENT_TIMESTAMP' })
  lastActivity: Date;

  @Column('timestamp', { nullable: true })
  connectedAt?: Date;

  @Column('timestamp', { nullable: true })
  disconnectedAt?: Date;

  @Column('int', { default: 0 })
  messagesSent: number;

  @Column('int', { default: 0 })
  messagesReceived: number;

  @Column('json', { nullable: true })
  connectionInfo?: {
    transport?: string;
    protocol?: string;
    latency?: number;
    bandwidth?: number;
  };

  @Column('varchar', { length: 20, nullable: true })
  disconnectReason?: string; // 'client' | 'server' | 'timeout' | 'error'

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
