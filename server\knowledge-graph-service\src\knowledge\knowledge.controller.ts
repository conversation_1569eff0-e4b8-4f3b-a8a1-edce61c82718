import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { KnowledgeGraphService, QueryResult } from './knowledge-graph.service';
import { CreateEntityDto } from './dto/create-entity.dto';
import { CreateRelationDto } from './dto/create-relation.dto';
import { QueryKnowledgeDto } from './dto/query-knowledge.dto';
import { UpdateEntityDto } from './dto/update-entity.dto';

@ApiTags('knowledge-graph')
@Controller('knowledge')
@ApiBearerAuth()
export class KnowledgeController {
  private readonly logger = new Logger(KnowledgeController.name);

  constructor(
    private readonly knowledgeGraphService: KnowledgeGraphService,
  ) {}

  @Post('entities')
  @ApiOperation({ summary: '添加知识实体' })
  @ApiResponse({ status: 201, description: '实体创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async createEntity(@Body() createEntityDto: CreateEntityDto) {
    try {
      const entityId = await this.knowledgeGraphService.addEntity(createEntityDto);
      return {
        success: true,
        data: { entityId },
        message: '知识实体创建成功',
      };
    } catch (error: any) {
      this.logger.error('创建知识实体失败', error);
      throw new HttpException(
        {
          success: false,
          message: '创建知识实体失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('relations')
  @ApiOperation({ summary: '添加知识关系' })
  @ApiResponse({ status: 201, description: '关系创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async createRelation(@Body() createRelationDto: CreateRelationDto) {
    try {
      const relationId = await this.knowledgeGraphService.addRelation(createRelationDto);
      return {
        success: true,
        data: { relationId },
        message: '知识关系创建成功',
      };
    } catch (error: any) {
      this.logger.error('创建知识关系失败', error);
      throw new HttpException(
        {
          success: false,
          message: '创建知识关系失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('query')
  @ApiOperation({ summary: '智能查询知识图谱' })
  @ApiResponse({ status: 200, description: '查询成功' })
  @ApiResponse({ status: 400, description: '查询参数错误' })
  async intelligentQuery(@Body() queryDto: QueryKnowledgeDto) {
    try {
      const result = await this.knowledgeGraphService.intelligentQuery(
        queryDto.query,
        queryDto.reasoning,
      );
      return {
        success: true,
        data: result,
        message: '智能查询完成',
      };
    } catch (error: any) {
      this.logger.error('智能查询失败', error);
      throw new HttpException(
        {
          success: false,
          message: '智能查询失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('entities/:id/similar')
  @ApiOperation({ summary: '查找相似实体' })
  @ApiParam({ name: 'id', description: '实体ID' })
  @ApiQuery({ name: 'threshold', description: '相似度阈值', required: false })
  @ApiResponse({ status: 200, description: '查询成功' })
  async findSimilarEntities(
    @Param('id') entityId: string,
    @Query('threshold') threshold?: number,
  ) {
    try {
      // 这里需要先获取实体，然后查找相似实体
      // 简化实现，实际项目中需要完善
      const similarEntities = [];
      return {
        success: true,
        data: similarEntities,
        message: '相似实体查询完成',
      };
    } catch (error: any) {
      this.logger.error('查找相似实体失败', error);
      throw new HttpException(
        {
          success: false,
          message: '查找相似实体失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('paths/:sourceId/:targetId')
  @ApiOperation({ summary: '查找实体间路径' })
  @ApiParam({ name: 'sourceId', description: '源实体ID' })
  @ApiParam({ name: 'targetId', description: '目标实体ID' })
  @ApiQuery({ name: 'maxDepth', description: '最大深度', required: false })
  @ApiResponse({ status: 200, description: '查询成功' })
  async findPaths(
    @Param('sourceId') sourceId: string,
    @Param('targetId') targetId: string,
    @Query('maxDepth') maxDepth?: number,
  ) {
    try {
      const paths = await this.knowledgeGraphService.findPaths(
        sourceId,
        targetId,
        maxDepth || 5,
      );
      return {
        success: true,
        data: paths,
        message: '路径查找完成',
      };
    } catch (error: any) {
      this.logger.error('查找路径失败', error);
      throw new HttpException(
        {
          success: false,
          message: '查找路径失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('inference')
  @ApiOperation({ summary: '执行知识推理' })
  @ApiResponse({ status: 200, description: '推理成功' })
  async performInference(@Body() facts: any[]) {
    try {
      const result = await this.knowledgeGraphService.performKnowledgeInference(facts);
      return {
        success: true,
        data: result,
        message: '知识推理完成',
      };
    } catch (error: any) {
      this.logger.error('知识推理失败', error);
      throw new HttpException(
        {
          success: false,
          message: '知识推理失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('statistics')
  @ApiOperation({ summary: '获取知识图谱统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getStatistics() {
    try {
      const statistics = await this.knowledgeGraphService.getStatistics();
      return {
        success: true,
        data: statistics,
        message: '统计信息获取成功',
      };
    } catch (error: any) {
      this.logger.error('获取统计信息失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取统计信息失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
