import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * 指标类型枚举
 */
export enum MetricType {
  SCHEDULING_PERFORMANCE = 'scheduling_performance',
  RESOURCE_UTILIZATION = 'resource_utilization',
  COST_EFFICIENCY = 'cost_efficiency',
  ENERGY_CONSUMPTION = 'energy_consumption',
  QUALITY_METRICS = 'quality_metrics',
  THROUGHPUT = 'throughput',
  RESPONSE_TIME = 'response_time',
  SYSTEM_HEALTH = 'system_health',
}

/**
 * 指标单位枚举
 */
export enum MetricUnit {
  PERCENTAGE = 'percentage',
  COUNT = 'count',
  TIME_SECONDS = 'seconds',
  TIME_MINUTES = 'minutes',
  TIME_HOURS = 'hours',
  CURRENCY = 'currency',
  ENERGY_KWH = 'kwh',
  RATIO = 'ratio',
}

/**
 * 性能指标实体
 */
@Entity('performance_metrics')
@Index(['metricType', 'timestamp'])
@Index(['source', 'timestamp'])
export class PerformanceMetric {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'metric_name', length: 100 })
  metricName: string;

  @Column({
    type: 'enum',
    enum: MetricType,
  })
  metricType: MetricType;

  @Column({ length: 100, comment: '指标来源' })
  source: string;

  @Column({ type: 'decimal', precision: 15, scale: 6, comment: '指标值' })
  value: number;

  @Column({
    type: 'enum',
    enum: MetricUnit,
    default: MetricUnit.COUNT,
  })
  unit: MetricUnit;

  @Column({ type: 'datetime' })
  timestamp: Date;

  @Column({ name: 'target_value', type: 'decimal', precision: 15, scale: 6, nullable: true, comment: '目标值' })
  targetValue: number;

  @Column({ name: 'threshold_min', type: 'decimal', precision: 15, scale: 6, nullable: true, comment: '最小阈值' })
  thresholdMin: number;

  @Column({ name: 'threshold_max', type: 'decimal', precision: 15, scale: 6, nullable: true, comment: '最大阈值' })
  thresholdMax: number;

  @Column({ name: 'is_anomaly', type: 'boolean', default: false, comment: '是否异常' })
  isAnomaly: boolean;

  @Column({ name: 'anomaly_score', type: 'decimal', precision: 5, scale: 4, nullable: true, comment: '异常评分' })
  anomalyScore: number;

  @Column({ type: 'json', nullable: true, comment: '标签' })
  tags: Record<string, string>;

  @Column({ type: 'json', nullable: true, comment: '维度信息' })
  dimensions: {
    dimension: string;
    value: string;
  }[];

  @Column({ name: 'aggregation_period', length: 20, nullable: true, comment: '聚合周期' })
  aggregationPeriod: string;

  @Column({ name: 'data_quality', type: 'decimal', precision: 3, scale: 2, default: 1.0, comment: '数据质量评分' })
  dataQuality: number;

  @Column({ type: 'text', nullable: true, comment: '备注' })
  notes: string;

  @Column({ type: 'json', nullable: true, comment: '扩展属性' })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
