import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';

import { EnergyManagementService } from './energy-management.service';
import { EnergyOptimizationService } from './energy-optimization.service';
import { EnergyMonitoringService } from './energy-monitoring.service';

/**
 * 能耗管理控制器
 */
@ApiTags('energy')
@Controller('energy')
export class EnergyController {
  constructor(
    private readonly energyService: EnergyManagementService,
    private readonly optimizationService: EnergyOptimizationService,
    private readonly monitoringService: EnergyMonitoringService,
  ) {}

  /**
   * 获取能耗数据
   */
  @Get('consumption')
  @ApiOperation({ summary: '获取能耗数据' })
  @ApiQuery({ name: 'deviceId', required: false, type: String })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiResponse({ status: 200, description: '能耗数据' })
  async getEnergyConsumption(
    @Query('deviceId') deviceId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit') limit: number = 100,
  ): Promise<any> {
    const consumption = await this.energyService.getEnergyConsumption({
      deviceId,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      limit,
    });

    return {
      success: true,
      message: '获取能耗数据成功',
      data: consumption,
    };
  }

  /**
   * 获取能耗统计
   */
  @Get('consumption/stats')
  @ApiOperation({ summary: '获取能耗统计信息' })
  @ApiQuery({ name: 'period', required: false, type: String })
  @ApiResponse({ status: 200, description: '能耗统计信息' })
  async getEnergyStats(
    @Query('period') period: string = 'day',
  ): Promise<any> {
    const stats = await this.energyService.getEnergyStats(period);

    return {
      success: true,
      message: '获取能耗统计成功',
      data: stats,
    };
  }

  /**
   * 生成能耗优化计划
   */
  @Post('optimization/plan')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '生成能耗优化计划' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        targetDevices: { type: 'array', items: { type: 'string' } },
        strategy: { type: 'string' },
        targetSavings: { type: 'number' },
        timeframe: { type: 'object' },
      },
    },
  })
  @ApiResponse({ status: 200, description: '优化计划生成成功' })
  async generateOptimizationPlan(@Body() request: any): Promise<any> {
    const plan = await this.optimizationService.generateOptimizationPlan(request);

    return {
      success: true,
      message: '能耗优化计划生成成功',
      data: plan,
    };
  }

  /**
   * 获取优化建议
   */
  @Get('optimization/suggestions')
  @ApiOperation({ summary: '获取能耗优化建议' })
  @ApiResponse({ status: 200, description: '优化建议' })
  async getOptimizationSuggestions(): Promise<any> {
    const suggestions = await this.optimizationService.getOptimizationSuggestions();

    return {
      success: true,
      message: '获取优化建议成功',
      data: suggestions,
    };
  }

  /**
   * 获取能耗监控数据
   */
  @Get('monitoring/dashboard')
  @ApiOperation({ summary: '获取能耗监控仪表板数据' })
  @ApiResponse({ status: 200, description: '监控仪表板数据' })
  async getMonitoringDashboard(): Promise<any> {
    const dashboard = await this.monitoringService.getMonitoringDashboard();

    return {
      success: true,
      message: '获取监控数据成功',
      data: dashboard,
    };
  }

  /**
   * 获取能效分析报告
   */
  @Get('analysis/efficiency')
  @ApiOperation({ summary: '获取能效分析报告' })
  @ApiQuery({ name: 'period', required: false, type: String })
  @ApiResponse({ status: 200, description: '能效分析报告' })
  async getEfficiencyAnalysis(
    @Query('period') period: string = 'week',
  ): Promise<any> {
    const analysis = await this.monitoringService.getEfficiencyAnalysis(period);

    return {
      success: true,
      message: '获取能效分析成功',
      data: analysis,
    };
  }

  /**
   * 获取碳足迹报告
   */
  @Get('analysis/carbon-footprint')
  @ApiOperation({ summary: '获取碳足迹报告' })
  @ApiQuery({ name: 'scope', required: false, type: String })
  @ApiResponse({ status: 200, description: '碳足迹报告' })
  async getCarbonFootprintReport(
    @Query('scope') scope: string = 'all',
  ): Promise<any> {
    const report = await this.monitoringService.getCarbonFootprintReport(scope);

    return {
      success: true,
      message: '获取碳足迹报告成功',
      data: report,
    };
  }
}
