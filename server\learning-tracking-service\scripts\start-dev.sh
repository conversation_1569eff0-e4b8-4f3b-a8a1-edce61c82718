#!/bin/bash

# 学习记录跟踪服务开发启动脚本

echo "🚀 启动学习记录跟踪服务开发环境..."

# 检查Node.js版本
NODE_VERSION=$(node -v)
echo "📦 Node.js版本: $NODE_VERSION"

# 检查npm版本
NPM_VERSION=$(npm -v)
echo "📦 npm版本: $NPM_VERSION"

# 检查环境配置文件
if [ ! -f .env ]; then
    echo "⚠️  未找到.env文件，复制.env.example..."
    cp .env.example .env
    echo "✅ 已创建.env文件，请根据需要修改配置"
fi

# 安装依赖
echo "📦 安装依赖..."
npm install

# 检查数据库连接
echo "🔍 检查数据库连接..."
# 这里可以添加数据库连接检查逻辑

# 检查Redis连接
echo "🔍 检查Redis连接..."
# 这里可以添加Redis连接检查逻辑

# 启动开发服务器
echo "🚀 启动开发服务器..."
npm run start:dev
