import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { InventoryService } from './inventory.service';
import { Inventory, InventoryTransaction } from './entities/inventory.entity';

/**
 * 库存管理控制器
 */
@ApiTags('inventory')
@Controller('inventory')
@ApiBearerAuth()
export class InventoryController {
  private readonly logger = new Logger(InventoryController.name);

  constructor(private readonly inventoryService: InventoryService) {}

  /**
   * 创建库存记录
   */
  @Post()
  @ApiOperation({ summary: '创建库存记录', description: '创建新的库存记录' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '库存记录创建成功',
    type: Inventory,
  })
  async createInventory(@Body() createInventoryDto: any): Promise<Inventory> {
    this.logger.log(`创建库存记录: ${createInventoryDto.materialCode}`);
    return await this.inventoryService.createInventory(createInventoryDto);
  }

  /**
   * 获取库存列表
   */
  @Get()
  @ApiOperation({ summary: '获取库存列表', description: '分页查询库存列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
  })
  async getInventories(@Query() query: any) {
    this.logger.log(`查询库存列表: 页码=${query.page}, 限制=${query.limit}`);
    return await this.inventoryService.getInventories(query);
  }

  /**
   * 获取库存详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取库存详情', description: '根据ID获取库存详细信息' })
  @ApiParam({ name: 'id', description: '库存ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    type: Inventory,
  })
  async getInventoryById(@Param('id') id: string): Promise<Inventory> {
    this.logger.log(`获取库存详情: ${id}`);
    return await this.inventoryService.getInventoryById(id);
  }

  /**
   * 更新库存
   */
  @Put(':id')
  @ApiOperation({ summary: '更新库存', description: '更新库存信息' })
  @ApiParam({ name: 'id', description: '库存ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '更新成功',
    type: Inventory,
  })
  async updateInventory(
    @Param('id') id: string,
    @Body() updateInventoryDto: any,
  ): Promise<Inventory> {
    this.logger.log(`更新库存: ${id}`);
    return await this.inventoryService.updateInventory(id, updateInventoryDto);
  }

  /**
   * 删除库存
   */
  @Delete(':id')
  @ApiOperation({ summary: '删除库存', description: '删除库存记录' })
  @ApiParam({ name: 'id', description: '库存ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '删除成功',
  })
  async deleteInventory(@Param('id') id: string): Promise<void> {
    this.logger.log(`删除库存: ${id}`);
    await this.inventoryService.deleteInventory(id);
  }

  /**
   * 入库操作
   */
  @Post('stock-in')
  @ApiOperation({ summary: '入库操作', description: '执行库存入库操作' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '入库成功',
    type: Inventory,
  })
  async stockIn(@Body() stockInDto: {
    materialCode: string;
    quantity: number;
    warehouseCode: string;
    operatedBy: string;
    reason?: string;
  }): Promise<Inventory> {
    this.logger.log(`入库操作: ${stockInDto.materialCode}, 数量: ${stockInDto.quantity}`);
    return await this.inventoryService.stockIn(
      stockInDto.materialCode,
      stockInDto.quantity,
      stockInDto.warehouseCode,
      stockInDto.operatedBy,
      stockInDto.reason,
    );
  }

  /**
   * 出库操作
   */
  @Post('stock-out')
  @ApiOperation({ summary: '出库操作', description: '执行库存出库操作' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '出库成功',
    type: Inventory,
  })
  async stockOut(@Body() stockOutDto: {
    materialCode: string;
    quantity: number;
    warehouseCode: string;
    operatedBy: string;
    reason?: string;
  }): Promise<Inventory> {
    this.logger.log(`出库操作: ${stockOutDto.materialCode}, 数量: ${stockOutDto.quantity}`);
    return await this.inventoryService.stockOut(
      stockOutDto.materialCode,
      stockOutDto.quantity,
      stockOutDto.warehouseCode,
      stockOutDto.operatedBy,
      stockOutDto.reason,
    );
  }

  /**
   * 库存预留
   */
  @Post('reserve')
  @ApiOperation({ summary: '库存预留', description: '预留库存数量' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '预留成功',
    type: Inventory,
  })
  async reserveStock(@Body() reserveDto: {
    materialCode: string;
    quantity: number;
    warehouseCode: string;
    operatedBy: string;
  }): Promise<Inventory> {
    this.logger.log(`库存预留: ${reserveDto.materialCode}, 数量: ${reserveDto.quantity}`);
    return await this.inventoryService.reserveStock(
      reserveDto.materialCode,
      reserveDto.quantity,
      reserveDto.warehouseCode,
      reserveDto.operatedBy,
    );
  }

  /**
   * 获取库存统计
   */
  @Get('stats/overview')
  @ApiOperation({ summary: '获取库存统计', description: '获取库存管理统计数据' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
  })
  async getInventoryStatistics() {
    this.logger.log('获取库存统计数据');
    return await this.inventoryService.getInventoryStatistics();
  }

  /**
   * 获取库存事务记录
   */
  @Get('transactions')
  @ApiOperation({ summary: '获取库存事务记录', description: '分页查询库存事务记录' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
  })
  async getTransactions(@Query() query: any) {
    this.logger.log(`查询库存事务记录: 页码=${query.page}, 限制=${query.limit}`);
    return await this.inventoryService.getTransactions(query);
  }
}
