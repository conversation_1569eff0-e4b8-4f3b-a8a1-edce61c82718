import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as os from 'os';

/**
 * 应用服务
 */
@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * 获取健康状态
   */
  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'intelligent-scheduling-service',
      version: '1.0.0',
      uptime: process.uptime(),
    };
  }

  /**
   * 获取服务信息
   */
  getServiceInfo() {
    return {
      name: 'intelligent-scheduling-service',
      description: '智能调度和优化服务 - 生产计划、资源配置、供应链协同',
      version: '1.0.0',
      environment: this.configService.get('NODE_ENV', 'development'),
      port: this.configService.get('PORT', 3015),
      features: [
        '智能生产调度',
        '资源优化配置',
        '供应链协同',
        '能耗智能管理',
        '实时调度优化',
        '多目标优化算法',
      ],
      algorithms: [
        '遗传算法',
        '模拟退火',
        '粒子群优化',
        '蚁群算法',
        '禁忌搜索',
        '线性规划',
        '约束规划',
      ],
    };
  }

  /**
   * 获取系统统计信息
   */
  async getSystemStats() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      system: {
        platform: os.platform(),
        arch: os.arch(),
        nodeVersion: process.version,
        uptime: os.uptime(),
        loadAverage: os.loadavg(),
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpuCount: os.cpus().length,
      },
      process: {
        pid: process.pid,
        uptime: process.uptime(),
        memoryUsage: {
          rss: memoryUsage.rss,
          heapTotal: memoryUsage.heapTotal,
          heapUsed: memoryUsage.heapUsed,
          external: memoryUsage.external,
        },
        cpuUsage: {
          user: cpuUsage.user,
          system: cpuUsage.system,
        },
      },
      timestamp: new Date().toISOString(),
    };
  }
}
