/**
 * 初始数据种子文件
 */

import { DataSource } from 'typeorm';
import { Logger } from '@nestjs/common';

export class InitialDataSeed {
  private readonly logger = new Logger(InitialDataSeed.name);

  async run(dataSource: DataSource): Promise<void> {
    this.logger.log('开始执行初始数据种子...');

    try {
      // 插入系统配置数据
      await this.seedSystemConfigs(dataSource);
      
      // 插入示例学习内容
      await this.seedSampleContent(dataSource);
      
      this.logger.log('初始数据种子执行完成');
    } catch (error) {
      this.logger.error('初始数据种子执行失败:', error);
      throw error;
    }
  }

  /**
   * 插入系统配置数据
   */
  private async seedSystemConfigs(dataSource: DataSource): Promise<void> {
    const queryRunner = dataSource.createQueryRunner();
    
    try {
      await queryRunner.query(`
        INSERT IGNORE INTO system_configs (id, config_key, config_value, description) VALUES
        (UUID(), 'recommendation_algorithm', '{"name": "personalized_hybrid", "version": "1.0", "enabled": true}', '推荐算法配置'),
        (UUID(), 'sync_settings', '{"batch_size": 50, "max_retries": 3, "retry_delay": 5000}', '数据同步设置'),
        (UUID(), 'profile_analysis', '{"min_data_points": 10, "analysis_depth": "detailed", "update_frequency": "daily"}', '用户画像分析配置'),
        (UUID(), 'xapi_settings', '{"version": "2.0.0", "timeout": 30000, "enable_validation": true}', 'xAPI协议设置'),
        (UUID(), 'learning_paths', '{"default_difficulty": "medium", "adaptive_learning": true, "personalization": true}', '学习路径配置'),
        (UUID(), 'notification_settings', '{"email_enabled": true, "push_enabled": true, "frequency": "daily"}', '通知设置');
      `);
      
      this.logger.log('系统配置数据插入完成');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 插入示例学习内容
   */
  private async seedSampleContent(dataSource: DataSource): Promise<void> {
    const queryRunner = dataSource.createQueryRunner();
    
    try {
      // 检查是否已存在内容数据
      const existingContent = await queryRunner.query(
        'SELECT COUNT(*) as count FROM contents'
      );
      
      if (existingContent[0].count > 0) {
        this.logger.log('学习内容数据已存在，跳过插入');
        return;
      }

      await queryRunner.query(`
        INSERT INTO contents (id, title, description, content_type, difficulty_level, tags, metadata, created_at, updated_at) VALUES
        (UUID(), 'JavaScript基础教程', 'JavaScript编程语言基础知识学习', 'course', 'beginner', '["javascript", "programming", "web"]', '{"duration": 120, "lessons": 10, "language": "zh-CN"}', NOW(), NOW()),
        (UUID(), 'React框架入门', 'React前端框架基础教程', 'course', 'intermediate', '["react", "frontend", "javascript"]', '{"duration": 180, "lessons": 15, "language": "zh-CN"}', NOW(), NOW()),
        (UUID(), 'Node.js后端开发', 'Node.js服务器端开发教程', 'course', 'intermediate', '["nodejs", "backend", "javascript"]', '{"duration": 200, "lessons": 18, "language": "zh-CN"}', NOW(), NOW()),
        (UUID(), '数据库设计原理', '关系型数据库设计基础', 'course', 'advanced', '["database", "sql", "design"]', '{"duration": 150, "lessons": 12, "language": "zh-CN"}', NOW(), NOW()),
        (UUID(), 'TypeScript进阶', 'TypeScript高级特性和最佳实践', 'course', 'advanced', '["typescript", "javascript", "programming"]', '{"duration": 160, "lessons": 14, "language": "zh-CN"}', NOW(), NOW());
      `);
      
      this.logger.log('示例学习内容插入完成');
    } finally {
      await queryRunner.release();
    }
  }
}
