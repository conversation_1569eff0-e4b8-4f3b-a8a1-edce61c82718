/**
 * 移动设备守卫
 * 
 * 验证请求是否来自已注册的移动设备
 */

import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Request } from 'express';
import { MobileDevice } from '../device/entities/mobile-device.entity';

@Injectable()
export class MobileDeviceGuard implements CanActivate {
  constructor(
    @InjectRepository(MobileDevice)
    private readonly deviceRepository: Repository<MobileDevice>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    
    const deviceId = request.headers['x-device-id'] as string;
    const userId = request.headers['x-user-id'] as string;

    if (!deviceId) {
      throw new BadRequestException('缺少设备ID');
    }

    if (!userId) {
      throw new BadRequestException('缺少用户ID');
    }

    // 验证设备是否存在且属于该用户
    const device = await this.deviceRepository.findOne({
      where: { deviceId, userId, isActive: true }
    });

    if (!device) {
      throw new UnauthorizedException('设备未注册或已停用');
    }

    // 更新设备最后活动时间
    await this.deviceRepository.update(
      { deviceId },
      { lastActivity: new Date() }
    );

    // 将设备信息附加到请求对象
    request['device'] = device;

    return true;
  }
}
