-- 工业数据采集服务数据库初始化脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS industrial_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE industrial_data;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'industrial'@'%' IDENTIFIED BY 'industrial123';
GRANT ALL PRIVILEGES ON industrial_data.* TO 'industrial'@'%';
FLUSH PRIVILEGES;

-- 设备表
CREATE TABLE IF NOT EXISTS devices (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    type ENUM('plc', 'sensor', 'actuator', 'robot', 'machine', 'gateway', 'hmi', 'drive') DEFAULT 'sensor',
    status ENUM('online', 'offline', 'error', 'maintenance', 'unknown') DEFAULT 'offline',
    protocol ENUM('modbus_tcp', 'modbus_rtu', 'opc_ua', 'mqtt', 'ethernet_ip', 'profinet', 'http', 'serial') DEFAULT 'modbus_tcp',
    ipAddress VARCHAR(100) NOT NULL,
    port INT DEFAULT 502,
    slaveId INT,
    configuration JSON,
    tags JSON,
    location VARCHAR(100),
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    serialNumber VARCHAR(100),
    lastConnected TIMESTAMP NULL,
    lastDataReceived TIMESTAMP NULL,
    errorCount INT DEFAULT 0,
    isActive BOOLEAN DEFAULT TRUE,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_device_type (type),
    INDEX idx_device_status (status),
    INDEX idx_device_protocol (protocol),
    INDEX idx_device_active (isActive)
);

-- 数据点表
CREATE TABLE IF NOT EXISTS data_points (
    id VARCHAR(36) PRIMARY KEY,
    device_id VARCHAR(36) NOT NULL,
    tag_id VARCHAR(100) NOT NULL,
    tag_name VARCHAR(200),
    timestamp TIMESTAMP(3) NOT NULL,
    value JSON NOT NULL,
    data_type VARCHAR(50) NOT NULL,
    quality VARCHAR(50) DEFAULT 'good',
    metadata JSON,
    unit VARCHAR(50),
    source_timestamp TIMESTAMP(3),
    collection_method VARCHAR(50) DEFAULT 'polling',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_device_tag_time (device_id, tag_id, timestamp),
    INDEX idx_timestamp (timestamp),
    INDEX idx_device_id (device_id),
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE
);

-- 采集任务表
CREATE TABLE IF NOT EXISTS collection_tasks (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    device_id VARCHAR(36) NOT NULL,
    device_config TEXT NOT NULL,
    tag_ids TEXT NOT NULL,
    interval INT DEFAULT 10,
    enabled BOOLEAN DEFAULT TRUE,
    last_execution TIMESTAMP NULL,
    success_count INT DEFAULT 0,
    error_count INT DEFAULT 0,
    last_error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_device_id (device_id),
    INDEX idx_enabled (enabled),
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE
);

-- 时序数据表
CREATE TABLE IF NOT EXISTS time_series_data (
    id VARCHAR(36) PRIMARY KEY,
    deviceId VARCHAR(36) NOT NULL,
    tagName VARCHAR(100) NOT NULL,
    tagDescription TEXT,
    value JSON NOT NULL,
    unit VARCHAR(50),
    quality ENUM('good', 'bad', 'uncertain', 'stale') DEFAULT 'good',
    timestamp TIMESTAMP(3) NOT NULL,
    metadata JSON,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_device_tag_time (deviceId, tagName, timestamp),
    INDEX idx_timestamp (timestamp),
    INDEX idx_device_time (deviceId, timestamp)
);

-- 告警规则表
CREATE TABLE IF NOT EXISTS alert_rules (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    device_id VARCHAR(36),
    tag_name VARCHAR(100),
    condition ENUM('greater_than', 'less_than', 'equals', 'not_equals', 'between', 'outside_range', 'rate_of_change', 'no_data') DEFAULT 'greater_than',
    thresholds JSON NOT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    evaluation_interval INT DEFAULT 60,
    notification_channels JSON,
    notification_config JSON,
    auto_resolve BOOLEAN DEFAULT TRUE,
    resolve_timeout INT DEFAULT 300,
    last_evaluation TIMESTAMP NULL,
    last_triggered TIMESTAMP NULL,
    trigger_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_device_id (device_id),
    INDEX idx_tag_name (tag_name),
    INDEX idx_status (status)
);

-- 告警实例表
CREATE TABLE IF NOT EXISTS alert_instances (
    id VARCHAR(36) PRIMARY KEY,
    rule_id VARCHAR(36) NOT NULL,
    device_id VARCHAR(36),
    tag_name VARCHAR(100),
    message VARCHAR(200) NOT NULL,
    description TEXT,
    severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
    status ENUM('triggered', 'acknowledged', 'resolved', 'suppressed') DEFAULT 'triggered',
    trigger_value JSON,
    threshold_value JSON,
    triggered_at TIMESTAMP NOT NULL,
    acknowledged_at TIMESTAMP NULL,
    acknowledged_by VARCHAR(100),
    resolved_at TIMESTAMP NULL,
    resolved_by VARCHAR(100),
    auto_resolved BOOLEAN DEFAULT FALSE,
    metadata JSON,
    notification_sent BOOLEAN DEFAULT FALSE,
    notification_attempts INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_rule_id (rule_id),
    INDEX idx_status (status),
    INDEX idx_triggered_at (triggered_at),
    INDEX idx_device_tag (device_id, tag_name),
    FOREIGN KEY (rule_id) REFERENCES alert_rules(id) ON DELETE CASCADE
);

-- 数据归档表
CREATE TABLE IF NOT EXISTS data_archives (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    archiveDate DATE NOT NULL,
    startTime TIMESTAMP NOT NULL,
    endTime TIMESTAMP NOT NULL,
    status ENUM('pending', 'in_progress', 'completed', 'failed') DEFAULT 'pending',
    compressionType ENUM('none', 'gzip', 'lz4', 'snappy') DEFAULT 'gzip',
    originalSize BIGINT DEFAULT 0,
    compressedSize BIGINT DEFAULT 0,
    recordCount INT DEFAULT 0,
    filePath VARCHAR(500),
    checksum VARCHAR(100),
    metadata JSON,
    startedAt TIMESTAMP NULL,
    completedAt TIMESTAMP NULL,
    errorMessage TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_archive_date (archiveDate)
);

-- 插入示例数据
INSERT IGNORE INTO devices (id, name, description, type, protocol, ipAddress, port, slaveId, location, manufacturer, model) VALUES
('550e8400-e29b-41d4-a716-446655440001', '温度传感器-01', '车间温度监测传感器', 'sensor', 'modbus_tcp', '*************', 502, 1, '生产车间A', 'Siemens', 'S7-1200'),
('550e8400-e29b-41d4-a716-446655440002', '压力传感器-01', '管道压力监测传感器', 'sensor', 'modbus_tcp', '*************', 502, 2, '生产车间A', 'Schneider', 'TM3'),
('550e8400-e29b-41d4-a716-446655440003', 'PLC控制器-01', '主控制器', 'plc', 'opc_ua', '*************', 4840, NULL, '控制室', 'Siemens', 'S7-1500'),
('550e8400-e29b-41d4-a716-446655440004', '机器人-01', '装配机器人', 'robot', 'ethernet_ip', '*************', 44818, NULL, '装配线', 'ABB', 'IRB-6700');

-- 插入示例采集任务
INSERT IGNORE INTO collection_tasks (id, name, description, device_id, device_config, tag_ids, interval, enabled) VALUES
('660e8400-e29b-41d4-a716-446655440001', '温度数据采集', '每5秒采集温度数据', '550e8400-e29b-41d4-a716-446655440001', '{"protocol":"modbus_tcp","timeout":5000}', '["40001","40002"]', 5, TRUE),
('660e8400-e29b-41d4-a716-446655440002', '压力数据采集', '每10秒采集压力数据', '550e8400-e29b-41d4-a716-446655440002', '{"protocol":"modbus_tcp","timeout":5000}', '["40003","40004"]', 10, TRUE);

-- 插入示例告警规则
INSERT IGNORE INTO alert_rules (id, name, description, device_id, tag_name, condition, thresholds, severity, status) VALUES
('770e8400-e29b-41d4-a716-446655440001', '高温告警', '温度超过80度时触发告警', '550e8400-e29b-41d4-a716-446655440001', 'temperature', 'greater_than', '{"value":80}', 'high', 'active'),
('770e8400-e29b-41d4-a716-446655440002', '低压告警', '压力低于1bar时触发告警', '550e8400-e29b-41d4-a716-446655440002', 'pressure', 'less_than', '{"value":1}', 'medium', 'active');

COMMIT;
