/**
 * 种子数据执行脚本
 */

import { config } from 'dotenv';
import { AppDataSource } from '../data-source';
import { InitialDataSeed } from './initial-data.seed';

// 加载环境变量
config();

async function runSeeds() {
  console.log('🌱 开始执行数据库种子...');

  try {
    // 初始化数据源
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');

    // 执行种子
    const initialDataSeed = new InitialDataSeed();
    await initialDataSeed.run(AppDataSource);

    console.log('🎉 所有种子执行完成');
  } catch (error) {
    console.error('❌ 种子执行失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('📦 数据库连接已关闭');
    }
  }
}

// 执行种子
runSeeds();
