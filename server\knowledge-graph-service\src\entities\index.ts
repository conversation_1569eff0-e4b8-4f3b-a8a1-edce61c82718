// 知识图谱实体
import { KnowledgeEntityModel } from '../knowledge/entities/knowledge-entity.entity';
import { KnowledgeRelationModel } from '../knowledge/entities/knowledge-relation.entity';
import { InferenceRuleModel } from '../knowledge/entities/inference-rule.entity';

// 导出所有实体
export const entities = [
  KnowledgeEntityModel,
  KnowledgeRelationModel,
  InferenceRuleModel,
];

// 单独导出实体类
export {
  KnowledgeEntityModel,
  KnowledgeRelationModel,
  InferenceRuleModel,
};
