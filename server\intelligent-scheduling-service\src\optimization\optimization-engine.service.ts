import { Injectable, Logger } from '@nestjs/common';

/**
 * 优化引擎服务
 */
@Injectable()
export class OptimizationEngineService {
  private readonly logger = new Logger(OptimizationEngineService.name);
  private optimizationHistory: any[] = [];

  /**
   * 获取优化历史
   */
  async getOptimizationHistory(limit: number, offset: number): Promise<any> {
    const start = Math.max(0, this.optimizationHistory.length - offset - limit);
    const end = Math.max(0, this.optimizationHistory.length - offset);
    
    return {
      history: this.optimizationHistory.slice(start, end).reverse(),
      total: this.optimizationHistory.length,
      limit,
      offset,
    };
  }

  /**
   * 生成优化建议
   */
  async generateOptimizationSuggestions(
    currentSolution: any,
    constraints: any,
  ): Promise<any> {
    try {
      this.logger.log('开始生成优化建议');

      const suggestions = [];

      // 分析当前方案的性能
      const performance = await this.analyzeCurrentPerformance(currentSolution);
      
      // 识别改进机会
      const improvements = await this.identifyImprovementOpportunities(
        currentSolution,
        performance,
        constraints,
      );

      suggestions.push(...improvements);

      // 算法建议
      const algorithmSuggestions = await this.generateAlgorithmSuggestions(
        currentSolution,
        performance,
      );

      suggestions.push(...algorithmSuggestions);

      return {
        totalSuggestions: suggestions.length,
        suggestions,
        currentPerformance: performance,
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`生成优化建议失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 分析当前性能
   */
  private async analyzeCurrentPerformance(solution: any): Promise<any> {
    return {
      makespan: solution?.makespan || 0,
      cost: solution?.totalCost || 0,
      utilization: solution?.utilization || 0,
      optimality: solution?.optimality || 0,
      feasibility: solution?.feasible || false,
    };
  }

  /**
   * 识别改进机会
   */
  private async identifyImprovementOpportunities(
    solution: any,
    performance: any,
    constraints: any,
  ): Promise<any[]> {
    const opportunities = [];

    // 完工时间优化
    if (performance.makespan > 0) {
      opportunities.push({
        type: 'makespan_optimization',
        priority: 'high',
        description: '通过并行化任务执行来减少完工时间',
        expectedImprovement: '10-20%',
        implementation: 'parallel_scheduling',
      });
    }

    // 成本优化
    if (performance.cost > 0) {
      opportunities.push({
        type: 'cost_optimization',
        priority: 'medium',
        description: '优化资源分配以降低总成本',
        expectedImprovement: '5-15%',
        implementation: 'resource_reallocation',
      });
    }

    // 利用率优化
    if (performance.utilization < 0.8) {
      opportunities.push({
        type: 'utilization_optimization',
        priority: 'medium',
        description: '提高资源利用率',
        expectedImprovement: `${((0.8 - performance.utilization) * 100).toFixed(1)}%`,
        implementation: 'load_balancing',
      });
    }

    return opportunities;
  }

  /**
   * 生成算法建议
   */
  private async generateAlgorithmSuggestions(
    solution: any,
    performance: any,
  ): Promise<any[]> {
    const suggestions = [];

    // 基于当前性能推荐算法
    if (performance.optimality < 0.7) {
      suggestions.push({
        type: 'algorithm_recommendation',
        priority: 'high',
        description: '当前最优性较低，建议使用遗传算法或模拟退火',
        recommendedAlgorithms: ['genetic_algorithm', 'simulated_annealing'],
        reason: '这些算法在复杂优化问题上表现更好',
      });
    }

    if (performance.makespan > 100) {
      suggestions.push({
        type: 'algorithm_recommendation',
        priority: 'medium',
        description: '对于大规模问题，建议使用启发式算法',
        recommendedAlgorithms: ['heuristic', 'greedy'],
        reason: '启发式算法在大规模问题上计算速度更快',
      });
    }

    return suggestions;
  }

  /**
   * 记录优化历史
   */
  recordOptimization(optimization: any): void {
    this.optimizationHistory.push({
      ...optimization,
      timestamp: new Date(),
    });

    // 保持历史记录在合理范围内
    if (this.optimizationHistory.length > 1000) {
      this.optimizationHistory = this.optimizationHistory.slice(-500);
    }
  }
}
