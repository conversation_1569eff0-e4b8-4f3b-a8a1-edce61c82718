import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';

import { OptimizationEngineService } from './optimization-engine.service';
import { AlgorithmService } from './algorithm.service';
import { PerformanceAnalysisService } from './performance-analysis.service';

/**
 * 优化控制器
 */
@ApiTags('optimization')
@Controller('optimization')
export class OptimizationController {
  constructor(
    private readonly optimizationService: OptimizationEngineService,
    private readonly algorithmService: AlgorithmService,
    private readonly performanceService: PerformanceAnalysisService,
  ) {}

  /**
   * 获取可用算法列表
   */
  @Get('algorithms')
  @ApiOperation({ summary: '获取可用优化算法列表' })
  @ApiResponse({ status: 200, description: '算法列表' })
  async getAvailableAlgorithms(): Promise<any> {
    const algorithms = await this.algorithmService.getAvailableAlgorithms();

    return {
      success: true,
      message: '获取算法列表成功',
      data: algorithms,
    };
  }

  /**
   * 算法性能比较
   */
  @Post('algorithms/compare')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '比较算法性能' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        algorithms: { type: 'array', items: { type: 'string' } },
        testData: { type: 'object' },
      },
    },
  })
  @ApiResponse({ status: 200, description: '算法性能比较结果' })
  async compareAlgorithms(@Body() request: any): Promise<any> {
    const comparison = await this.algorithmService.compareAlgorithms(
      request.algorithms,
      request.testData,
    );

    return {
      success: true,
      message: '算法性能比较完成',
      data: comparison,
    };
  }

  /**
   * 获取优化历史
   */
  @Get('history')
  @ApiOperation({ summary: '获取优化历史记录' })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'offset', required: false, type: Number })
  @ApiResponse({ status: 200, description: '优化历史记录' })
  async getOptimizationHistory(
    @Query('limit') limit: number = 20,
    @Query('offset') offset: number = 0,
  ): Promise<any> {
    const history = await this.optimizationService.getOptimizationHistory(
      limit,
      offset,
    );

    return {
      success: true,
      message: '获取优化历史成功',
      data: history,
    };
  }

  /**
   * 性能分析报告
   */
  @Get('performance/report')
  @ApiOperation({ summary: '获取性能分析报告' })
  @ApiQuery({ name: 'period', required: false, type: String })
  @ApiResponse({ status: 200, description: '性能分析报告' })
  async getPerformanceReport(
    @Query('period') period: string = 'week',
  ): Promise<any> {
    const report = await this.performanceService.generatePerformanceReport(period);

    return {
      success: true,
      message: '获取性能报告成功',
      data: report,
    };
  }

  /**
   * 优化建议
   */
  @Post('suggestions')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '获取优化建议' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        currentSolution: { type: 'object' },
        constraints: { type: 'object' },
      },
    },
  })
  @ApiResponse({ status: 200, description: '优化建议' })
  async getOptimizationSuggestions(@Body() request: any): Promise<any> {
    const suggestions = await this.optimizationService.generateOptimizationSuggestions(
      request.currentSolution,
      request.constraints,
    );

    return {
      success: true,
      message: '获取优化建议成功',
      data: suggestions,
    };
  }
}
