/**
 * 日志拦截器
 * 
 * 记录请求和响应的日志信息
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    
    const { method, url, ip } = request;
    const userAgent = request.get('User-Agent') || '';
    const deviceId = request.headers['x-device-id'] as string;
    const userId = request.headers['x-user-id'] as string;
    
    const startTime = Date.now();

    return next.handle().pipe(
      tap(() => {
        const { statusCode } = response;
        const contentLength = response.get('content-length');
        const responseTime = Date.now() - startTime;

        // 构建日志信息
        const logInfo = [
          `${method} ${url}`,
          `${statusCode}`,
          `${responseTime}ms`,
          contentLength ? `${contentLength}bytes` : '',
          `IP: ${ip}`,
          deviceId ? `Device: ${deviceId}` : '',
          userId ? `User: ${userId}` : '',
          userAgent ? `UA: ${userAgent}` : '',
        ].filter(Boolean).join(' - ');

        // 根据状态码选择日志级别
        if (statusCode >= 400) {
          this.logger.warn(logInfo);
        } else {
          this.logger.log(logInfo);
        }
      }),
    );
  }
}
