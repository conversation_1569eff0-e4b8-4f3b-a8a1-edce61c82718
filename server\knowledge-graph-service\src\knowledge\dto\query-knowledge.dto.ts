import { IsString, IsBoolean, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class QueryKnowledgeDto {
  @ApiProperty({
    description: '查询语句（自然语言或Cypher）',
    example: '查找所有数控机床的故障信息',
  })
  @IsString()
  query: string;

  @ApiProperty({
    description: '是否启用推理',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  reasoning?: boolean = true;
}
