import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { KnowledgeGraphService } from './knowledge-graph.service';
import { KnowledgeController } from './knowledge.controller';
import { KnowledgeGateway } from './knowledge.gateway';

// 实体
import { KnowledgeEntityModel } from './entities/knowledge-entity.entity';
import { KnowledgeRelationModel } from './entities/knowledge-relation.entity';
import { InferenceRuleModel } from './entities/inference-rule.entity';

@Module({
  imports: [
    ...(process.env.DB_HOST ? [
      TypeOrmModule.forFeature([
        KnowledgeEntityModel,
        KnowledgeRelationModel,
        InferenceRuleModel,
      ])
    ] : []),
  ],
  controllers: [KnowledgeController],
  providers: [
    KnowledgeGraphService,
    KnowledgeGateway,
  ],
  exports: [KnowledgeGraphService],
})
export class KnowledgeModule {}
