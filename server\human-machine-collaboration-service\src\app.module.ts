import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';

// 核心模块
import { CollaborationModule } from './collaboration/collaboration.module';
import { AIAssistantModule } from './ai-assistant/ai-assistant.module';
import { VoiceInteractionModule } from './voice-interaction/voice-interaction.module';
import { GestureRecognitionModule } from './gesture-recognition/gesture-recognition.module';
import { GatewayModule } from './gateway/gateway.module';

// 数据库实体
import { ARVRScene } from './database/entities/arvr-scene.entity';
import { UserSession } from './database/entities/user-session.entity';
import { InteractionLog } from './database/entities/interaction-log.entity';
import { AIConversation } from './database/entities/ai-conversation.entity';
import { VoiceCommand } from './database/entities/voice-command.entity';
import { GesturePattern } from './database/entities/gesture-pattern.entity';
import { CollaborationSession } from './database/entities/collaboration-session.entity';

// 控制器
import { AppController } from './controllers/app.controller';
import { ARVRController } from './controllers/arvr.controller';
import { AIAssistantController } from './controllers/ai-assistant.controller';
import { VoiceController } from './controllers/voice.controller';
import { GestureController } from './controllers/gesture.controller';

// 服务
import { AppService } from './app.service';

/**
 * 人机协作增强服务主模块
 * 整合AR/VR维护指导、智能助手、语音交互、手势识别等功能
 */
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get<string>('DB_HOST', 'localhost'),
        port: configService.get<number>('DB_PORT', 3306),
        username: configService.get<string>('DB_USERNAME', 'root'),
        password: configService.get<string>('DB_PASSWORD', ''),
        database: configService.get<string>('DB_DATABASE', 'human_machine_collaboration'),
        entities: [
          ARVRScene,
          UserSession,
          InteractionLog,
          AIConversation,
          VoiceCommand,
          GesturePattern,
          CollaborationSession,
        ],
        synchronize: configService.get<string>('NODE_ENV') === 'development',
        logging: configService.get<string>('NODE_ENV') === 'development',
        timezone: '+08:00',
        charset: 'utf8mb4',
        extra: {
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
        },
      }),
      inject: [ConfigService],
    }),

    // 定时任务模块
    ScheduleModule.forRoot(),

    // 功能模块
    CollaborationModule,
    AIAssistantModule,
    VoiceInteractionModule,
    GestureRecognitionModule,
    GatewayModule,
  ],
  controllers: [
    AppController,
    ARVRController,
    AIAssistantController,
    VoiceController,
    GestureController,
  ],
  providers: [
    AppService,
  ],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    // 输出模块加载信息
    console.log('🔧 人机协作增强服务模块已加载');
    console.log(`📊 数据库: ${this.configService.get('DB_HOST')}:${this.configService.get('DB_PORT')}`);
    console.log(`🔴 Redis: ${this.configService.get('REDIS_HOST')}:${this.configService.get('REDIS_PORT')}`);
  }
}
