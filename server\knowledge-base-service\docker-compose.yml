version: '3.8'

services:
  # 知识库服务
  knowledge-base-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dl-knowledge-base-service
    ports:
      - "3008:3008"  # 微服务端口
      - "4008:4008"  # HTTP API端口
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=password
      - DB_DATABASE=dl_knowledge_base
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CHROMA_URL=http://chromadb:8000
      - JWT_SECRET=knowledge-base-service-secret-key
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - mysql
      - redis
      - chromadb
    networks:
      - dl-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4008/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: dl-knowledge-base-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=dl_knowledge_base
      - MYSQL_USER=dl_user
      - MYSQL_PASSWORD=dl_password
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - dl-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: dl-knowledge-base-redis
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - dl-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # ChromaDB向量数据库
  chromadb:
    image: chromadb/chroma:latest
    container_name: dl-knowledge-base-chromadb
    ports:
      - "8001:8000"
    volumes:
      - chromadb_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    networks:
      - dl-network
    restart: unless-stopped

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  chromadb_data:
    driver: local

networks:
  dl-network:
    driver: bridge
    external: true
