/**
 * 学习记录跟踪服务应用服务
 */

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  constructor(private configService: ConfigService) {}

  /**
   * 获取服务信息
   */
  getServiceInfo() {
    return {
      name: '学习记录跟踪服务',
      description: '基于xAPI的学习数据采集、用户画像分析和个性化推荐服务',
      version: '1.0.0',
      environment: this.configService.get('NODE_ENV', 'development'),
      timestamp: new Date().toISOString(),
      features: [
        'xAPI数据采集',
        '学习记录管理',
        '用户画像分析',
        '个性化推荐',
        'Learninglocker集成',
        '数据同步',
        '队列处理'
      ],
      endpoints: {
        health: '/api/v1/health',
        docs: '/api/docs',
        tracking: '/api/v1/learning-tracking'
      }
    };
  }

  /**
   * 获取版本信息
   */
  getVersion() {
    return {
      version: '1.0.0',
      buildTime: new Date().toISOString(),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    };
  }
}
