import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete,
  Query,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe
} from '@nestjs/common';
import { AlertService } from './alert.service';
import { AlertRule } from './entities/alert-rule.entity';

@Controller('alerts')
export class AlertController {
  constructor(private readonly alertService: AlertService) {}

  /**
   * 创建告警规则
   */
  @Post('rules')
  @HttpCode(HttpStatus.CREATED)
  async createAlertRule(@Body() ruleData: Partial<AlertRule>) {
    const rule = await this.alertService.createAlertRule(ruleData);
    return {
      success: true,
      message: '告警规则创建成功',
      data: rule
    };
  }

  /**
   * 获取所有告警规则
   */
  @Get('rules')
  async getAllAlertRules() {
    const rules = await this.alertService.getAllAlertRules();
    return {
      success: true,
      message: '获取告警规则列表成功',
      data: rules
    };
  }

  /**
   * 根据设备获取告警规则
   */
  @Get('rules/device/:deviceId')
  async getAlertRulesByDevice(@Param('deviceId', ParseUUIDPipe) deviceId: string) {
    const rules = await this.alertService.getAlertRulesByDevice(deviceId);
    return {
      success: true,
      message: '获取设备告警规则成功',
      data: rules
    };
  }

  /**
   * 更新告警规则
   */
  @Patch('rules/:id')
  async updateAlertRule(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateData: Partial<AlertRule>
  ) {
    const rule = await this.alertService.updateAlertRule(id, updateData);
    return {
      success: true,
      message: '告警规则更新成功',
      data: rule
    };
  }

  /**
   * 删除告警规则
   */
  @Delete('rules/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteAlertRule(@Param('id', ParseUUIDPipe) id: string) {
    await this.alertService.deleteAlertRule(id);
    return {
      success: true,
      message: '告警规则删除成功'
    };
  }

  /**
   * 手动评估告警规则
   */
  @Post('rules/:id/evaluate')
  @HttpCode(HttpStatus.OK)
  async evaluateAlertRule(@Param('id', ParseUUIDPipe) id: string) {
    // 这里需要先获取规则，然后评估
    const rules = await this.alertService.getAllAlertRules();
    const rule = rules.find(r => r.id === id);
    
    if (!rule) {
      return {
        success: false,
        message: '告警规则不存在'
      };
    }

    const triggered = await this.alertService.evaluateAlertRule(rule);
    return {
      success: true,
      message: triggered ? '告警已触发' : '告警条件未满足',
      data: { triggered }
    };
  }

  /**
   * 获取活跃告警
   */
  @Get('active')
  async getActiveAlerts() {
    const alerts = await this.alertService.getActiveAlerts();
    return {
      success: true,
      message: '获取活跃告警成功',
      data: alerts
    };
  }

  /**
   * 确认告警
   */
  @Post('instances/:id/acknowledge')
  @HttpCode(HttpStatus.OK)
  async acknowledgeAlert(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('acknowledgedBy') acknowledgedBy: string
  ) {
    const instance = await this.alertService.acknowledgeAlert(id, acknowledgedBy);
    return {
      success: true,
      message: '告警确认成功',
      data: instance
    };
  }

  /**
   * 解决告警
   */
  @Post('instances/:id/resolve')
  @HttpCode(HttpStatus.OK)
  async resolveAlert(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('resolvedBy') resolvedBy?: string
  ) {
    const instance = await this.alertService.resolveAlert(id, resolvedBy);
    return {
      success: true,
      message: '告警解决成功',
      data: instance
    };
  }

  /**
   * 获取告警统计信息
   */
  @Get('statistics')
  async getAlertStatistics() {
    const statistics = await this.alertService.getAlertStatistics();
    return {
      success: true,
      message: '获取告警统计信息成功',
      data: statistics
    };
  }
}
