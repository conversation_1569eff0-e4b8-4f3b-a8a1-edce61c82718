{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@/collaboration/*": ["src/collaboration/*"], "@/ai-assistant/*": ["src/ai-assistant/*"], "@/voice-interaction/*": ["src/voice-interaction/*"], "@/gesture-recognition/*": ["src/gesture-recognition/*"], "@/database/*": ["src/database/*"], "@/gateway/*": ["src/gateway/*"], "@/controllers/*": ["src/controllers/*"], "@/common/*": ["src/common/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}