import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Param, 
  Query,
  ParseUUI<PERSON>ipe,
  BadRequestException
} from '@nestjs/common';
import { AnalyticsService, AnalyticsQuery } from './analytics.service';

@Controller('analytics')
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  /**
   * 统计分析
   */
  @Post('statistical')
  async performStatisticalAnalysis(@Body() query: AnalyticsQuery) {
    if (!query.startTime || !query.endTime) {
      throw new BadRequestException('开始时间和结束时间不能为空');
    }

    const result = await this.analyticsService.performStatisticalAnalysis({
      ...query,
      startTime: new Date(query.startTime),
      endTime: new Date(query.endTime)
    });

    return {
      success: true,
      message: '统计分析完成',
      data: result
    };
  }

  /**
   * 趋势分析
   */
  @Post('trend')
  async performTrendAnalysis(@Body() query: AnalyticsQuery) {
    if (!query.startTime || !query.endTime) {
      throw new BadRequestException('开始时间和结束时间不能为空');
    }

    const result = await this.analyticsService.performTrendAnalysis({
      ...query,
      startTime: new Date(query.startTime),
      endTime: new Date(query.endTime)
    });

    return {
      success: true,
      message: '趋势分析完成',
      data: result
    };
  }

  /**
   * 实时分析
   */
  @Get('realtime/:deviceId/:tagName')
  async performRealtimeAnalysis(
    @Param('deviceId', ParseUUIDPipe) deviceId: string,
    @Param('tagName') tagName: string,
    @Query('windowSize') windowSize?: string
  ) {
    const size = windowSize ? parseInt(windowSize) : 100;
    
    if (isNaN(size) || size < 1 || size > 1000) {
      throw new BadRequestException('窗口大小必须在1-1000之间');
    }

    const result = await this.analyticsService.performRealtimeAnalysis(deviceId, tagName, size);

    return {
      success: true,
      message: '实时分析完成',
      data: result
    };
  }

  /**
   * 设备效率分析
   */
  @Post('efficiency/:deviceId')
  async analyzeDeviceEfficiency(
    @Param('deviceId', ParseUUIDPipe) deviceId: string,
    @Body() body: { startTime: string; endTime: string }
  ) {
    if (!body.startTime || !body.endTime) {
      throw new BadRequestException('开始时间和结束时间不能为空');
    }

    const result = await this.analyticsService.analyzeDeviceEfficiency(
      deviceId,
      new Date(body.startTime),
      new Date(body.endTime)
    );

    return {
      success: true,
      message: '设备效率分析完成',
      data: result
    };
  }

  /**
   * 能耗分析
   */
  @Post('energy/:deviceId')
  async analyzeEnergyConsumption(
    @Param('deviceId', ParseUUIDPipe) deviceId: string,
    @Body() body: { startTime: string; endTime: string }
  ) {
    if (!body.startTime || !body.endTime) {
      throw new BadRequestException('开始时间和结束时间不能为空');
    }

    const result = await this.analyticsService.analyzeEnergyConsumption(
      deviceId,
      new Date(body.startTime),
      new Date(body.endTime)
    );

    return {
      success: true,
      message: '能耗分析完成',
      data: result
    };
  }
}
