import { Injectable, Logger } from '@nestjs/common';

/**
 * 通知服务
 */
@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  /**
   * 发送调度完成通知
   */
  async sendScheduleCompletionNotification(solution: any): Promise<void> {
    try {
      // 这里可以集成邮件、短信、企业微信等通知方式
      this.logger.log(`调度完成通知: 方案ID ${solution.solutionId}`);
      
      // 模拟发送通知
      await this.simulateNotificationSending({
        type: 'schedule_completion',
        title: '调度方案生成完成',
        message: `调度方案 ${solution.solutionId} 已生成完成`,
        data: solution,
      });
    } catch (error) {
      this.logger.error(`发送调度完成通知失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 发送优化警告通知
   */
  async sendOptimizationWarning(warning: any): Promise<void> {
    try {
      this.logger.warn(`优化警告通知: ${warning.message}`);
      
      await this.simulateNotificationSending({
        type: 'optimization_warning',
        title: '调度优化警告',
        message: warning.message,
        data: warning,
      });
    } catch (error) {
      this.logger.error(`发送优化警告通知失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 发送系统错误通知
   */
  async sendSystemErrorNotification(error: any): Promise<void> {
    try {
      this.logger.error(`系统错误通知: ${error.message}`);
      
      await this.simulateNotificationSending({
        type: 'system_error',
        title: '系统错误',
        message: error.message,
        data: error,
      });
    } catch (error) {
      this.logger.error(`发送系统错误通知失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 模拟发送通知
   */
  private async simulateNotificationSending(notification: any): Promise<void> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 100));
    
    this.logger.debug(`通知已发送: ${notification.type} - ${notification.title}`);
  }
}
