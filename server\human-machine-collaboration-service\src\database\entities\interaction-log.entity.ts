import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { UserSession } from './user-session.entity';

/**
 * 交互日志实体
 * 记录用户在AR/VR场景中的所有交互行为
 */
@Entity('interaction_logs')
@Index(['sessionId', 'timestamp'])
@Index(['interactionType', 'success'])
export class InteractionLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  sessionId: string;

  @Column({ type: 'datetime' })
  timestamp: Date;

  @Column({
    type: 'enum',
    enum: ['gesture', 'voice', 'gaze', 'touch', 'controller', 'mixed'],
  })
  interactionType: string;

  @Column({ type: 'varchar', length: 100 })
  action: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  target: string;

  @Column({ type: 'boolean' })
  success: boolean;

  @Column({ type: 'int', comment: '响应时间（毫秒）' })
  responseTime: number;

  @Column({ type: 'json', nullable: true })
  inputData: any;

  @Column({ type: 'json', nullable: true })
  outputData: any;

  @Column({ type: 'json', nullable: true })
  errorInfo: any;

  @Column({ type: 'float', nullable: true, comment: '置信度' })
  confidence: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  stepId: string;

  @Column({ type: 'json', nullable: true })
  contextData: any;

  @CreateDateColumn()
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => UserSession, (session) => session.interactions)
  @JoinColumn({ name: 'sessionId' })
  session: UserSession;
}
