import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { ResourceAllocation } from './resource-allocation.entity';

/**
 * 资源类型枚举
 */
export enum ResourceType {
  MACHINE = 'machine',
  WORKER = 'worker',
  MATERIAL = 'material',
  TOOL = 'tool',
  ENERGY = 'energy',
  SPACE = 'space',
  TRANSPORT = 'transport',
}

/**
 * 资源状态枚举
 */
export enum ResourceStatus {
  AVAILABLE = 'available',
  BUSY = 'busy',
  MAINTENANCE = 'maintenance',
  OFFLINE = 'offline',
  RESERVED = 'reserved',
}

/**
 * 资源实体
 */
@Entity('resources')
@Index(['type', 'status'])
@Index(['location'])
export class Resource {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'resource_id', length: 100, unique: true })
  resourceId: string;

  @Column({ length: 200 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: ResourceType,
  })
  type: ResourceType;

  @Column({
    type: 'enum',
    enum: ResourceStatus,
    default: ResourceStatus.AVAILABLE,
  })
  status: ResourceStatus;

  @Column({ length: 200, nullable: true })
  location: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true, comment: '容量' })
  capacity: number;

  @Column({ name: 'available_capacity', type: 'decimal', precision: 10, scale: 2, nullable: true, comment: '可用容量' })
  availableCapacity: number;

  @Column({ type: 'json', nullable: true, comment: '技能列表' })
  skills: string[];

  @Column({ name: 'hourly_cost', type: 'decimal', precision: 8, scale: 2, nullable: true, comment: '小时成本' })
  hourlyCost: number;

  @Column({ name: 'setup_cost', type: 'decimal', precision: 8, scale: 2, default: 0, comment: '准备成本' })
  setupCost: number;

  @Column({ name: 'energy_consumption', type: 'decimal', precision: 8, scale: 2, nullable: true, comment: '能耗(kWh)' })
  energyConsumption: number;

  @Column({ name: 'working_hours', type: 'json', nullable: true, comment: '工作时间' })
  workingHours: {
    start: string;
    end: string;
    days: string[];
  };

  @Column({ name: 'maintenance_schedule', type: 'json', nullable: true, comment: '维护计划' })
  maintenanceSchedule: {
    frequency: string;
    duration: number;
    lastMaintenance: Date;
    nextMaintenance: Date;
  };

  @Column({ type: 'json', nullable: true, comment: '扩展属性' })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => ResourceAllocation, allocation => allocation.resource)
  allocations: ResourceAllocation[];
}
