import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';

/**
 * 调度WebSocket网关
 */
@WebSocketGateway({
  cors: {
    origin: '*',
  },
  namespace: '/scheduling',
})
export class SchedulingGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(SchedulingGateway.name);
  private connectedClients = new Map<string, Socket>();

  /**
   * 客户端连接
   */
  handleConnection(client: Socket) {
    this.connectedClients.set(client.id, client);
    this.logger.log(`客户端连接: ${client.id}`);
    
    // 发送欢迎消息
    client.emit('connected', {
      message: '已连接到智能调度服务',
      clientId: client.id,
      timestamp: new Date(),
    });
  }

  /**
   * 客户端断开连接
   */
  handleDisconnect(client: Socket) {
    this.connectedClients.delete(client.id);
    this.logger.log(`客户端断开连接: ${client.id}`);
  }

  /**
   * 订阅调度更新
   */
  @SubscribeMessage('subscribe-schedule-updates')
  handleSubscribeScheduleUpdates(@ConnectedSocket() client: Socket) {
    client.join('schedule-updates');
    this.logger.log(`客户端 ${client.id} 订阅调度更新`);
    
    client.emit('subscription-confirmed', {
      type: 'schedule-updates',
      message: '已订阅调度更新',
    });
  }

  /**
   * 取消订阅调度更新
   */
  @SubscribeMessage('unsubscribe-schedule-updates')
  handleUnsubscribeScheduleUpdates(@ConnectedSocket() client: Socket) {
    client.leave('schedule-updates');
    this.logger.log(`客户端 ${client.id} 取消订阅调度更新`);
    
    client.emit('subscription-cancelled', {
      type: 'schedule-updates',
      message: '已取消订阅调度更新',
    });
  }

  /**
   * 订阅优化进度
   */
  @SubscribeMessage('subscribe-optimization-progress')
  handleSubscribeOptimizationProgress(@ConnectedSocket() client: Socket) {
    client.join('optimization-progress');
    this.logger.log(`客户端 ${client.id} 订阅优化进度`);
    
    client.emit('subscription-confirmed', {
      type: 'optimization-progress',
      message: '已订阅优化进度',
    });
  }

  /**
   * 广播调度方案更新
   */
  broadcastScheduleUpdate(solution: any) {
    this.server.to('schedule-updates').emit('schedule-updated', {
      solution,
      timestamp: new Date(),
      message: '调度方案已更新',
    });
    
    this.logger.log('广播调度方案更新');
  }

  /**
   * 广播优化进度
   */
  broadcastOptimizationProgress(progress: any) {
    this.server.to('optimization-progress').emit('optimization-progress', {
      ...progress,
      timestamp: new Date(),
    });
  }

  /**
   * 广播系统警告
   */
  broadcastSystemAlert(alert: any) {
    this.server.emit('system-alert', {
      ...alert,
      timestamp: new Date(),
    });
    
    this.logger.warn(`广播系统警告: ${alert.message}`);
  }

  /**
   * 发送给特定客户端
   */
  sendToClient(clientId: string, event: string, data: any) {
    const client = this.connectedClients.get(clientId);
    if (client) {
      client.emit(event, data);
    }
  }

  /**
   * 获取连接的客户端数量
   */
  getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }
}
