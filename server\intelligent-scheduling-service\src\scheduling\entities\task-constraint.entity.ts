import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ProductionTask } from './production-task.entity';

/**
 * 约束类型枚举
 */
export enum ConstraintType {
  TIME_WINDOW = 'time_window',
  RESOURCE_CONFLICT = 'resource_conflict',
  PRECEDENCE = 'precedence',
  SETUP_TIME = 'setup_time',
  SKILL_REQUIREMENT = 'skill_requirement',
  CAPACITY_LIMIT = 'capacity_limit',
  AVAILABILITY = 'availability',
  QUALITY_REQUIREMENT = 'quality_requirement',
  SAFETY_REQUIREMENT = 'safety_requirement',
  ENVIRONMENTAL = 'environmental',
}

/**
 * 约束优先级枚举
 */
export enum ConstraintPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  CRITICAL = 4,
}

/**
 * 约束状态枚举
 */
export enum ConstraintStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  VIOLATED = 'violated',
  SATISFIED = 'satisfied',
}

/**
 * 任务约束实体
 */
@Entity('task_constraints')
@Index(['taskId', 'type'])
@Index(['priority', 'status'])
@Index(['type', 'status'])
export class TaskConstraint {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'task_id', type: 'uuid' })
  taskId: string;

  @Column({ length: 200 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: ConstraintType,
  })
  type: ConstraintType;

  @Column({
    type: 'enum',
    enum: ConstraintPriority,
    default: ConstraintPriority.NORMAL,
  })
  priority: ConstraintPriority;

  @Column({
    type: 'enum',
    enum: ConstraintStatus,
    default: ConstraintStatus.ACTIVE,
  })
  status: ConstraintStatus;

  @Column({ type: 'json', comment: '约束参数' })
  parameters: any;

  @Column({ name: 'violation_penalty', type: 'decimal', precision: 10, scale: 2, default: 0, comment: '违反惩罚' })
  violationPenalty: number;

  @Column({ name: 'is_hard_constraint', type: 'boolean', default: true, comment: '是否硬约束' })
  isHardConstraint: boolean;

  @Column({ name: 'tolerance_level', type: 'decimal', precision: 5, scale: 2, default: 0, comment: '容忍度' })
  toleranceLevel: number;

  @Column({ name: 'effective_from', type: 'datetime', nullable: true, comment: '生效开始时间' })
  effectiveFrom: Date;

  @Column({ name: 'effective_to', type: 'datetime', nullable: true, comment: '生效结束时间' })
  effectiveTo: Date;

  @Column({ name: 'related_tasks', type: 'json', nullable: true, comment: '相关任务' })
  relatedTasks: string[];

  @Column({ name: 'related_resources', type: 'json', nullable: true, comment: '相关资源' })
  relatedResources: string[];

  @Column({ name: 'validation_rules', type: 'json', nullable: true, comment: '验证规则' })
  validationRules: any;

  @Column({ name: 'last_violation_time', type: 'datetime', nullable: true, comment: '最后违反时间' })
  lastViolationTime: Date;

  @Column({ name: 'violation_count', type: 'int', default: 0, comment: '违反次数' })
  violationCount: number;

  @Column({ name: 'satisfaction_score', type: 'decimal', precision: 5, scale: 2, nullable: true, comment: '满足度评分' })
  satisfactionScore: number;

  @Column({ type: 'json', nullable: true, comment: '扩展属性' })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => ProductionTask, task => task.constraints)
  @JoinColumn({ name: 'task_id' })
  task: ProductionTask;
}
