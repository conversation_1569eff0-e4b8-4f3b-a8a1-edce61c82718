import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions } from 'typeorm';
import { ProductionTask, TaskStatus } from './entities/production-task.entity';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';

/**
 * 任务管理服务
 */
@Injectable()
export class TaskManagementService {
  private readonly logger = new Logger(TaskManagementService.name);

  constructor(
    @InjectRepository(ProductionTask)
    private readonly taskRepository: Repository<ProductionTask>,
  ) {}

  /**
   * 创建任务
   */
  async createTask(createTaskDto: CreateTaskDto): Promise<ProductionTask> {
    try {
      // 检查任务ID是否已存在
      const existingTask = await this.taskRepository.findOne({
        where: { taskId: createTaskDto.taskId },
      });

      if (existingTask) {
        throw new BadRequestException(`任务ID ${createTaskDto.taskId} 已存在`);
      }

      // 验证依赖任务是否存在
      if (createTaskDto.dependencies && createTaskDto.dependencies.length > 0) {
        await this.validateDependencies(createTaskDto.dependencies);
      }

      const task = this.taskRepository.create({
        ...createTaskDto,
        status: TaskStatus.PENDING,
      });

      const savedTask = await this.taskRepository.save(task);
      this.logger.log(`任务创建成功: ${savedTask.taskId}`);

      return savedTask;
    } catch (error) {
      this.logger.error(`创建任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取任务列表
   */
  async getTasks(options: {
    status?: string;
    priority?: number;
    limit?: number;
    offset?: number;
  }): Promise<{ tasks: ProductionTask[]; total: number }> {
    try {
      const { status, priority, limit = 20, offset = 0 } = options;

      const queryOptions: FindManyOptions<ProductionTask> = {
        take: limit,
        skip: offset,
        order: { createdAt: 'DESC' },
        relations: ['constraints'],
      };

      if (status || priority !== undefined) {
        queryOptions.where = {};
        if (status) {
          queryOptions.where.status = status as TaskStatus;
        }
        if (priority !== undefined) {
          queryOptions.where.priority = priority;
        }
      }

      const [tasks, total] = await this.taskRepository.findAndCount(queryOptions);

      return { tasks, total };
    } catch (error) {
      this.logger.error(`获取任务列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据ID获取任务
   */
  async getTaskById(id: string): Promise<ProductionTask> {
    try {
      const task = await this.taskRepository.findOne({
        where: { id },
        relations: ['constraints', 'scheduledTasks'],
      });

      if (!task) {
        throw new NotFoundException(`任务不存在: ${id}`);
      }

      return task;
    } catch (error) {
      this.logger.error(`获取任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据任务ID获取任务
   */
  async getTaskByTaskId(taskId: string): Promise<ProductionTask> {
    try {
      const task = await this.taskRepository.findOne({
        where: { taskId },
        relations: ['constraints', 'scheduledTasks'],
      });

      if (!task) {
        throw new NotFoundException(`任务不存在: ${taskId}`);
      }

      return task;
    } catch (error) {
      this.logger.error(`获取任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新任务
   */
  async updateTask(id: string, updateTaskDto: UpdateTaskDto): Promise<ProductionTask> {
    try {
      const task = await this.getTaskById(id);

      // 验证状态转换
      if (updateTaskDto.status) {
        this.validateStatusTransition(task.status, updateTaskDto.status);
      }

      // 验证依赖任务
      if (updateTaskDto.dependencies && updateTaskDto.dependencies.length > 0) {
        await this.validateDependencies(updateTaskDto.dependencies);
      }

      Object.assign(task, updateTaskDto);
      const updatedTask = await this.taskRepository.save(task);

      this.logger.log(`任务更新成功: ${updatedTask.taskId}`);
      return updatedTask;
    } catch (error) {
      this.logger.error(`更新任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除任务
   */
  async deleteTask(id: string): Promise<void> {
    try {
      const task = await this.getTaskById(id);

      // 检查任务是否可以删除
      if (task.status === TaskStatus.IN_PROGRESS) {
        throw new BadRequestException('正在执行的任务不能删除');
      }

      await this.taskRepository.remove(task);
      this.logger.log(`任务删除成功: ${task.taskId}`);
    } catch (error) {
      this.logger.error(`删除任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取待调度任务
   */
  async getPendingTasks(): Promise<ProductionTask[]> {
    try {
      return await this.taskRepository.find({
        where: { status: TaskStatus.PENDING },
        order: { priority: 'DESC', dueDate: 'ASC' },
        relations: ['constraints'],
      });
    } catch (error) {
      this.logger.error(`获取待调度任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 批量更新任务状态
   */
  async batchUpdateTaskStatus(taskIds: string[], status: TaskStatus): Promise<void> {
    try {
      await this.taskRepository.update(
        { taskId: { $in: taskIds } as any },
        { status }
      );

      this.logger.log(`批量更新任务状态成功: ${taskIds.length} 个任务`);
    } catch (error) {
      this.logger.error(`批量更新任务状态失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 验证依赖任务
   */
  private async validateDependencies(dependencies: string[]): Promise<void> {
    for (const depTaskId of dependencies) {
      const depTask = await this.taskRepository.findOne({
        where: { taskId: depTaskId },
      });

      if (!depTask) {
        throw new BadRequestException(`依赖任务不存在: ${depTaskId}`);
      }
    }
  }

  /**
   * 验证状态转换
   */
  private validateStatusTransition(currentStatus: TaskStatus, newStatus: TaskStatus): void {
    const validTransitions: Record<TaskStatus, TaskStatus[]> = {
      [TaskStatus.PENDING]: [TaskStatus.SCHEDULED, TaskStatus.CANCELLED],
      [TaskStatus.SCHEDULED]: [TaskStatus.IN_PROGRESS, TaskStatus.DELAYED, TaskStatus.CANCELLED],
      [TaskStatus.IN_PROGRESS]: [TaskStatus.COMPLETED, TaskStatus.DELAYED, TaskStatus.CANCELLED],
      [TaskStatus.DELAYED]: [TaskStatus.IN_PROGRESS, TaskStatus.CANCELLED],
      [TaskStatus.COMPLETED]: [],
      [TaskStatus.CANCELLED]: [],
    };

    if (!validTransitions[currentStatus].includes(newStatus)) {
      throw new BadRequestException(
        `无效的状态转换: ${currentStatus} -> ${newStatus}`
      );
    }
  }
}
