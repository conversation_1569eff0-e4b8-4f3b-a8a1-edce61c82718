import { Module } from '@nestjs/common';
import { ProtocolService } from './protocol.service';
import { ProtocolController } from './protocol.controller';
import { ModbusDriver } from './drivers/modbus.driver';
import { OPCUADriver } from './drivers/opcua.driver';
import { MQTTDriver } from './drivers/mqtt.driver';

@Module({
  controllers: [ProtocolController],
  providers: [
    ProtocolService,
    ModbusDriver,
    OPCUADriver,
    MQTTDriver
  ],
  exports: [ProtocolService]
})
export class ProtocolModule {}
