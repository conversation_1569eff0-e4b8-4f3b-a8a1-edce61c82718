import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';

// 临时类型定义，替代 mediasoup
interface Worker {
  id: string;
  closed: boolean;
  close(): void;
  on(event: string, listener: (...args: any[]) => void): void;
  createWebRtcServer(options: any): Promise<WebRtcServer>;
  createRouter(options: any): Promise<Router>;
}

interface Router {
  id: string;
  closed: boolean;
  rtpCapabilities: RtpCapabilities;
  close(): void;
  createWebRtcTransport(options: any): Promise<WebRtcTransport>;
  canConsume(options: any): boolean;
}

interface WebRtcServer {
  id: string;
  closed: boolean;
  close(): void;
}

interface WebRtcTransport {
  id: string;
  closed: boolean;
  appData: any;
  iceParameters: any;
  iceCandidates: any[];
  dtlsParameters: any;
  sctpParameters: any;
  observer: {
    on(event: string, listener: (...args: any[]) => void): void;
  };
  close(): void;
  connect(options: any): Promise<void>;
  produce(options: any): Promise<Producer>;
  consume(options: any): Promise<Consumer>;
  produceData(options: any): Promise<any>;
  consumeData(options: any): Promise<any>;
  on(event: string, listener: (...args: any[]) => void): void;
}

interface Producer {
  id: string;
  closed: boolean;
  kind: string;
  close(): void;
  pause(): Promise<void>;
  resume(): Promise<void>;
  enableTraceEvent(types: string[]): void;
  on(event: string, listener: (...args: any[]) => void): void;
}

interface Consumer {
  id: string;
  closed: boolean;
  kind: string;
  type: string;
  producerId: string;
  rtpParameters: RtpParameters;
  close(): void;
  resume(): Promise<void>;
  pause(): Promise<void>;
  enableTraceEvent(types: string[]): void;
  setPreferredLayers(layers: { spatialLayer: number; temporalLayer: number }): void;
  on(event: string, listener: (...args: any[]) => void): void;
}

export interface RtpCapabilities {
  codecs: any[];
  headerExtensions: any[];
}

export interface RtpParameters {
  codecs: any[];
  headerExtensions: any[];
  encodings: any[];
  rtcp: any;
}

interface SctpCapabilities {
  numStreams: any;
}

/**
 * WebRTC服务
 * 负责管理WebRTC连接、媒体流处理等
 */
@Injectable()
export class WebRTCService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(WebRTCService.name);
  private workers: Worker[] = [];
  private routers: Router[] = [];
  private webRtcServers: WebRtcServer[] = [];
  private transports: Map<string, WebRtcTransport> = new Map();
  private producers: Map<string, Producer> = new Map();
  private consumers: Map<string, Consumer> = new Map();
  private readonly numWorkers: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.numWorkers = this.configService.get<number>('MEDIASOUP_NUM_WORKERS', 1);
  }

  /**
   * 模块初始化时创建mediasoup工作进程
   */
  async onModuleInit() {
    try {
      await this.createWorkers();
      this.logger.log(`已创建 ${this.workers.length} 个mediasoup工作进程`);
    } catch (error) {
      this.logger.error(`初始化mediasoup工作进程失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 模块销毁时关闭mediasoup工作进程
   */
  async onModuleDestroy() {
    this.closeAll();
  }

  /**
   * 创建mediasoup工作进程
   */
  private async createWorkers(): Promise<void> {
    for (let i = 0; i < this.numWorkers; i++) {
      // 模拟创建工作进程
      const worker: Worker = {
        id: `worker-${i}`,
        closed: false,
        close: () => {},
        on: () => {},
        createWebRtcServer: async (options: any) => ({
          id: `webrtc-server-${i}`,
          closed: false,
          close: () => {},
        }),
        createRouter: async (options: any) => ({
          id: `router-${i}`,
          closed: false,
          rtpCapabilities: {
            codecs: [],
            headerExtensions: [],
          },
          close: () => {},
          createWebRtcTransport: async (options: any) => ({
            id: `transport-${Date.now()}`,
            closed: false,
            appData: options.appData || {},
            iceParameters: {},
            iceCandidates: [],
            dtlsParameters: {},
            sctpParameters: {},
            observer: {
              on: () => {}
            },
            close: () => {},
            connect: async () => {},
            produce: async () => ({
              id: `producer-${Date.now()}`,
              closed: false,
              kind: 'video',
              close: () => {},
              pause: async () => {},
              resume: async () => {},
              enableTraceEvent: () => {},
              on: () => {},
            }),
            consume: async () => ({
              id: `consumer-${Date.now()}`,
              closed: false,
              kind: 'video',
              type: 'simple',
              producerId: `producer-${Date.now()}`,
              rtpParameters: {
                codecs: [],
                headerExtensions: [],
                encodings: [],
                rtcp: {},
              },
              close: () => {},
              resume: async () => {},
              pause: async () => {},
              enableTraceEvent: () => {},
              setPreferredLayers: () => {},
              on: () => {},
            }),
            produceData: async () => ({
              id: `data-producer-${Date.now()}`,
              closed: false,
              close: () => {},
              send: () => {},
              on: () => {},
            }),
            consumeData: async () => ({
              id: `data-consumer-${Date.now()}`,
              closed: false,
              dataProducerId: `data-producer-${Date.now()}`,
              label: '',
              protocol: '',
              close: () => {},
              on: () => {},
            }),
            on: () => {},
          }),
          canConsume: () => true,
        }),
      };

      worker.on('died', () => {
        this.logger.error(`mediasoup工作进程 ${i} 意外终止`);
        this.eventEmitter.emit('webrtc.worker.died', { workerId: i });

        // 模拟重新创建工作进程
        setTimeout(async () => {
          try {
            this.workers[i] = worker; // 简化处理
            this.logger.log(`mediasoup工作进程 ${i} 已重新创建`);
          } catch (error) {
            this.logger.error(`重新创建mediasoup工作进程 ${i} 失败: ${error.message}`, error.stack);
          }
        }, 5000);
      });

      this.workers.push(worker);

      // 创建WebRTC服务器
      await this.createWebRtcServer(worker, i);

      // 创建路由器
      await this.createRouter(worker, i);
    }
  }

  /**
   * 创建WebRTC服务器
   */
  private async createWebRtcServer(worker: Worker, index: number): Promise<void> {
    try {
      const listenIps = [
        {
          ip: '0.0.0.0',
          announcedIp: this.configService.get<string>('MEDIASOUP_ANNOUNCED_IP', '127.0.0.1'),
        },
      ];

      const port = this.configService.get<number>('MEDIASOUP_RTC_START_PORT', 10000) + index;

      const webRtcServer = await worker.createWebRtcServer({
        listenInfos: [
          {
            protocol: 'udp',
            ip: '0.0.0.0',
            announcedIp: this.configService.get<string>('MEDIASOUP_ANNOUNCED_IP', '127.0.0.1'),
            port,
          },
          {
            protocol: 'tcp',
            ip: '0.0.0.0',
            announcedIp: this.configService.get<string>('MEDIASOUP_ANNOUNCED_IP', '127.0.0.1'),
            port,
          },
        ],
      });

      this.webRtcServers.push(webRtcServer);
      this.logger.log(`已创建WebRTC服务器 ${index}, 端口: ${port}`);
    } catch (error) {
      this.logger.error(`创建WebRTC服务器失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 创建路由器
   */
  private async createRouter(worker: Worker, index: number): Promise<void> {
    try {
      const mediaCodecs = [
        {
          kind: 'audio',
          mimeType: 'audio/opus',
          clockRate: 48000,
          channels: 2,
        },
        {
          kind: 'video',
          mimeType: 'video/VP8',
          clockRate: 90000,
          parameters: {
            'x-google-start-bitrate': 1000,
          },
        },
        {
          kind: 'video',
          mimeType: 'video/VP9',
          clockRate: 90000,
          parameters: {
            'profile-id': 2,
            'x-google-start-bitrate': 1000,
          },
        },
        {
          kind: 'video',
          mimeType: 'video/h264',
          clockRate: 90000,
          parameters: {
            'packetization-mode': 1,
            'profile-level-id': '4d0032',
            'level-asymmetry-allowed': 1,
            'x-google-start-bitrate': 1000,
          },
        },
      ];

      const router = await worker.createRouter({ mediaCodecs });
      this.routers.push(router);
      this.logger.log(`已创建路由器 ${index}`);
    } catch (error) {
      this.logger.error(`创建路由器失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 创建优化的WebRTC传输（支持100+用户）
   */
  async createWebRtcTransport(routerIndex: number = 0): Promise<WebRtcTransport> {
    if (routerIndex >= this.routers.length) {
      throw new Error(`路由器索引超出范围: ${routerIndex}`);
    }

    try {
      const router = this.routers[routerIndex];
      const webRtcServer = this.webRtcServers[routerIndex];

      // 优化的传输配置，支持更高并发
      const transport = await router.createWebRtcTransport({
        webRtcServer,
        enableUdp: true,
        enableTcp: true,
        preferUdp: true,
        // 提高带宽配置以支持更多用户
        initialAvailableOutgoingBitrate: 2000000, // 2Mbps
        minimumAvailableOutgoingBitrate: 800000,  // 800Kbps
        maxIncomingBitrate: 5000000, // 5Mbps
        // 优化SCTP配置
        maxSctpMessageSize: 262144,
        sctpSendBufferSize: 262144,
        // 启用传输拥塞控制
        enableSctp: true,
        numSctpStreams: { OS: 1024, MIS: 1024 },
        // 应用数据
        appData: {
          routerIndex,
          createdAt: new Date(),
          maxUsers: 100, // 支持100用户
        },
      });

      // 存储传输
      this.transports.set(transport.id, transport);

      // 设置优化的传输事件监听器
      this.setupEnhancedTransportListeners(transport);

      this.logger.log(`已创建优化WebRTC传输: ${transport.id}，路由器: ${routerIndex}`);

      return transport;
    } catch (error) {
      this.logger.error(`创建WebRTC传输失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 设置增强的传输事件监听器
   */
  private setupEnhancedTransportListeners(transport: WebRtcTransport): void {
    // 原有的监听器
    this.setupTransportListeners(transport);

    // 新增的性能监控监听器
    transport.observer.on('newproducer', (producer) => {
      this.logger.debug(`传输 ${transport.id} 新增生产者: ${producer.id}`);
      this.optimizeProducerSettings(producer);
    });

    transport.observer.on('newconsumer', (consumer) => {
      this.logger.debug(`传输 ${transport.id} 新增消费者: ${consumer.id}`);
      this.optimizeConsumerSettings(consumer);
    });

    // 监控传输拥塞
    transport.on('icestatechange', (iceState) => {
      if (iceState === 'disconnected' || iceState === 'failed') {
        this.logger.warn(`传输 ${transport.id} ICE状态异常: ${iceState}`);
        this.handleTransportIssue(transport);
      }
    });
  }

  /**
   * 设置传输事件监听器
   */
  private setupTransportListeners(transport: WebRtcTransport): void {
    transport.on('sctpstatechange', (sctpState) => {
      this.logger.log(`传输 ${transport.id} SCTP状态变更为 ${sctpState}`);
    });

    transport.on('dtlsstatechange', (dtlsState) => {
      if (dtlsState === 'closed') {
        this.logger.log(`传输 ${transport.id} DTLS状态关闭`);
        this.closeTransport(transport.id);
      }
    });
  }

  /**
   * 优化生产者设置
   */
  private optimizeProducerSettings(producer: Producer): void {
    try {
      // 根据媒体类型优化设置
      if (producer.kind === 'video') {
        // 视频优化：启用时间层和空间层
        producer.enableTraceEvent(['rtp', 'keyframe', 'nack', 'pli', 'fir']);
      } else if (producer.kind === 'audio') {
        // 音频优化：启用DTX和FEC
        producer.enableTraceEvent(['rtp', 'silence']);
      }

      this.logger.debug(`已优化生产者 ${producer.id} 设置`);
    } catch (error) {
      this.logger.error(`优化生产者设置失败: ${error.message}`);
    }
  }

  /**
   * 优化消费者设置
   */
  private optimizeConsumerSettings(consumer: Consumer): void {
    try {
      // 根据媒体类型优化设置
      if (consumer.kind === 'video') {
        // 视频消费者优化：自适应比特率
        consumer.enableTraceEvent(['rtp', 'keyframe', 'nack', 'pli', 'fir']);

        // 设置首选层（如果支持）
        if (consumer.type === 'simulcast') {
          consumer.setPreferredLayers({ spatialLayer: 2, temporalLayer: 2 });
        }
      } else if (consumer.kind === 'audio') {
        // 音频消费者优化
        consumer.enableTraceEvent(['rtp', 'silence']);
      }

      this.logger.debug(`已优化消费者 ${consumer.id} 设置`);
    } catch (error) {
      this.logger.error(`优化消费者设置失败: ${error.message}`);
    }
  }

  /**
   * 处理传输问题
   */
  private handleTransportIssue(transport: WebRtcTransport): void {
    // 实现传输问题的处理逻辑
    // 例如：重连、降级质量、切换路由器等
    this.logger.warn(`处理传输 ${transport.id} 的连接问题`);

    // 发出传输问题事件
    this.eventEmitter.emit('webrtc.transport.issue', {
      transportId: transport.id,
      timestamp: new Date(),
    });
  }

  /**
   * 关闭传输
   */
  closeTransport(transportId: string): void {
    const transport = this.transports.get(transportId);

    if (!transport) {
      this.logger.warn(`传输不存在: ${transportId}`);
      return;
    }

    try {
      transport.close();
      this.transports.delete(transportId);
      this.logger.log(`已关闭传输: ${transportId}`);
    } catch (error) {
      this.logger.error(`关闭传输失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 关闭所有资源
   */
  closeAll(): void {
    // 关闭所有消费者
    for (const consumer of this.consumers.values()) {
      consumer.close();
    }
    this.consumers.clear();

    // 关闭所有生产者
    for (const producer of this.producers.values()) {
      producer.close();
    }
    this.producers.clear();

    // 关闭所有传输
    for (const transport of this.transports.values()) {
      transport.close();
    }
    this.transports.clear();

    // 关闭所有WebRTC服务器
    for (const webRtcServer of this.webRtcServers) {
      webRtcServer.close();
    }
    this.webRtcServers = [];

    // 关闭所有路由器
    this.routers = [];

    // 关闭所有工作进程
    for (const worker of this.workers) {
      worker.close();
    }
    this.workers = [];

    this.logger.log('已关闭所有WebRTC资源');
  }

  /**
   * 获取工作进程
   */
  getWorkers(): Worker[] {
    return this.workers;
  }

  /**
   * 获取路由器
   */
  getRouters(): Router[] {
    return this.routers;
  }

  /**
   * 获取WebRTC服务器
   */
  getWebRtcServers(): WebRtcServer[] {
    return this.webRtcServers;
  }

  /**
   * 获取传输
   * @param transportId 传输ID
   */
  getTransport(transportId: string): WebRtcTransport | undefined {
    return this.transports.get(transportId);
  }

  /**
   * 获取所有传输
   */
  getAllTransports(): Map<string, WebRtcTransport> {
    return this.transports;
  }

  /**
   * 连接传输
   * @param transportId 传输ID
   * @param dtlsParameters DTLS参数
   */
  async connectTransport(transportId: string, dtlsParameters: any): Promise<boolean> {
    const transport = this.transports.get(transportId);

    if (!transport) {
      this.logger.error(`连接传输失败: 找不到传输 ${transportId}`);
      return false;
    }

    try {
      await transport.connect({ dtlsParameters });
      this.logger.log(`已连接传输: ${transportId}`);

      // 触发事件
      this.eventEmitter.emit('webrtc.transport.connected', {
        transportId,
      });

      return true;
    } catch (error) {
      this.logger.error(`连接传输失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 创建生产者
   * @param transportId 传输ID
   * @param rtpParameters RTP参数
   * @param kind 媒体类型
   * @param appData 应用数据
   */
  async createProducer(
    transportId: string,
    rtpParameters: RtpParameters,
    kind: 'audio' | 'video',
    appData: any = {},
  ): Promise<Producer | null> {
    const transport = this.transports.get(transportId);

    if (!transport) {
      this.logger.error(`创建生产者失败: 找不到传输 ${transportId}`);
      return null;
    }

    try {
      const producer = await transport.produce({
        kind,
        rtpParameters,
        appData,
      });

      // 存储生产者
      this.producers.set(producer.id, producer);

      // 设置生产者事件监听器
      this.setupProducerListeners(producer);

      this.logger.log(`已创建生产者: ${producer.id}, 类型: ${kind}`);

      // 触发事件
      this.eventEmitter.emit('webrtc.producer.created', {
        producerId: producer.id,
        transportId,
        kind,
      });

      return producer;
    } catch (error) {
      this.logger.error(`创建生产者失败: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 创建消费者
   * @param transportId 传输ID
   * @param producerId 生产者ID
   * @param rtpCapabilities RTP能力
   * @param appData 应用数据
   */
  async createConsumer(
    transportId: string,
    producerId: string,
    rtpCapabilities: RtpCapabilities,
    appData: any = {},
  ): Promise<Consumer | null> {
    const transport = this.transports.get(transportId);

    if (!transport) {
      this.logger.error(`创建消费者失败: 找不到传输 ${transportId}`);
      return null;
    }

    const producer = this.producers.get(producerId);

    if (!producer) {
      this.logger.error(`创建消费者失败: 找不到生产者 ${producerId}`);
      return null;
    }

    // 检查路由器是否可以消费
    const router = this.routers[transport.appData.routerIndex as number];
    if (!router.canConsume({ producerId, rtpCapabilities })) {
      this.logger.error(`创建消费者失败: 路由器无法消费生产者 ${producerId}`);
      return null;
    }

    try {
      const consumer = await transport.consume({
        producerId,
        rtpCapabilities,
        appData,
        paused: true, // 默认暂停，等待客户端准备好
      });

      // 存储消费者
      this.consumers.set(consumer.id, consumer);

      // 设置消费者事件监听器
      this.setupConsumerListeners(consumer);

      this.logger.log(`已创建消费者: ${consumer.id}, 生产者: ${producerId}`);

      // 触发事件
      this.eventEmitter.emit('webrtc.consumer.created', {
        consumerId: consumer.id,
        producerId,
        transportId,
      });

      return consumer;
    } catch (error) {
      this.logger.error(`创建消费者失败: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 设置生产者事件监听器
   * @param producer 生产者
   */
  private setupProducerListeners(producer: Producer): void {
    producer.on('transportclose', () => {
      this.logger.log(`生产者传输关闭: ${producer.id}`);
      this.producers.delete(producer.id);

      // 触发事件
      this.eventEmitter.emit('webrtc.producer.closed', {
        producerId: producer.id,
      });
    });

    producer.on('close', () => {
      this.logger.log(`生产者关闭: ${producer.id}`);
      this.producers.delete(producer.id);

      // 触发事件
      this.eventEmitter.emit('webrtc.producer.closed', {
        producerId: producer.id,
      });
    });

    producer.on('score', (score) => {
      // 触发事件
      this.eventEmitter.emit('webrtc.producer.score', {
        producerId: producer.id,
        score,
      });
    });

    producer.on('videoorientationchange', (videoOrientation) => {
      // 触发事件
      this.eventEmitter.emit('webrtc.producer.videoorientationchange', {
        producerId: producer.id,
        videoOrientation,
      });
    });
  }

  /**
   * 设置消费者事件监听器
   * @param consumer 消费者
   */
  private setupConsumerListeners(consumer: Consumer): void {
    consumer.on('transportclose', () => {
      this.logger.log(`消费者传输关闭: ${consumer.id}`);
      this.consumers.delete(consumer.id);

      // 触发事件
      this.eventEmitter.emit('webrtc.consumer.closed', {
        consumerId: consumer.id,
      });
    });

    consumer.on('close', () => {
      this.logger.log(`消费者关闭: ${consumer.id}`);
      this.consumers.delete(consumer.id);

      // 触发事件
      this.eventEmitter.emit('webrtc.consumer.closed', {
        consumerId: consumer.id,
      });
    });

    consumer.on('producerclose', () => {
      this.logger.log(`消费者生产者关闭: ${consumer.id}`);
      this.consumers.delete(consumer.id);

      // 触发事件
      this.eventEmitter.emit('webrtc.consumer.producerClosed', {
        consumerId: consumer.id,
      });
    });

    consumer.on('producerpause', () => {
      // 触发事件
      this.eventEmitter.emit('webrtc.consumer.producerPaused', {
        consumerId: consumer.id,
      });
    });

    consumer.on('producerresume', () => {
      // 触发事件
      this.eventEmitter.emit('webrtc.consumer.producerResumed', {
        consumerId: consumer.id,
      });
    });

    consumer.on('score', (score) => {
      // 触发事件
      this.eventEmitter.emit('webrtc.consumer.score', {
        consumerId: consumer.id,
        score,
      });
    });

    consumer.on('layerschange', (layers) => {
      // 触发事件
      this.eventEmitter.emit('webrtc.consumer.layersChange', {
        consumerId: consumer.id,
        layers,
      });
    });
  }

  /**
   * 获取路由器的RTP能力
   * @param routerIndex 路由器索引
   */
  getRtpCapabilities(routerIndex: number = 0): RtpCapabilities | null {
    if (routerIndex >= this.routers.length) {
      this.logger.error(`获取RTP能力失败: 路由器索引超出范围 ${routerIndex}`);
      return null;
    }

    return this.routers[routerIndex].rtpCapabilities;
  }

  /**
   * 暂停生产者
   * @param producerId 生产者ID
   */
  async pauseProducer(producerId: string): Promise<boolean> {
    const producer = this.producers.get(producerId);

    if (!producer) {
      this.logger.error(`暂停生产者失败: 找不到生产者 ${producerId}`);
      return false;
    }

    try {
      await producer.pause();
      this.logger.log(`已暂停生产者: ${producerId}`);

      // 触发事件
      this.eventEmitter.emit('webrtc.producer.paused', {
        producerId,
      });

      return true;
    } catch (error) {
      this.logger.error(`暂停生产者失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 恢复生产者
   * @param producerId 生产者ID
   */
  async resumeProducer(producerId: string): Promise<boolean> {
    const producer = this.producers.get(producerId);

    if (!producer) {
      this.logger.error(`恢复生产者失败: 找不到生产者 ${producerId}`);
      return false;
    }

    try {
      await producer.resume();
      this.logger.log(`已恢复生产者: ${producerId}`);

      // 触发事件
      this.eventEmitter.emit('webrtc.producer.resumed', {
        producerId,
      });

      return true;
    } catch (error) {
      this.logger.error(`恢复生产者失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 暂停消费者
   * @param consumerId 消费者ID
   */
  async pauseConsumer(consumerId: string): Promise<boolean> {
    const consumer = this.consumers.get(consumerId);

    if (!consumer) {
      this.logger.error(`暂停消费者失败: 找不到消费者 ${consumerId}`);
      return false;
    }

    try {
      await consumer.pause();
      this.logger.log(`已暂停消费者: ${consumerId}`);

      // 触发事件
      this.eventEmitter.emit('webrtc.consumer.paused', {
        consumerId,
      });

      return true;
    } catch (error) {
      this.logger.error(`暂停消费者失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 恢复消费者
   * @param consumerId 消费者ID
   */
  async resumeConsumer(consumerId: string): Promise<boolean> {
    const consumer = this.consumers.get(consumerId);

    if (!consumer) {
      this.logger.error(`恢复消费者失败: 找不到消费者 ${consumerId}`);
      return false;
    }

    try {
      await consumer.resume();
      this.logger.log(`已恢复消费者: ${consumerId}`);

      // 触发事件
      this.eventEmitter.emit('webrtc.consumer.resumed', {
        consumerId,
      });

      return true;
    } catch (error) {
      this.logger.error(`恢复消费者失败: ${error.message}`, error.stack);
      return false;
    }
  }
}
