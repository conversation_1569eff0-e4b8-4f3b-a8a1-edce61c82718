{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@/scheduling/*": ["src/scheduling/*"], "@/resource/*": ["src/resource/*"], "@/optimization/*": ["src/optimization/*"], "@/supply-chain/*": ["src/supply-chain/*"], "@/energy/*": ["src/energy/*"], "@/monitoring/*": ["src/monitoring/*"], "@/websocket/*": ["src/websocket/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}