/**
 * 请求日志拦截器
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    
    const { method, url, ip, headers } = request;
    const userAgent = headers['user-agent'] || '';
    const startTime = Date.now();

    // 记录请求开始
    this.logger.log(
      `${method} ${url} - ${ip} - ${userAgent} - Request started`
    );

    return next.handle().pipe(
      tap({
        next: (data) => {
          const endTime = Date.now();
          const duration = endTime - startTime;
          const { statusCode } = response;

          // 记录请求完成
          this.logger.log(
            `${method} ${url} - ${statusCode} - ${duration}ms - Request completed`
          );
        },
        error: (error) => {
          const endTime = Date.now();
          const duration = endTime - startTime;

          // 记录请求错误
          this.logger.error(
            `${method} ${url} - ${error.status || 500} - ${duration}ms - Request failed: ${error.message}`
          );
        },
      })
    );
  }
}
