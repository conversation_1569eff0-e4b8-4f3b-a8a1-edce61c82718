import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { HttpExceptionFilter, AllExceptionsFilter } from './common/filters/http-exception.filter';
import { ResponseInterceptor } from './common/interceptors/response.interceptor';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';

/**
 * 监控服务启动入口
 */
async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 获取配置服务
  const configService = app.get(ConfigService);

  // 启用全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));

  // 启用全局过滤器
  app.useGlobalFilters(new AllExceptionsFilter(), new HttpExceptionFilter());

  // 启用全局拦截器
  app.useGlobalInterceptors(new LoggingInterceptor(), new ResponseInterceptor());

  // 启用CORS
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*'),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  });

  // 设置全局前缀
  app.setGlobalPrefix('api/v1');

  // 配置Swagger文档
  const config = new DocumentBuilder()
    .setTitle('监控服务 API')
    .setDescription('监控服务提供系统监控、告警管理、健康检查、日志分析和通知功能')
    .setVersion('1.0')
    .addTag('monitoring', '监控管理')
    .addTag('alerts', '告警管理')
    .addTag('health', '健康检查')
    .addTag('logs', '日志管理')
    .addTag('notifications', '通知管理')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // 获取端口配置
  const port = configService.get<number>('PORT', 3003);

  // 启动服务
  await app.listen(port);

  console.log(`监控服务已启动，端口: ${port}`);
  console.log(`健康检查地址: http://localhost:${port}/api/v1/health`);
  console.log(`Prometheus指标地址: http://localhost:${port}/metrics`);
  console.log(`API文档地址: http://localhost:${port}/api/docs`);
}

bootstrap().catch(error => {
  console.error('监控服务启动失败:', error);
  process.exit(1);
});
