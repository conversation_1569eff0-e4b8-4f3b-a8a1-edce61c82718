import {
  Controller,
  Post,
  Body,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ExpertService } from './expert.service';
import { FaultDiagnosisDto } from './dto/fault-diagnosis.dto';
import { MaintenanceRecommendationDto } from './dto/maintenance-recommendation.dto';
import { OptimizationRecommendationDto } from './dto/optimization-recommendation.dto';
import { TrainingRecommendationDto } from './dto/training-recommendation.dto';

@ApiTags('expert')
@Controller('expert')
@ApiBearerAuth()
export class ExpertController {
  private readonly logger = new Logger(ExpertController.name);

  constructor(
    private readonly expertService: ExpertService,
  ) {}

  @Post('fault-diagnosis')
  @ApiOperation({ summary: '获取故障诊断建议' })
  @ApiResponse({ status: 200, description: '诊断建议获取成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async getFaultDiagnosisRecommendations(@Body() faultDiagnosisDto: FaultDiagnosisDto) {
    try {
      const recommendations = await this.expertService.getFaultDiagnosisRecommendations(
        faultDiagnosisDto.equipmentId,
        faultDiagnosisDto.symptoms,
      );

      return {
        success: true,
        data: recommendations,
        message: '故障诊断建议获取成功',
      };
    } catch (error: any) {
      this.logger.error('获取故障诊断建议失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取故障诊断建议失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('maintenance')
  @ApiOperation({ summary: '获取维护建议' })
  @ApiResponse({ status: 200, description: '维护建议获取成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async getMaintenanceRecommendations(@Body() maintenanceDto: MaintenanceRecommendationDto) {
    try {
      const recommendations = await this.expertService.getMaintenanceRecommendations(
        maintenanceDto.equipmentId,
        maintenanceDto.maintenanceHistory,
      );

      return {
        success: true,
        data: recommendations,
        message: '维护建议获取成功',
      };
    } catch (error: any) {
      this.logger.error('获取维护建议失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取维护建议失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('optimization')
  @ApiOperation({ summary: '获取优化建议' })
  @ApiResponse({ status: 200, description: '优化建议获取成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async getOptimizationRecommendations(@Body() optimizationDto: OptimizationRecommendationDto) {
    try {
      const recommendations = await this.expertService.getOptimizationRecommendations(
        optimizationDto.processId,
        optimizationDto.performanceData,
      );

      return {
        success: true,
        data: recommendations,
        message: '优化建议获取成功',
      };
    } catch (error: any) {
      this.logger.error('获取优化建议失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取优化建议失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('training')
  @ApiOperation({ summary: '获取培训建议' })
  @ApiResponse({ status: 200, description: '培训建议获取成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async getTrainingRecommendations(@Body() trainingDto: TrainingRecommendationDto) {
    try {
      const recommendations = await this.expertService.getTrainingRecommendations(
        trainingDto.skillGaps,
        trainingDto.userProfile,
      );

      return {
        success: true,
        data: recommendations,
        message: '培训建议获取成功',
      };
    } catch (error: any) {
      this.logger.error('获取培训建议失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取培训建议失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
