/**
 * 移动端同步模块
 * 
 * 整合移动端数据同步相关的功能
 */

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';

// 控制器
import { MobileSyncController } from '../controllers/mobile-sync.controller';

// 服务
import { MobileSyncService } from '../services/mobile-sync.service';

// 网关
import { MobileSyncGateway } from '../gateways/mobile-sync.gateway';

// 实体
import { SyncRecord } from './entities/sync-record.entity';
import { ConflictRecord } from './entities/conflict-record.entity';
import { MobileDevice } from '../device/entities/mobile-device.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SyncRecord,
      ConflictRecord,
      MobileDevice,
    ]),
    JwtModule,
  ],
  controllers: [MobileSyncController],
  providers: [
    MobileSyncService,
    MobileSyncGateway,
  ],
  exports: [
    MobileSyncService,
    MobileSyncGateway,
  ],
})
export class MobileSyncModule {}
