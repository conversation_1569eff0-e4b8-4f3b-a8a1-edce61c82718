import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { IsString, IsNumber, IsEnum, IsOptional, IsDateString, IsArray, IsObject, Min, Max, Length, IsUUID } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { OrderStatus, OrderPriority } from '../entities/production-order.entity';

/**
 * 创建生产订单DTO
 */
export class CreateOrderDto {
  @ApiProperty({ description: '订单编号', example: 'PO202406300001' })
  @IsString()
  @Length(1, 50)
  orderNumber: string;

  @ApiProperty({ description: '产品编码', example: 'PROD001' })
  @IsString()
  @Length(1, 100)
  productCode: string;

  @ApiProperty({ description: '产品名称', example: '智能手机外壳' })
  @IsString()
  @Length(1, 200)
  productName: string;

  @ApiPropertyOptional({ description: '产品规格', example: '尺寸: 150x75x8mm, 材质: 铝合金' })
  @IsOptional()
  @IsString()
  productSpecification?: string;

  @ApiProperty({ description: '计划数量', example: 1000, minimum: 1 })
  @IsNumber()
  @Min(1)
  plannedQuantity: number;

  @ApiPropertyOptional({ description: '优先级', enum: OrderPriority, example: OrderPriority.NORMAL })
  @IsOptional()
  @IsEnum(OrderPriority)
  priority?: OrderPriority;

  @ApiPropertyOptional({ description: '计划开始时间', example: '2024-07-01T08:00:00Z' })
  @IsOptional()
  @IsDateString()
  plannedStartTime?: string;

  @ApiPropertyOptional({ description: '计划完成时间', example: '2024-07-15T18:00:00Z' })
  @IsOptional()
  @IsDateString()
  plannedEndTime?: string;

  @ApiPropertyOptional({ description: '工艺路线ID' })
  @IsOptional()
  @IsUUID()
  processRouteId?: string;

  @ApiPropertyOptional({ description: '生产线ID' })
  @IsOptional()
  @IsUUID()
  productionLineId?: string;

  @ApiPropertyOptional({ description: '客户编码', example: 'CUST001' })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  customerCode?: string;

  @ApiPropertyOptional({ description: '客户名称', example: '华为技术有限公司' })
  @IsOptional()
  @IsString()
  @Length(1, 200)
  customerName?: string;

  @ApiPropertyOptional({ description: '交货日期', example: '2024-07-20' })
  @IsOptional()
  @IsDateString()
  deliveryDate?: string;

  @ApiPropertyOptional({ description: '订单备注' })
  @IsOptional()
  @IsString()
  remarks?: string;

  @ApiPropertyOptional({ description: '订单配置', example: { color: 'black', storage: '128GB' } })
  @IsOptional()
  @IsObject()
  configuration?: any;

  @ApiPropertyOptional({ description: '质量要求', example: { defectRate: 0.01, testStandard: 'ISO9001' } })
  @IsOptional()
  @IsObject()
  qualityRequirements?: any;

  @ApiPropertyOptional({ description: '物料清单', example: [{ materialCode: 'MAT001', quantity: 2 }] })
  @IsOptional()
  @IsArray()
  billOfMaterials?: any[];

  @ApiPropertyOptional({ description: '成本预算', example: 50000.00 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  costBudget?: number;

  @ApiProperty({ description: '创建人', example: 'admin' })
  @IsString()
  @Length(1, 100)
  createdBy: string;
}

/**
 * 更新生产订单DTO
 */
export class UpdateOrderDto extends PartialType(CreateOrderDto) {
  @ApiPropertyOptional({ description: '订单状态', enum: OrderStatus })
  @IsOptional()
  @IsEnum(OrderStatus)
  status?: OrderStatus;

  @ApiPropertyOptional({ description: '已完成数量', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  completedQuantity?: number;

  @ApiPropertyOptional({ description: '合格数量', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  qualifiedQuantity?: number;

  @ApiPropertyOptional({ description: '不合格数量', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  defectiveQuantity?: number;

  @ApiPropertyOptional({ description: '实际开始时间' })
  @IsOptional()
  @IsDateString()
  actualStartTime?: string;

  @ApiPropertyOptional({ description: '实际完成时间' })
  @IsOptional()
  @IsDateString()
  actualEndTime?: string;

  @ApiPropertyOptional({ description: '实际成本', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  actualCost?: number;

  @ApiPropertyOptional({ description: '更新人' })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  updatedBy?: string;
}

/**
 * 订单查询DTO
 */
export class OrderQueryDto {
  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: '订单编号' })
  @IsOptional()
  @IsString()
  orderNumber?: string;

  @ApiPropertyOptional({ description: '产品编码' })
  @IsOptional()
  @IsString()
  productCode?: string;

  @ApiPropertyOptional({ description: '产品名称' })
  @IsOptional()
  @IsString()
  productName?: string;

  @ApiPropertyOptional({ description: '订单状态', enum: OrderStatus })
  @IsOptional()
  @IsEnum(OrderStatus)
  status?: OrderStatus;

  @ApiPropertyOptional({ description: '优先级', enum: OrderPriority })
  @IsOptional()
  @IsEnum(OrderPriority)
  priority?: OrderPriority;

  @ApiPropertyOptional({ description: '客户编码' })
  @IsOptional()
  @IsString()
  customerCode?: string;

  @ApiPropertyOptional({ description: '客户名称' })
  @IsOptional()
  @IsString()
  customerName?: string;

  @ApiPropertyOptional({ description: '生产线ID' })
  @IsOptional()
  @IsUUID()
  productionLineId?: string;

  @ApiPropertyOptional({ description: '开始日期', example: '2024-07-01' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '结束日期', example: '2024-07-31' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: '是否逾期', example: true })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  isOverdue?: boolean;

  @ApiPropertyOptional({ description: '排序字段', example: 'createdAt' })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', example: 'DESC', enum: ['ASC', 'DESC'] })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * 批量操作DTO
 */
export class BatchOrderDto {
  @ApiProperty({ description: '订单ID列表', example: ['uuid1', 'uuid2'] })
  @IsArray()
  @IsUUID('4', { each: true })
  orderIds: string[];

  @ApiPropertyOptional({ description: '操作类型', example: 'start', enum: ['start', 'pause', 'complete', 'cancel'] })
  @IsOptional()
  @IsEnum(['start', 'pause', 'complete', 'cancel'])
  action?: string;

  @ApiPropertyOptional({ description: '操作人' })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  operatedBy?: string;
}

/**
 * 订单统计查询DTO
 */
export class OrderStatsQueryDto {
  @ApiPropertyOptional({ description: '统计开始日期', example: '2024-07-01' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '统计结束日期', example: '2024-07-31' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: '生产线ID' })
  @IsOptional()
  @IsUUID()
  productionLineId?: string;

  @ApiPropertyOptional({ description: '客户编码' })
  @IsOptional()
  @IsString()
  customerCode?: string;

  @ApiPropertyOptional({ description: '统计维度', example: 'daily', enum: ['daily', 'weekly', 'monthly'] })
  @IsOptional()
  @IsEnum(['daily', 'weekly', 'monthly'])
  dimension?: string = 'daily';
}
