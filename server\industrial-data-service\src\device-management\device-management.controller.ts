import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  Query, 
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
  ValidationPipe
} from '@nestjs/common';
import { DeviceManagementService } from './device-management.service';
import { CreateDeviceDto } from './dto/create-device.dto';
import { UpdateDeviceDto } from './dto/update-device.dto';
import { DeviceQueryDto } from './dto/device-query.dto';
import { DeviceStatus } from './entities/device.entity';

@Controller('devices')
export class DeviceManagementController {
  constructor(private readonly deviceManagementService: DeviceManagementService) {}

  /**
   * 创建设备
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createDeviceDto: CreateDeviceDto) {
    const device = await this.deviceManagementService.create(createDeviceDto);
    return {
      success: true,
      message: '设备创建成功',
      data: device
    };
  }

  /**
   * 获取设备列表
   */
  @Get()
  async findAll(@Query(new ValidationPipe({ transform: true })) query: DeviceQueryDto) {
    const result = await this.deviceManagementService.findAll(query);
    return {
      success: true,
      message: '获取设备列表成功',
      data: result.devices,
      pagination: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: Math.ceil(result.total / result.limit)
      }
    };
  }

  /**
   * 获取设备统计信息
   */
  @Get('statistics')
  async getStatistics() {
    const statistics = await this.deviceManagementService.getStatistics();
    return {
      success: true,
      message: '获取设备统计信息成功',
      data: statistics
    };
  }

  /**
   * 根据ID获取设备详情
   */
  @Get(':id')
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    const device = await this.deviceManagementService.findOne(id);
    return {
      success: true,
      message: '获取设备详情成功',
      data: device
    };
  }

  /**
   * 更新设备信息
   */
  @Patch(':id')
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDeviceDto: UpdateDeviceDto
  ) {
    const device = await this.deviceManagementService.update(id, updateDeviceDto);
    return {
      success: true,
      message: '设备更新成功',
      data: device
    };
  }

  /**
   * 删除设备
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id', ParseUUIDPipe) id: string) {
    await this.deviceManagementService.remove(id);
    return {
      success: true,
      message: '设备删除成功'
    };
  }

  /**
   * 更新设备状态
   */
  @Patch(':id/status')
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('status') status: DeviceStatus
  ) {
    const device = await this.deviceManagementService.updateStatus(id, status);
    return {
      success: true,
      message: '设备状态更新成功',
      data: device
    };
  }

  /**
   * 设备连接测试
   */
  @Post(':id/test-connection')
  async testConnection(@Param('id', ParseUUIDPipe) id: string) {
    // TODO: 实现设备连接测试逻辑
    const device = await this.deviceManagementService.findOne(id);
    
    // 模拟连接测试
    const isConnected = Math.random() > 0.3; // 70%成功率
    
    if (isConnected) {
      await this.deviceManagementService.updateStatus(id, DeviceStatus.ONLINE);
      return {
        success: true,
        message: '设备连接测试成功',
        data: {
          deviceId: id,
          connected: true,
          responseTime: Math.floor(Math.random() * 100) + 10
        }
      };
    } else {
      await this.deviceManagementService.updateStatus(id, DeviceStatus.ERROR);
      return {
        success: false,
        message: '设备连接测试失败',
        data: {
          deviceId: id,
          connected: false,
          error: '连接超时或设备不可达'
        }
      };
    }
  }

  /**
   * 批量更新设备状态
   */
  @Patch('batch/status')
  async batchUpdateStatus(
    @Body() body: { deviceIds: string[]; status: DeviceStatus }
  ) {
    const { deviceIds, status } = body;
    const results = [];

    for (const deviceId of deviceIds) {
      try {
        const device = await this.deviceManagementService.updateStatus(deviceId, status);
        results.push({
          deviceId,
          success: true,
          device
        });
      } catch (error) {
        results.push({
          deviceId,
          success: false,
          error: error.message
        });
      }
    }

    return {
      success: true,
      message: '批量更新设备状态完成',
      data: results
    };
  }
}
