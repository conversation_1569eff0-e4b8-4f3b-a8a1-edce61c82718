import { IsArray, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class TrainingRecommendationDto {
  @ApiProperty({
    description: '技能缺口列表',
    example: ['数控编程', '设备维护', '质量控制'],
  })
  @IsArray()
  skillGaps: string[];

  @ApiProperty({
    description: '用户档案',
    example: {
      userId: 'user_001',
      currentSkills: ['基础操作'],
      experience: 2,
      role: 'operator',
    },
  })
  @IsObject()
  userProfile: any;
}
