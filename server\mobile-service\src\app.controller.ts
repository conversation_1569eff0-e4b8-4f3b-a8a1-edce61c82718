/**
 * 移动端服务主控制器
 * 
 * 提供服务基本信息和状态接口
 */

import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('服务信息')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  /**
   * 获取服务信息
   */
  @Get()
  @ApiOperation({ summary: '获取移动端服务信息' })
  @ApiResponse({ status: 200, description: '服务信息获取成功' })
  getServiceInfo() {
    return this.appService.getServiceInfo();
  }

  /**
   * 获取服务版本
   */
  @Get('version')
  @ApiOperation({ summary: '获取服务版本信息' })
  @ApiResponse({ status: 200, description: '版本信息获取成功' })
  getVersion() {
    return this.appService.getVersion();
  }
}
