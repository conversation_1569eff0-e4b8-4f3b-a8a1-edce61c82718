import { IsString, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class OptimizationRecommendationDto {
  @ApiProperty({
    description: '流程ID',
    example: 'process_001',
  })
  @IsString()
  processId: string;

  @ApiProperty({
    description: '性能数据',
    example: {
      efficiency: 0.85,
      throughput: 100,
      errorRate: 0.02,
    },
  })
  @IsObject()
  performanceData: any;
}
