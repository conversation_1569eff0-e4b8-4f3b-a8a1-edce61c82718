import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

/**
 * 应用主控制器
 */
@ApiTags('System')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  /**
   * 健康检查
   */
  @Get('health')
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({ status: 200, description: '服务健康状态' })
  getHealth() {
    return this.appService.getHealth();
  }

  /**
   * 服务信息
   */
  @Get('info')
  @ApiOperation({ summary: '获取服务信息' })
  @ApiResponse({ status: 200, description: '服务详细信息' })
  getServiceInfo() {
    return this.appService.getServiceInfo();
  }

  /**
   * 系统统计
   */
  @Get('stats')
  @ApiOperation({ summary: '获取系统统计信息' })
  @ApiResponse({ status: 200, description: '系统统计数据' })
  async getSystemStats() {
    return await this.appService.getSystemStats();
  }
}
