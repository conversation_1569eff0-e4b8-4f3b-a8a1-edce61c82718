import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { AlertRule, AlertSeverity } from './alert-rule.entity';

export enum AlertInstanceStatus {
  TRIGGERED = 'triggered',
  ACKNOWLEDGED = 'acknowledged',
  RESOLVED = 'resolved',
  SUPPRESSED = 'suppressed'
}

@Entity('alert_instances')
@Index(['ruleId'])
@Index(['status'])
@Index(['triggeredAt'])
@Index(['deviceId', 'tagName'])
export class AlertInstance {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'rule_id', type: 'uuid' })
  ruleId: string;

  @Column({ name: 'device_id', type: 'uuid', nullable: true })
  deviceId: string;

  @Column({ name: 'tag_name', length: 100, nullable: true })
  tagName: string;

  @Column({ length: 200 })
  message: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: AlertSeverity
  })
  severity: AlertSeverity;

  @Column({
    type: 'enum',
    enum: AlertInstanceStatus,
    default: AlertInstanceStatus.TRIGGERED
  })
  status: AlertInstanceStatus;

  @Column({ name: 'trigger_value', type: 'json', nullable: true })
  triggerValue: any;

  @Column({ name: 'threshold_value', type: 'json', nullable: true })
  thresholdValue: any;

  @Column({ name: 'triggered_at', type: 'timestamp' })
  triggeredAt: Date;

  @Column({ name: 'acknowledged_at', type: 'timestamp', nullable: true })
  acknowledgedAt: Date;

  @Column({ name: 'acknowledged_by', length: 100, nullable: true })
  acknowledgedBy: string;

  @Column({ name: 'resolved_at', type: 'timestamp', nullable: true })
  resolvedAt: Date;

  @Column({ name: 'resolved_by', length: 100, nullable: true })
  resolvedBy: string;

  @Column({ name: 'auto_resolved', type: 'boolean', default: false })
  autoResolved: boolean;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @Column({ name: 'notification_sent', type: 'boolean', default: false })
  notificationSent: boolean;

  @Column({ name: 'notification_attempts', type: 'int', default: 0 })
  notificationAttempts: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => AlertRule)
  @JoinColumn({ name: 'rule_id' })
  rule: AlertRule;
}
