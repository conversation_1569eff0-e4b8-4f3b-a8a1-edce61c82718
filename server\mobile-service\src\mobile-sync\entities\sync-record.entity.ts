/**
 * 同步记录实体
 * 
 * 存储移动设备的数据同步记录
 */

import {
  Entity,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('sync_records')
@Index(['userId', 'deviceId'])
@Index(['entityType', 'entityId'])
@Index(['timestamp'])
export class SyncRecord {
  @PrimaryColumn('varchar', { length: 36 })
  id: string;

  @Column('varchar', { length: 36 })
  @Index()
  userId: string;

  @Column('varchar', { length: 100 })
  @Index()
  deviceId: string;

  @Column('varchar', { length: 20 })
  type: string; // 'create' | 'update' | 'delete' | 'move'

  @Column('varchar', { length: 50 })
  entityType: string; // 'node' | 'tree' | 'config'

  @Column('varchar', { length: 100 })
  entityId: string;

  @Column('json')
  data: any;

  @Column('bigint')
  @Index()
  timestamp: number;

  @Column('int', { default: 1 })
  version: number;

  @Column('varchar', { length: 20, default: 'pending' })
  status: string; // 'pending' | 'success' | 'failed'

  @Column('text', { nullable: true })
  errorMessage?: string;

  @Column('int', { nullable: true })
  retryCount?: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
