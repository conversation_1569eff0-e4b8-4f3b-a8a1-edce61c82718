import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { IoAdapter } from '@nestjs/platform-socket.io';
import { AppModule } from './app.module';

/**
 * 人机协作增强服务主启动文件
 * 提供AR/VR维护指导、智能助手、语音交互、手势识别等功能
 */
async function bootstrap() {
  const logger = new Logger('HumanMachineCollaborationService');
  
  try {
    // 创建NestJS应用实例
    const app = await NestFactory.create(AppModule, {
      logger: ['log', 'error', 'warn', 'debug', 'verbose'],
    });

    // 获取配置服务
    const configService = app.get(ConfigService);
    const port = configService.get<number>('PORT', 3005);
    const nodeEnv = configService.get<string>('NODE_ENV', 'development');

    // 设置全局前缀
    app.setGlobalPrefix('api/v1');

    // 启用CORS
    app.enableCors({
      origin: true,
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      credentials: true,
    });

    // 设置全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );

    // 设置WebSocket适配器
    app.useWebSocketAdapter(new IoAdapter(app));

    // 配置Swagger文档（仅在开发环境）
    if (nodeEnv === 'development') {
      const config = new DocumentBuilder()
        .setTitle('人机协作增强服务 API')
        .setDescription(`
          人机协作增强服务提供以下核心功能：
          
          🔧 **AR/VR维护指导**
          - 沉浸式维护指导场景
          - 实时3D可视化指导
          - 智能步骤验证
          - 性能分析和优化建议
          
          🤖 **智能助手**
          - 自然语言理解
          - 智能问答系统
          - 个性化建议
          - 知识库管理
          
          🎤 **语音交互**
          - 语音识别和合成
          - 多语言支持
          - 实时语音指令
          - 语音反馈系统
          
          👋 **手势识别**
          - 实时手势捕捉
          - 动作识别和分析
          - 手势指令映射
          - 多模态交互
        `)
        .setVersion('1.0.0')
        .addTag('AR/VR', 'AR/VR维护指导相关接口')
        .addTag('AI Assistant', '智能助手相关接口')
        .addTag('Voice', '语音交互相关接口')
        .addTag('Gesture', '手势识别相关接口')
        .addTag('Collaboration', '人机协作相关接口')
        .addBearerAuth()
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
          persistAuthorization: true,
          tagsSorter: 'alpha',
          operationsSorter: 'alpha',
        },
      });

      logger.log(`📚 Swagger文档已启用: http://localhost:${port}/api/docs`);
    }

    // 启动应用
    await app.listen(port, '0.0.0.0');

    logger.log(`🚀 人机协作增强服务已启动`);
    logger.log(`🌐 服务地址: http://localhost:${port}`);
    logger.log(`📡 WebSocket地址: ws://localhost:${port}`);
    logger.log(`🔧 环境: ${nodeEnv}`);
    logger.log(`📊 API前缀: /api/v1`);

    // 优雅关闭处理
    process.on('SIGTERM', async () => {
      logger.log('收到SIGTERM信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('收到SIGINT信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

  } catch (error) {
    logger.error('启动人机协作增强服务失败:', error);
    process.exit(1);
  }
}

// 启动应用
bootstrap();
