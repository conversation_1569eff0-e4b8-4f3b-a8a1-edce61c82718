import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { VoiceInteractionService } from '../voice-interaction/voice-interaction.service';

/**
 * 语音交互控制器
 */
@ApiTags('Voice')
@Controller('voice')
export class VoiceController {
  constructor(private readonly voiceInteractionService: VoiceInteractionService) {}

  /**
   * 处理语音输入
   */
  @Post('recognize')
  @ApiOperation({ summary: '语音识别和处理' })
  @ApiResponse({ status: 200, description: '语音处理结果' })
  async processVoice(@Body() voiceData: any) {
    const { audioData, userId, sessionId } = voiceData;
    return await this.voiceInteractionService.processVoiceInput(audioData, userId, sessionId);
  }

  /**
   * 文本转语音
   */
  @Post('synthesize')
  @ApiOperation({ summary: '文本转语音' })
  @ApiResponse({ status: 200, description: '语音合成结果' })
  async textToSpeech(@Body() ttsData: any) {
    const { text, options } = ttsData;
    return await this.voiceInteractionService.textToSpeech(text, options);
  }

  /**
   * 获取支持的语言
   */
  @Get('languages')
  @ApiOperation({ summary: '获取支持的语言列表' })
  @ApiResponse({ status: 200, description: '支持的语言' })
  async getSupportedLanguages() {
    return await this.voiceInteractionService.getSupportedLanguages();
  }

  /**
   * 获取语音指令历史
   */
  @Get('commands/:userId')
  @ApiOperation({ summary: '获取语音指令历史' })
  @ApiResponse({ status: 200, description: '指令历史' })
  async getCommandHistory(
    @Param('userId') userId: string,
    @Query('limit') limit?: number,
  ) {
    return await this.voiceInteractionService.getVoiceCommandHistory(userId, limit);
  }

  /**
   * 获取语音交互统计
   */
  @Get('statistics')
  @ApiOperation({ summary: '获取语音交互统计' })
  @ApiResponse({ status: 200, description: '统计数据' })
  async getStatistics(@Query('userId') userId?: string) {
    return await this.voiceInteractionService.getVoiceInteractionStats(userId);
  }
}
