/**
 * 向量存储控制器
 */
import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Body,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { VectorStoreService } from './vector-store.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { Public } from '../common/decorators/public.decorator';

@ApiTags('vector-store')
@Controller('vector-store')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class VectorStoreController {
  constructor(private readonly vectorStoreService: VectorStoreService) {}

  @Post('collection/:knowledgeBaseId')
  @ApiOperation({ summary: '创建向量集合' })
  @ApiParam({ name: 'knowledgeBaseId', description: '知识库ID' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '集合创建成功',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '创建失败',
  })
  async createCollection(@Param('knowledgeBaseId') knowledgeBaseId: string) {
    await this.vectorStoreService.createCollection(knowledgeBaseId);
    return { message: '向量集合创建成功' };
  }

  @Get('collection/:knowledgeBaseId')
  @ApiOperation({ summary: '获取向量集合信息' })
  @ApiParam({ name: 'knowledgeBaseId', description: '知识库ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        count: { type: 'number' },
        metadata: { type: 'object' },
      },
    },
  })
  async getCollectionInfo(@Param('knowledgeBaseId') knowledgeBaseId: string) {
    return this.vectorStoreService.getCollectionInfo(knowledgeBaseId);
  }

  @Delete('collection/:knowledgeBaseId')
  @ApiOperation({ summary: '删除向量集合' })
  @ApiParam({ name: 'knowledgeBaseId', description: '知识库ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: '删除成功',
  })
  async deleteCollection(@Param('knowledgeBaseId') knowledgeBaseId: string) {
    await this.vectorStoreService.deleteCollection(knowledgeBaseId);
    return { message: '向量集合删除成功' };
  }

  @Get('collection/:knowledgeBaseId/count')
  @ApiOperation({ summary: '获取向量数量' })
  @ApiParam({ name: 'knowledgeBaseId', description: '知识库ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        count: { type: 'number' },
      },
    },
  })
  async getVectorCount(@Param('knowledgeBaseId') knowledgeBaseId: string) {
    const count = await this.vectorStoreService.getVectorCount(knowledgeBaseId);
    return { count };
  }

  @Delete('document/:knowledgeBaseId/:documentId')
  @ApiOperation({ summary: '删除文档向量' })
  @ApiParam({ name: 'knowledgeBaseId', description: '知识库ID' })
  @ApiParam({ name: 'documentId', description: '文档ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: '删除成功',
  })
  async deleteDocumentVectors(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @Param('documentId') documentId: string,
  ) {
    await this.vectorStoreService.deleteDocument(knowledgeBaseId, documentId);
    return { message: '文档向量删除成功' };
  }

  @Post('search/:knowledgeBaseId')
  @ApiOperation({ summary: '向量搜索' })
  @ApiParam({ name: 'knowledgeBaseId', description: '知识库ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '搜索成功',
    schema: {
      type: 'object',
      properties: {
        query: { type: 'string' },
        results: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              content: { type: 'string' },
              score: { type: 'number' },
              metadata: { type: 'object' },
            },
          },
        },
      },
    },
  })
  async searchVectors(
    @Param('knowledgeBaseId') knowledgeBaseId: string,
    @Body() searchDto: { query: string; topK?: number; threshold?: number; filter?: any },
  ) {
    const results = await this.vectorStoreService.search(knowledgeBaseId, searchDto.query, {
      topK: searchDto.topK,
      threshold: searchDto.threshold,
      filter: searchDto.filter,
    });

    return {
      query: searchDto.query,
      results,
    };
  }

  @Get('health')
  @Public()
  @ApiOperation({ summary: '向量存储健康检查' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '健康检查结果',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string' },
        isHealthy: { type: 'boolean' },
        timestamp: { type: 'string' },
      },
    },
  })
  async healthCheck() {
    const isHealthy = await this.vectorStoreService.healthCheck();
    
    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      isHealthy,
      timestamp: new Date().toISOString(),
    };
  }
}
