/**
 * 冲突记录实体
 * 
 * 存储数据同步过程中的冲突信息
 */

import {
  Entity,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('conflict_records')
@Index(['userId', 'deviceId'])
@Index(['entityType', 'entityId'])
@Index(['status'])
export class ConflictRecord {
  @PrimaryColumn('varchar', { length: 36 })
  id: string;

  @Column('varchar', { length: 36 })
  @Index()
  userId: string;

  @Column('varchar', { length: 100 })
  @Index()
  deviceId: string;

  @Column('varchar', { length: 50 })
  entityType: string;

  @Column('varchar', { length: 100 })
  entityId: string;

  @Column('json')
  localData: any;

  @Column('json')
  remoteData: any;

  @Column('int')
  localVersion: number;

  @Column('int')
  remoteVersion: number;

  @Column('varchar', { length: 20, default: 'pending' })
  status: string; // 'pending' | 'resolved' | 'ignored'

  @Column('varchar', { length: 20, nullable: true })
  resolution?: string; // 'accept_local' | 'accept_remote' | 'merge'

  @Column('json', { nullable: true })
  resolvedData?: any;

  @Column('timestamp', { nullable: true })
  resolvedAt?: Date;

  @Column('varchar', { length: 36, nullable: true })
  resolvedBy?: string; // 解决冲突的用户ID

  @Column('text', { nullable: true })
  notes?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
