import { Injectable, Logger } from '@nestjs/common';
import { StorageService } from '../storage/storage.service';

export interface AnalyticsQuery {
  deviceId?: string;
  tagName?: string;
  startTime: Date;
  endTime: Date;
  interval?: number; // 分析间隔（秒）
}

export interface StatisticalResult {
  count: number;
  min: number;
  max: number;
  avg: number;
  sum: number;
  stdDev: number;
  variance: number;
}

export interface TrendAnalysisResult {
  trend: 'increasing' | 'decreasing' | 'stable';
  slope: number;
  correlation: number;
  prediction?: number[];
}

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);

  constructor(private storageService: StorageService) {}

  /**
   * 统计分析
   */
  async performStatisticalAnalysis(query: AnalyticsQuery): Promise<StatisticalResult> {
    try {
      const data = await this.storageService.queryData({
        deviceId: query.deviceId,
        tagName: query.tagName,
        startTime: query.startTime.toISOString(),
        endTime: query.endTime.toISOString(),
        limit: 10000
      });

      if (!data.data || data.data.length === 0) {
        throw new Error('没有找到数据进行分析');
      }

      const values = data.data.map(item => parseFloat(item.value)).filter(v => !isNaN(v));
      
      if (values.length === 0) {
        throw new Error('没有有效的数值数据');
      }

      const count = values.length;
      const sum = values.reduce((a, b) => a + b, 0);
      const avg = sum / count;
      const min = Math.min(...values);
      const max = Math.max(...values);
      
      // 计算标准差和方差
      const variance = values.reduce((acc, val) => acc + Math.pow(val - avg, 2), 0) / count;
      const stdDev = Math.sqrt(variance);

      const result: StatisticalResult = {
        count,
        min,
        max,
        avg: parseFloat(avg.toFixed(4)),
        sum: parseFloat(sum.toFixed(4)),
        stdDev: parseFloat(stdDev.toFixed(4)),
        variance: parseFloat(variance.toFixed(4))
      };

      this.logger.log(`统计分析完成: ${query.deviceId}/${query.tagName}, 数据点: ${count}`);
      return result;

    } catch (error) {
      this.logger.error(`统计分析失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 趋势分析
   */
  async performTrendAnalysis(query: AnalyticsQuery): Promise<TrendAnalysisResult> {
    try {
      const data = await this.storageService.queryData({
        deviceId: query.deviceId,
        tagName: query.tagName,
        startTime: query.startTime.toISOString(),
        endTime: query.endTime.toISOString(),
        sortOrder: 'ASC',
        limit: 1000
      });

      if (!data.data || data.data.length < 2) {
        throw new Error('数据点不足，无法进行趋势分析');
      }

      const points = data.data.map((item, index) => ({
        x: index,
        y: parseFloat(item.value),
        timestamp: new Date(item.timestamp)
      })).filter(p => !isNaN(p.y));

      if (points.length < 2) {
        throw new Error('有效数据点不足');
      }

      // 线性回归分析
      const n = points.length;
      const sumX = points.reduce((sum, p) => sum + p.x, 0);
      const sumY = points.reduce((sum, p) => sum + p.y, 0);
      const sumXY = points.reduce((sum, p) => sum + p.x * p.y, 0);
      const sumXX = points.reduce((sum, p) => sum + p.x * p.x, 0);
      const sumYY = points.reduce((sum, p) => sum + p.y * p.y, 0);

      // 计算斜率和相关系数
      const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
      const correlation = (n * sumXY - sumX * sumY) / 
        Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));

      // 判断趋势
      let trend: 'increasing' | 'decreasing' | 'stable';
      if (Math.abs(slope) < 0.01) {
        trend = 'stable';
      } else if (slope > 0) {
        trend = 'increasing';
      } else {
        trend = 'decreasing';
      }

      // 简单预测（线性外推）
      const prediction = [];
      const lastX = points[points.length - 1].x;
      const intercept = (sumY - slope * sumX) / n;
      
      for (let i = 1; i <= 5; i++) {
        const predictedY = slope * (lastX + i) + intercept;
        prediction.push(parseFloat(predictedY.toFixed(4)));
      }

      const result: TrendAnalysisResult = {
        trend,
        slope: parseFloat(slope.toFixed(6)),
        correlation: parseFloat(correlation.toFixed(4)),
        prediction
      };

      this.logger.log(`趋势分析完成: ${query.deviceId}/${query.tagName}, 趋势: ${trend}`);
      return result;

    } catch (error) {
      this.logger.error(`趋势分析失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 实时分析
   */
  async performRealtimeAnalysis(deviceId: string, tagName: string, windowSize: number = 100): Promise<any> {
    try {
      // 获取最近的数据点
      const endTime = new Date();
      const startTime = new Date(endTime.getTime() - 60 * 60 * 1000); // 最近1小时

      const data = await this.storageService.queryData({
        deviceId,
        tagName,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        sortOrder: 'DESC',
        limit: windowSize
      });

      if (!data.data || data.data.length === 0) {
        return {
          status: 'no_data',
          message: '没有实时数据'
        };
      }

      const values = data.data.map(item => parseFloat(item.value)).filter(v => !isNaN(v));
      
      if (values.length === 0) {
        return {
          status: 'invalid_data',
          message: '没有有效的数值数据'
        };
      }

      // 计算移动平均
      const movingAverage = this.calculateMovingAverage(values, Math.min(10, values.length));
      
      // 检测异常值
      const anomalies = this.detectAnomalies(values);
      
      // 计算变化率
      const changeRate = values.length > 1 ? 
        ((values[0] - values[values.length - 1]) / values[values.length - 1]) * 100 : 0;

      return {
        status: 'success',
        currentValue: values[0],
        movingAverage: movingAverage[movingAverage.length - 1],
        changeRate: parseFloat(changeRate.toFixed(2)),
        anomalies: anomalies.length,
        dataPoints: values.length,
        timestamp: new Date()
      };

    } catch (error) {
      this.logger.error(`实时分析失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 计算移动平均
   */
  private calculateMovingAverage(values: number[], windowSize: number): number[] {
    const result = [];
    for (let i = 0; i < values.length; i++) {
      const start = Math.max(0, i - windowSize + 1);
      const window = values.slice(start, i + 1);
      const avg = window.reduce((sum, val) => sum + val, 0) / window.length;
      result.push(parseFloat(avg.toFixed(4)));
    }
    return result;
  }

  /**
   * 检测异常值（使用Z-score方法）
   */
  private detectAnomalies(values: number[], threshold: number = 2): number[] {
    if (values.length < 3) return [];

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const stdDev = Math.sqrt(
      values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    );

    if (stdDev === 0) return [];

    return values.filter(value => {
      const zScore = Math.abs((value - mean) / stdDev);
      return zScore > threshold;
    });
  }

  /**
   * 设备效率分析
   */
  async analyzeDeviceEfficiency(deviceId: string, startTime: Date, endTime: Date): Promise<any> {
    try {
      // 这里可以根据具体的设备类型和标签来计算效率
      // 示例：计算设备运行时间比例
      
      const statusData = await this.storageService.queryData({
        deviceId,
        tagName: 'status', // 假设有状态标签
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        limit: 10000
      });

      if (!statusData.data || statusData.data.length === 0) {
        return {
          efficiency: 0,
          runningTime: 0,
          totalTime: 0,
          message: '没有状态数据'
        };
      }

      const totalTime = endTime.getTime() - startTime.getTime();
      let runningTime = 0;

      // 简化计算：假设状态为'running'表示运行
      for (let i = 0; i < statusData.data.length - 1; i++) {
        const current = statusData.data[i];
        const next = statusData.data[i + 1];
        
        if (current.value === 'running') {
          const duration = new Date(next.timestamp).getTime() - new Date(current.timestamp).getTime();
          runningTime += duration;
        }
      }

      const efficiency = (runningTime / totalTime) * 100;

      return {
        efficiency: parseFloat(efficiency.toFixed(2)),
        runningTime: Math.round(runningTime / 1000), // 秒
        totalTime: Math.round(totalTime / 1000), // 秒
        dataPoints: statusData.data.length
      };

    } catch (error) {
      this.logger.error(`设备效率分析失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 能耗分析
   */
  async analyzeEnergyConsumption(deviceId: string, startTime: Date, endTime: Date): Promise<any> {
    try {
      const powerData = await this.storageService.queryData({
        deviceId,
        tagName: 'power', // 假设有功率标签
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        limit: 10000
      });

      if (!powerData.data || powerData.data.length === 0) {
        return {
          totalConsumption: 0,
          averagePower: 0,
          peakPower: 0,
          message: '没有功率数据'
        };
      }

      const powerValues = powerData.data.map(item => parseFloat(item.value)).filter(v => !isNaN(v));
      
      if (powerValues.length === 0) {
        return {
          totalConsumption: 0,
          averagePower: 0,
          peakPower: 0,
          message: '没有有效的功率数据'
        };
      }

      const averagePower = powerValues.reduce((sum, val) => sum + val, 0) / powerValues.length;
      const peakPower = Math.max(...powerValues);
      const timeHours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
      const totalConsumption = averagePower * timeHours; // kWh

      return {
        totalConsumption: parseFloat(totalConsumption.toFixed(4)),
        averagePower: parseFloat(averagePower.toFixed(2)),
        peakPower: parseFloat(peakPower.toFixed(2)),
        timeHours: parseFloat(timeHours.toFixed(2)),
        dataPoints: powerValues.length
      };

    } catch (error) {
      this.logger.error(`能耗分析失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
