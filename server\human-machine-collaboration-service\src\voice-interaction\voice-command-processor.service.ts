import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * 语音指令处理器
 * 解析和执行语音指令
 */
@Injectable()
export class VoiceCommandProcessor {
  private readonly logger = new Logger(VoiceCommandProcessor.name);
  private commandPatterns: Map<string, any> = new Map();

  constructor(private readonly configService: ConfigService) {
    this.initializeCommandPatterns();
  }

  /**
   * 初始化指令模式
   */
  private initializeCommandPatterns(): void {
    // 导航指令
    this.commandPatterns.set('navigation', {
      patterns: [
        /^(下一步|下一个|继续|前进)/,
        /^(上一步|上一个|返回|后退)/,
        /^(重新开始|重新来|重置)/,
        /^(跳转到|转到|去到)\s*(.+)/,
        /^(暂停|停止|结束)/,
      ],
      actions: ['next', 'previous', 'restart', 'goto', 'pause'],
    });

    // 控制指令
    this.commandPatterns.set('control', {
      patterns: [
        /^(开始|启动|开启)\s*(.+)?/,
        /^(关闭|停止|结束)\s*(.+)?/,
        /^(调整|设置|修改)\s*(.+)/,
        /^(增加|提高|加大)\s*(.+)/,
        /^(减少|降低|减小)\s*(.+)/,
      ],
      actions: ['start', 'stop', 'adjust', 'increase', 'decrease'],
    });

    // 查询指令
    this.commandPatterns.set('query', {
      patterns: [
        /^(显示|查看|展示)\s*(.+)/,
        /^(什么是|介绍一下|解释)\s*(.+)/,
        /^(如何|怎么|怎样)\s*(.+)/,
        /^(状态|情况|进度)\s*(如何|怎么样)?/,
        /^(帮助|说明|指导)/,
      ],
      actions: ['show', 'explain', 'howto', 'status', 'help'],
    });

    // 反馈指令
    this.commandPatterns.set('feedback', {
      patterns: [
        /^(完成|好了|做完了)/,
        /^(正确|对的|是的)/,
        /^(错误|不对|不是)/,
        /^(重复|再说一遍|再来一次)/,
        /^(跳过|略过|下一个)/,
      ],
      actions: ['complete', 'confirm', 'deny', 'repeat', 'skip'],
    });

    this.logger.log('语音指令模式初始化完成');
  }

  /**
   * 处理语音指令
   * @param text 识别的文本
   * @param userId 用户ID
   * @param sessionId 会话ID
   * @returns 处理结果
   */
  async processCommand(text: string, userId: string, sessionId?: string): Promise<any> {
    try {
      // 文本预处理
      const cleanedText = this.preprocessText(text);

      // 指令分类
      const classification = this.classifyCommand(cleanedText);

      // 参数提取
      const parameters = this.extractParameters(cleanedText, classification);

      // 执行指令
      const result = await this.executeCommand(classification, parameters, userId, sessionId);

      return {
        success: true,
        type: classification.type,
        command: classification.action,
        parameters,
        response: result.response,
        suggestions: result.suggestions,
        nextActions: result.nextActions,
      };

    } catch (error) {
      this.logger.error('处理语音指令失败:', error);
      return {
        success: false,
        error: error.message,
        response: '抱歉，我无法理解您的指令，请重新说一遍。',
        suggestions: ['重新说一遍', '查看帮助', '联系支持'],
      };
    }
  }

  /**
   * 文本预处理
   */
  private preprocessText(text: string): string {
    return text
      .trim()
      .toLowerCase()
      .replace(/[，。！？；：""''（）【】]/g, '') // 移除标点符号
      .replace(/\s+/g, ' '); // 合并空格
  }

  /**
   * 指令分类
   */
  private classifyCommand(text: string): any {
    for (const [type, config] of this.commandPatterns) {
      for (let i = 0; i < config.patterns.length; i++) {
        const pattern = config.patterns[i];
        const match = text.match(pattern);
        
        if (match) {
          return {
            type,
            action: config.actions[i],
            match,
            confidence: this.calculateMatchConfidence(text, pattern),
          };
        }
      }
    }

    // 未匹配到已知模式
    return {
      type: 'unknown',
      action: 'unknown',
      match: null,
      confidence: 0,
    };
  }

  /**
   * 提取参数
   */
  private extractParameters(text: string, classification: any): any {
    const parameters: any = {};

    if (!classification.match) {
      return parameters;
    }

    // 根据指令类型提取特定参数
    switch (classification.type) {
      case 'navigation':
        if (classification.action === 'goto' && classification.match[2]) {
          parameters.target = classification.match[2].trim();
        }
        break;

      case 'control':
        if (classification.match[2]) {
          parameters.target = classification.match[2].trim();
        }
        break;

      case 'query':
        if (classification.match[2]) {
          parameters.subject = classification.match[2].trim();
        }
        break;
    }

    // 提取数值参数
    const numbers = text.match(/\d+/g);
    if (numbers) {
      parameters.numbers = numbers.map(n => parseInt(n));
    }

    // 提取时间参数
    const timePattern = /(\d+)\s*(秒|分钟|小时|天)/g;
    const timeMatches = [...text.matchAll(timePattern)];
    if (timeMatches.length > 0) {
      parameters.duration = timeMatches.map(match => ({
        value: parseInt(match[1]),
        unit: match[2],
      }));
    }

    return parameters;
  }

  /**
   * 执行指令
   */
  private async executeCommand(
    classification: any,
    parameters: any,
    userId: string,
    sessionId?: string,
  ): Promise<any> {
    const { type, action } = classification;

    switch (type) {
      case 'navigation':
        return this.executeNavigationCommand(action, parameters, userId, sessionId);
      
      case 'control':
        return this.executeControlCommand(action, parameters, userId, sessionId);
      
      case 'query':
        return this.executeQueryCommand(action, parameters, userId, sessionId);
      
      case 'feedback':
        return this.executeFeedbackCommand(action, parameters, userId, sessionId);
      
      default:
        return this.executeUnknownCommand(classification, parameters, userId, sessionId);
    }
  }

  /**
   * 执行导航指令
   */
  private async executeNavigationCommand(action: string, parameters: any, userId: string, sessionId?: string): Promise<any> {
    switch (action) {
      case 'next':
        return {
          response: '正在进入下一步...',
          suggestions: ['暂停', '重复说明', '跳过'],
          nextActions: ['step_forward'],
        };

      case 'previous':
        return {
          response: '正在返回上一步...',
          suggestions: ['继续', '重新开始', '帮助'],
          nextActions: ['step_backward'],
        };

      case 'restart':
        return {
          response: '正在重新开始...',
          suggestions: ['确认重新开始', '取消', '保存进度'],
          nextActions: ['restart_session'],
        };

      case 'goto':
        const target = parameters.target || '未指定位置';
        return {
          response: `正在跳转到${target}...`,
          suggestions: ['确认跳转', '取消', '查看目录'],
          nextActions: ['navigate_to', target],
        };

      case 'pause':
        return {
          response: '已暂停当前操作',
          suggestions: ['继续', '结束', '保存'],
          nextActions: ['pause_session'],
        };

      default:
        return {
          response: '导航指令执行完成',
          suggestions: [],
          nextActions: [],
        };
    }
  }

  /**
   * 执行控制指令
   */
  private async executeControlCommand(action: string, parameters: any, userId: string, sessionId?: string): Promise<any> {
    const target = parameters.target || '系统';

    switch (action) {
      case 'start':
        return {
          response: `正在启动${target}...`,
          suggestions: ['查看状态', '停止', '调整设置'],
          nextActions: ['start_operation', target],
        };

      case 'stop':
        return {
          response: `正在停止${target}...`,
          suggestions: ['确认停止', '取消', '保存状态'],
          nextActions: ['stop_operation', target],
        };

      case 'adjust':
        return {
          response: `正在调整${target}设置...`,
          suggestions: ['确认调整', '取消', '查看当前设置'],
          nextActions: ['adjust_settings', target],
        };

      case 'increase':
        return {
          response: `正在增加${target}...`,
          suggestions: ['继续增加', '停止', '查看当前值'],
          nextActions: ['increase_value', target],
        };

      case 'decrease':
        return {
          response: `正在减少${target}...`,
          suggestions: ['继续减少', '停止', '查看当前值'],
          nextActions: ['decrease_value', target],
        };

      default:
        return {
          response: '控制指令执行完成',
          suggestions: [],
          nextActions: [],
        };
    }
  }

  /**
   * 执行查询指令
   */
  private async executeQueryCommand(action: string, parameters: any, userId: string, sessionId?: string): Promise<any> {
    const subject = parameters.subject || '信息';

    switch (action) {
      case 'show':
        return {
          response: `正在显示${subject}信息...`,
          suggestions: ['详细信息', '关闭', '导出'],
          nextActions: ['show_information', subject],
        };

      case 'explain':
        return {
          response: `${subject}是一个重要的概念，让我为您详细解释...`,
          suggestions: ['更多详情', '示例', '相关主题'],
          nextActions: ['explain_concept', subject],
        };

      case 'howto':
        return {
          response: `关于如何${subject}，我来为您介绍具体步骤...`,
          suggestions: ['开始操作', '查看视频', '练习模式'],
          nextActions: ['show_tutorial', subject],
        };

      case 'status':
        return {
          response: '当前系统状态正常，所有功能运行良好',
          suggestions: ['详细状态', '历史记录', '性能报告'],
          nextActions: ['show_status'],
        };

      case 'help':
        return {
          response: '我可以帮助您进行设备操作、维护指导、故障排除等。请告诉我您需要什么帮助。',
          suggestions: ['操作指导', '维护帮助', '故障排除'],
          nextActions: ['show_help_menu'],
        };

      default:
        return {
          response: '查询指令执行完成',
          suggestions: [],
          nextActions: [],
        };
    }
  }

  /**
   * 执行反馈指令
   */
  private async executeFeedbackCommand(action: string, parameters: any, userId: string, sessionId?: string): Promise<any> {
    switch (action) {
      case 'complete':
        return {
          response: '很好！您已完成当前步骤，准备进入下一步',
          suggestions: ['下一步', '重复练习', '结束'],
          nextActions: ['mark_complete', 'next_step'],
        };

      case 'confirm':
        return {
          response: '确认收到，继续当前操作',
          suggestions: ['继续', '修改', '取消'],
          nextActions: ['confirm_action'],
        };

      case 'deny':
        return {
          response: '了解，让我重新为您说明',
          suggestions: ['重新说明', '换个方式', '跳过'],
          nextActions: ['retry_explanation'],
        };

      case 'repeat':
        return {
          response: '好的，我重复一遍刚才的说明...',
          suggestions: ['明白了', '更详细', '跳过'],
          nextActions: ['repeat_instruction'],
        };

      case 'skip':
        return {
          response: '好的，跳过当前步骤，进入下一个',
          suggestions: ['继续', '返回', '结束'],
          nextActions: ['skip_step', 'next_step'],
        };

      default:
        return {
          response: '反馈已收到',
          suggestions: [],
          nextActions: [],
        };
    }
  }

  /**
   * 执行未知指令
   */
  private async executeUnknownCommand(classification: any, parameters: any, userId: string, sessionId?: string): Promise<any> {
    return {
      response: '抱歉，我没有理解您的指令。您可以说"帮助"来查看可用的指令。',
      suggestions: [
        '帮助',
        '下一步',
        '显示状态',
        '开始操作',
      ],
      nextActions: ['show_help'],
    };
  }

  /**
   * 计算匹配置信度
   */
  private calculateMatchConfidence(text: string, pattern: RegExp): number {
    const match = text.match(pattern);
    if (!match) return 0;

    // 基于匹配长度和文本长度计算置信度
    const matchLength = match[0].length;
    const textLength = text.length;
    const coverage = matchLength / textLength;

    // 基础置信度
    let confidence = 0.7 + coverage * 0.3;

    // 完全匹配加分
    if (matchLength === textLength) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * 获取可用指令列表
   */
  async getAvailableCommands(): Promise<any> {
    const commands: any = {};

    for (const [type, config] of this.commandPatterns) {
      commands[type] = {
        actions: config.actions,
        examples: this.getCommandExamples(type),
      };
    }

    return commands;
  }

  /**
   * 获取指令示例
   */
  private getCommandExamples(type: string): string[] {
    const examples: { [key: string]: string[] } = {
      navigation: ['下一步', '上一步', '重新开始', '跳转到第三步', '暂停'],
      control: ['开始维护', '停止操作', '调整速度', '增加亮度', '减少音量'],
      query: ['显示状态', '什么是CNC', '如何操作', '当前进度', '帮助'],
      feedback: ['完成了', '正确', '不对', '重复一遍', '跳过'],
    };

    return examples[type] || [];
  }
}
