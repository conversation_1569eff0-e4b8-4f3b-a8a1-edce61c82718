import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * 协作会话实体
 * 记录多用户协作会话信息
 */
@Entity('collaboration_sessions')
@Index(['sessionType', 'status'])
@Index(['createdAt', 'endTime'])
export class CollaborationSession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100 })
  sessionName: string;

  @Column({
    type: 'enum',
    enum: ['training', 'maintenance', 'troubleshooting', 'inspection', 'meeting'],
    default: 'training',
  })
  sessionType: string;

  @Column({ type: 'json' })
  participants: {
    userId: string;
    role: string;
    joinTime: Date;
    leaveTime?: Date;
    status: string;
  }[];

  @Column({ type: 'varchar', length: 50 })
  hostUserId: string;

  @Column({ type: 'uuid', nullable: true })
  arvrSceneId: string;

  @Column({ type: 'datetime' })
  startTime: Date;

  @Column({ type: 'datetime', nullable: true })
  endTime: Date;

  @Column({
    type: 'enum',
    enum: ['waiting', 'active', 'paused', 'completed', 'cancelled'],
    default: 'waiting',
  })
  status: string;

  @Column({ type: 'json', nullable: true })
  sharedData: any;

  @Column({ type: 'json', nullable: true })
  collaborationMetrics: {
    totalInteractions: number;
    averageResponseTime: number;
    participationRate: number;
    effectivenessScore: number;
  };

  @Column({ type: 'json', nullable: true })
  communicationLogs: {
    timestamp: Date;
    userId: string;
    type: string;
    content: any;
  }[];

  @Column({ type: 'json', nullable: true })
  settings: {
    maxParticipants: number;
    allowVoice: boolean;
    allowGesture: boolean;
    recordSession: boolean;
    shareScreen: boolean;
  };

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
