import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

/**
 * 语音指令实体
 * 记录语音识别和处理的结果
 */
@Entity('voice_commands')
@Index(['userId', 'createdAt'])
@Index(['commandType', 'success'])
export class VoiceCommand {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 50 })
  userId: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  sessionId: string;

  @Column({ type: 'text' })
  originalAudio: string; // 音频文件路径或Base64

  @Column({ type: 'text' })
  recognizedText: string;

  @Column({ type: 'float', comment: '识别置信度' })
  recognitionConfidence: number;

  @Column({
    type: 'enum',
    enum: ['navigation', 'control', 'query', 'command', 'feedback'],
    default: 'command',
  })
  commandType: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  extractedCommand: string;

  @Column({ type: 'json', nullable: true })
  parameters: any;

  @Column({ type: 'boolean' })
  success: boolean;

  @Column({ type: 'text', nullable: true })
  response: string;

  @Column({ type: 'int', comment: '处理时间（毫秒）' })
  processingTime: number;

  @Column({ type: 'varchar', length: 20, default: 'zh-CN' })
  language: string;

  @Column({ type: 'json', nullable: true })
  audioMetadata: {
    duration: number;
    sampleRate: number;
    channels: number;
    format: string;
  };

  @Column({ type: 'json', nullable: true })
  errorInfo: any;

  @Column({ type: 'json', nullable: true })
  contextData: any;

  @CreateDateColumn()
  createdAt: Date;
}
