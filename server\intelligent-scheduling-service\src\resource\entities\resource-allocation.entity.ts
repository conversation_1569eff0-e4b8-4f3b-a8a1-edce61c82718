import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Resource } from './resource.entity';

/**
 * 分配状态枚举
 */
export enum AllocationStatus {
  PLANNED = 'planned',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

/**
 * 资源分配实体
 */
@Entity('resource_allocations')
@Index(['resourceId', 'startTime', 'endTime'])
@Index(['taskId'])
@Index(['status'])
export class ResourceAllocation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'resource_id', type: 'uuid' })
  resourceId: string;

  @Column({ name: 'task_id', length: 100 })
  taskId: string;

  @Column({ name: 'solution_id', type: 'uuid', nullable: true })
  solutionId: string;

  @Column({
    type: 'enum',
    enum: AllocationStatus,
    default: AllocationStatus.PLANNED,
  })
  status: AllocationStatus;

  @Column({ name: 'start_time', type: 'datetime' })
  startTime: Date;

  @Column({ name: 'end_time', type: 'datetime' })
  endTime: Date;

  @Column({ name: 'allocated_quantity', type: 'decimal', precision: 10, scale: 2 })
  allocatedQuantity: number;

  @Column({ name: 'actual_quantity', type: 'decimal', precision: 10, scale: 2, nullable: true })
  actualQuantity: number;

  @Column({ name: 'allocation_cost', type: 'decimal', precision: 10, scale: 2, nullable: true })
  allocationCost: number;

  @Column({ name: 'setup_time', type: 'int', default: 0, comment: '准备时间(分钟)' })
  setupTime: number;

  @Column({ name: 'teardown_time', type: 'int', default: 0, comment: '清理时间(分钟)' })
  teardownTime: number;

  @Column({ name: 'efficiency_rate', type: 'decimal', precision: 5, scale: 2, default: 1.0, comment: '效率比率' })
  efficiencyRate: number;

  @Column({ name: 'conflict_level', type: 'int', default: 0, comment: '冲突级别' })
  conflictLevel: number;

  @Column({ name: 'priority_score', type: 'decimal', precision: 5, scale: 2, default: 0, comment: '优先级评分' })
  priorityScore: number;

  @Column({ type: 'json', nullable: true, comment: '分配约束' })
  constraints: any;

  @Column({ type: 'json', nullable: true, comment: '扩展属性' })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => Resource, resource => resource.allocations)
  @JoinColumn({ name: 'resource_id' })
  resource: Resource;
}
