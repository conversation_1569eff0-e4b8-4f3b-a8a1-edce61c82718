import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * AI对话实体
 * 存储用户与智能助手的对话记录
 */
@Entity('ai_conversations')
@Index(['userId', 'createdAt'])
@Index(['conversationType', 'status'])
export class AIConversation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 50 })
  userId: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  sessionId: string;

  @Column({
    type: 'enum',
    enum: ['general', 'maintenance', 'troubleshooting', 'training', 'support'],
    default: 'general',
  })
  conversationType: string;

  @Column({ type: 'text' })
  userMessage: string;

  @Column({ type: 'text' })
  aiResponse: string;

  @Column({ type: 'json', nullable: true })
  context: any;

  @Column({ type: 'json', nullable: true })
  intent: {
    name: string;
    confidence: number;
    entities: any[];
  };

  @Column({ type: 'float', nullable: true, comment: '响应置信度' })
  confidence: number;

  @Column({ type: 'int', comment: '响应时间（毫秒）' })
  responseTime: number;

  @Column({
    type: 'enum',
    enum: ['pending', 'completed', 'failed'],
    default: 'completed',
  })
  status: string;

  @Column({ type: 'json', nullable: true })
  feedback: {
    rating: number;
    helpful: boolean;
    comment: string;
  };

  @Column({ type: 'varchar', length: 20, nullable: true })
  language: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
