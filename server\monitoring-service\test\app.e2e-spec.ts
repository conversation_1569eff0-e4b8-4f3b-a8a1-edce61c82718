import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('监控服务 E2E 测试', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('基础功能测试', () => {
    it('/ (GET) - 应用根路径', () => {
      return request(app.getHttpServer())
        .get('/')
        .expect(200)
        .expect('Hello World!');
    });

    it('/api/v1/health (GET) - 健康检查', () => {
      return request(app.getHttpServer())
        .get('/api/v1/health')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status');
          expect(res.body.status).toBe('ok');
        });
    });
  });

  describe('监控接口测试', () => {
    it('/api/v1/monitoring/metrics (GET) - 获取系统指标', () => {
      return request(app.getHttpServer())
        .get('/api/v1/monitoring/metrics')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('data');
          expect(Array.isArray(res.body.data)).toBe(true);
        });
    });

    it('/api/v1/monitoring/services (GET) - 获取服务状态', () => {
      return request(app.getHttpServer())
        .get('/api/v1/monitoring/services')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('data');
          expect(Array.isArray(res.body.data)).toBe(true);
        });
    });
  });

  describe('告警接口测试', () => {
    it('/api/v1/alerts (GET) - 获取告警列表', () => {
      return request(app.getHttpServer())
        .get('/api/v1/alerts')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('data');
          expect(Array.isArray(res.body.data)).toBe(true);
        });
    });

    it('/api/v1/alerts/rules (POST) - 创建告警规则', () => {
      const alertRule = {
        name: '测试告警规则',
        description: '测试用的告警规则',
        condition: 'cpu_usage > 80',
        severity: 'warning',
        duration: '5m',
        enabled: true,
      };

      return request(app.getHttpServer())
        .post('/api/v1/alerts/rules')
        .send(alertRule)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('data');
          expect(res.body.data).toHaveProperty('id');
          expect(res.body.data.name).toBe(alertRule.name);
        });
    });
  });

  describe('日志接口测试', () => {
    it('/api/v1/logs (GET) - 查询日志', () => {
      return request(app.getHttpServer())
        .get('/api/v1/logs')
        .query({
          page: 1,
          limit: 10,
        })
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('data');
          expect(res.body).toHaveProperty('pagination');
        });
    });

    it('/api/v1/logs/search (POST) - 搜索日志', () => {
      const searchQuery = {
        query: 'error',
        startTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        endTime: new Date().toISOString(),
        level: 'error',
      };

      return request(app.getHttpServer())
        .post('/api/v1/logs/search')
        .send(searchQuery)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('data');
          expect(Array.isArray(res.body.data)).toBe(true);
        });
    });
  });

  describe('通知接口测试', () => {
    it('/api/v1/notifications (GET) - 获取通知历史', () => {
      return request(app.getHttpServer())
        .get('/api/v1/notifications')
        .query({
          page: 1,
          limit: 10,
        })
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('data');
          expect(res.body).toHaveProperty('pagination');
        });
    });

    it('/api/v1/notifications/test (POST) - 测试通知配置', () => {
      const testNotification = {
        channel: 'email',
        recipient: '<EMAIL>',
        message: '这是一条测试通知',
      };

      return request(app.getHttpServer())
        .post('/api/v1/notifications/test')
        .send(testNotification)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('success');
          expect(res.body.success).toBe(true);
        });
    });
  });
});
