import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * 节点类型枚举
 */
export enum NodeType {
  SUPPLIER = 'supplier',
  MANUFACTURER = 'manufacturer',
  DISTRIBUTOR = 'distributor',
  RETAILER = 'retailer',
  CUSTOMER = 'customer',
  WAREHOUSE = 'warehouse',
  LOGISTICS_HUB = 'logistics_hub',
}

/**
 * 节点状态枚举
 */
export enum NodeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DISRUPTED = 'disrupted',
  MAINTENANCE = 'maintenance',
}

/**
 * 供应链节点实体
 */
@Entity('supply_chain_nodes')
@Index(['type', 'status'])
@Index(['location'])
export class SupplyChainNode {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'node_id', length: 100, unique: true })
  nodeId: string;

  @Column({ length: 200 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: NodeType,
  })
  type: NodeType;

  @Column({
    type: 'enum',
    enum: NodeStatus,
    default: NodeStatus.ACTIVE,
  })
  status: NodeStatus;

  @Column({ length: 200, nullable: true })
  location: string;

  @Column({ type: 'decimal', precision: 10, scale: 6, nullable: true, comment: '纬度' })
  latitude: number;

  @Column({ type: 'decimal', precision: 10, scale: 6, nullable: true, comment: '经度' })
  longitude: number;

  @Column({ type: 'json', nullable: true, comment: '处理能力' })
  capacity: {
    category: string;
    maxCapacity: number;
    currentCapacity: number;
    unit: string;
    utilizationRate: number;
  }[];

  @Column({ type: 'json', nullable: true, comment: '连接的节点' })
  connections: {
    nodeId: string;
    connectionType: string;
    distance: number;
    transportTime: number;
    transportCost: number;
    reliability: number;
  }[];

  @Column({ type: 'json', nullable: true, comment: '库存信息' })
  inventory: {
    itemId: string;
    itemName: string;
    currentStock: number;
    maxStock: number;
    minStock: number;
    unit: string;
    lastUpdated: Date;
  }[];

  @Column({ name: 'operating_hours', type: 'json', nullable: true, comment: '运营时间' })
  operatingHours: {
    day: string;
    startTime: string;
    endTime: string;
    isOperating: boolean;
  }[];

  @Column({ name: 'performance_metrics', type: 'json', nullable: true, comment: '性能指标' })
  performanceMetrics: {
    metricName: string;
    value: number;
    unit: string;
    target: number;
    trend: string;
    lastUpdated: Date;
  }[];

  @Column({ name: 'risk_factors', type: 'json', nullable: true, comment: '风险因素' })
  riskFactors: {
    riskType: string;
    probability: number;
    impact: number;
    riskScore: number;
    mitigation: string;
  }[];

  @Column({ name: 'compliance_status', type: 'json', nullable: true, comment: '合规状态' })
  complianceStatus: {
    regulation: string;
    status: string;
    lastAudit: Date;
    nextAudit: Date;
    issues: string[];
  }[];

  @Column({ type: 'json', nullable: true, comment: '扩展属性' })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
