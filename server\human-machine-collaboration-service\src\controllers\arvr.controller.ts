import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { ARVRGuidanceService } from '../collaboration/ar-vr-guidance.service';

/**
 * AR/VR控制器
 * 提供AR/VR维护指导相关的API端点
 */
@ApiTags('AR/VR')
@Controller('arvr')
export class ARVRController {
  constructor(private readonly arvrGuidanceService: ARVRGuidanceService) {}

  /**
   * 创建AR/VR场景
   */
  @Post('scenes')
  @ApiOperation({ summary: '创建AR/VR维护指导场景' })
  @ApiResponse({ status: 201, description: '场景创建成功' })
  @ApiBody({
    description: '场景配置',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', description: '场景名称' },
        contentType: { type: 'string', description: '内容类型' },
        equipmentModel: { type: 'string', description: '设备型号' },
        description: { type: 'string', description: '场景描述' },
        difficulty: { type: 'string', enum: ['beginner', 'intermediate', 'advanced'] },
        estimatedDuration: { type: 'number', description: '预计时长（分钟）' },
      },
    },
  })
  async createScene(@Body() sceneConfig: any) {
    return await this.arvrGuidanceService.createMaintenanceScene(sceneConfig);
  }

  /**
   * 启动AR/VR指导会话
   */
  @Post('sessions')
  @ApiOperation({ summary: '启动AR/VR指导会话' })
  @ApiResponse({ status: 201, description: '会话启动成功' })
  @ApiBody({
    description: '会话配置',
    schema: {
      type: 'object',
      properties: {
        userId: { type: 'string', description: '用户ID' },
        sceneId: { type: 'string', description: '场景ID' },
        deviceInfo: { type: 'object', description: '设备信息' },
      },
      required: ['userId', 'sceneId'],
    },
  })
  async startSession(@Body() sessionConfig: any) {
    const { userId, sceneId, deviceInfo } = sessionConfig;
    return await this.arvrGuidanceService.startGuidanceSession(userId, sceneId, deviceInfo);
  }

  /**
   * 处理用户交互
   */
  @Post('sessions/:sessionId/interactions')
  @ApiOperation({ summary: '处理用户交互' })
  @ApiResponse({ status: 200, description: '交互处理成功' })
  async handleInteraction(
    @Param('sessionId') sessionId: string,
    @Body() interaction: any,
  ) {
    return await this.arvrGuidanceService.handleUserInteraction(sessionId, interaction);
  }

  /**
   * 获取智能提示
   */
  @Get('sessions/:sessionId/hints')
  @ApiOperation({ summary: '获取智能提示' })
  @ApiResponse({ status: 200, description: '智能提示' })
  async getHints(@Param('sessionId') sessionId: string) {
    return await this.arvrGuidanceService.getIntelligentHints(sessionId);
  }

  /**
   * 完成指导会话
   */
  @Post('sessions/:sessionId/complete')
  @ApiOperation({ summary: '完成指导会话' })
  @ApiResponse({ status: 200, description: '会话完成' })
  async completeSession(@Param('sessionId') sessionId: string) {
    return await this.arvrGuidanceService.completeGuidanceSession(sessionId);
  }

  /**
   * 获取会话统计
   */
  @Get('statistics')
  @ApiOperation({ summary: '获取会话统计' })
  @ApiResponse({ status: 200, description: '统计数据' })
  async getStatistics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const timeRange = startDate && endDate ? {
      start: new Date(startDate),
      end: new Date(endDate),
    } : undefined;

    return await this.arvrGuidanceService.getSessionStatistics(timeRange);
  }
}
