# 编译输出
/dist
/build
*.tsbuildinfo

# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov

# nyc测试覆盖率
.nyc_output

# 依赖锁定文件（可选）
package-lock.json
yarn.lock

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 上传文件
uploads/
temp/

# 数据库文件
*.sqlite
*.db

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# 测试
test-results/
coverage/

# 临时文件
tmp/
temp/
