/**
 * 应用控制器测试
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';

describe('AppController', () => {
  let appController: AppController;
  let appService: AppService;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [
        AppService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              const config = {
                NODE_ENV: 'test',
                PORT: 3030,
              };
              return config[key] || defaultValue;
            }),
          },
        },
      ],
    }).compile();

    appController = app.get<AppController>(AppController);
    appService = app.get<AppService>(AppService);
  });

  describe('getServiceInfo', () => {
    it('应该返回服务信息', () => {
      const result = appController.getServiceInfo();
      
      expect(result).toBeDefined();
      expect(result.name).toBe('学习记录跟踪服务');
      expect(result.version).toBe('1.0.0');
      expect(result.features).toBeInstanceOf(Array);
      expect(result.endpoints).toBeDefined();
    });
  });

  describe('getVersion', () => {
    it('应该返回版本信息', () => {
      const result = appController.getVersion();
      
      expect(result).toBeDefined();
      expect(result.version).toBe('1.0.0');
      expect(result.nodeVersion).toBeDefined();
      expect(result.platform).toBeDefined();
      expect(result.arch).toBeDefined();
    });
  });
});
