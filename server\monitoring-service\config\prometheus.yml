# Prometheus 配置文件
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 监控服务
  - job_name: 'monitoring-service'
    static_configs:
      - targets: ['monitoring-service:3003']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 用户服务
  - job_name: 'user-service'
    static_configs:
      - targets: ['user-service:3001']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 生态系统服务
  - job_name: 'ecosystem-service'
    static_configs:
      - targets: ['ecosystem-service:3002']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 情感服务
  - job_name: 'emotion-service'
    static_configs:
      - targets: ['emotion-service:3004']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # MES服务
  - job_name: 'mes-service'
    static_configs:
      - targets: ['mes-service:3005']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 移动服务
  - job_name: 'mobile-service'
    static_configs:
      - targets: ['mobile-service:3006']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Node Exporter (系统指标)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # MySQL Exporter
  - job_name: 'mysql-exporter'
    static_configs:
      - targets: ['mysql-exporter:9104']
    scrape_interval: 30s

  # Redis Exporter
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Elasticsearch Exporter
  - job_name: 'elasticsearch-exporter'
    static_configs:
      - targets: ['elasticsearch-exporter:9114']
    scrape_interval: 30s
