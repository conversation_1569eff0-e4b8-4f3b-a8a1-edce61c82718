/**
 * 健康检查服务
 * 
 * 提供详细的健康状态检查功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import * as os from 'os';
import * as process from 'process';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);
  private readonly startTime = Date.now();

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 获取详细健康信息
   */
  async getDetailedHealthInfo() {
    const timestamp = new Date().toISOString();
    
    try {
      const [
        systemInfo,
        databaseInfo,
        serviceMetrics,
        resourceUsage,
      ] = await Promise.all([
        this.getSystemInfo(),
        this.getDatabaseInfo(),
        this.getServiceMetrics(),
        this.getResourceUsage(),
      ]);

      return {
        status: 'healthy',
        timestamp,
        service: {
          name: '移动端服务',
          version: '1.0.0',
          environment: this.configService.get('NODE_ENV'),
          uptime: Date.now() - this.startTime,
        },
        system: systemInfo,
        database: databaseInfo,
        metrics: serviceMetrics,
        resources: resourceUsage,
      };
    } catch (error) {
      this.logger.error('获取健康信息失败:', error);
      return {
        status: 'unhealthy',
        timestamp,
        error: error.message,
      };
    }
  }

  /**
   * 获取服务状态
   */
  async getServiceStatus() {
    const uptime = Date.now() - this.startTime;
    
    return {
      status: 'running',
      uptime,
      uptimeFormatted: this.formatUptime(uptime),
      timestamp: new Date().toISOString(),
      pid: process.pid,
      version: process.version,
      platform: process.platform,
      arch: process.arch,
    };
  }

  /**
   * 检查依赖服务
   */
  async checkDependencies() {
    const dependencies = [];

    // 检查数据库连接
    try {
      await this.dataSource.query('SELECT 1');
      dependencies.push({
        name: 'MySQL数据库',
        status: 'healthy',
        responseTime: 0, // 简化实现
      });
    } catch (error) {
      dependencies.push({
        name: 'MySQL数据库',
        status: 'unhealthy',
        error: error.message,
      });
    }

    // 检查Redis连接（如果配置了）
    const redisHost = this.configService.get('REDIS_HOST');
    if (redisHost) {
      dependencies.push({
        name: 'Redis缓存',
        status: 'not_implemented',
        message: '待实现Redis健康检查',
      });
    }

    return {
      status: dependencies.every(dep => dep.status === 'healthy') ? 'healthy' : 'degraded',
      dependencies,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取系统信息
   */
  private getSystemInfo() {
    return {
      hostname: os.hostname(),
      platform: os.platform(),
      arch: os.arch(),
      release: os.release(),
      uptime: os.uptime(),
      loadavg: os.loadavg(),
      totalmem: os.totalmem(),
      freemem: os.freemem(),
      cpus: os.cpus().length,
    };
  }

  /**
   * 获取数据库信息
   */
  private async getDatabaseInfo() {
    try {
      const result = await this.dataSource.query('SELECT VERSION() as version');
      const isConnected = this.dataSource.isInitialized;
      
      return {
        connected: isConnected,
        version: result[0]?.version || 'unknown',
        driver: 'mysql2',
      };
    } catch (error) {
      return {
        connected: false,
        error: error.message,
      };
    }
  }

  /**
   * 获取服务指标
   */
  private async getServiceMetrics() {
    // 这里可以添加更多的业务指标
    return {
      activeConnections: 0, // 待实现
      totalRequests: 0, // 待实现
      errorRate: 0, // 待实现
      avgResponseTime: 0, // 待实现
    };
  }

  /**
   * 获取资源使用情况
   */
  private getResourceUsage() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      memory: {
        rss: memUsage.rss,
        heapTotal: memUsage.heapTotal,
        heapUsed: memUsage.heapUsed,
        external: memUsage.external,
        arrayBuffers: memUsage.arrayBuffers,
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      uptime: process.uptime(),
    };
  }

  /**
   * 格式化运行时间
   */
  private formatUptime(uptime: number): string {
    const seconds = Math.floor(uptime / 1000);
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (days > 0) {
      return `${days}天 ${hours}小时 ${minutes}分钟 ${secs}秒`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes}分钟 ${secs}秒`;
    } else if (minutes > 0) {
      return `${minutes}分钟 ${secs}秒`;
    } else {
      return `${secs}秒`;
    }
  }
}
